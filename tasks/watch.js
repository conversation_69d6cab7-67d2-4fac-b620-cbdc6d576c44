/* eslint-disable no-console */
import childProcess from 'child_process';
import fs from 'fs-extra';
import path from 'path';

const knownErrors = {
    ENOENT: 'no such file or directory'
};

const resolveSymlink = async linkPath => {
    try {
        const stats = await fs.lstat(linkPath);
        if (stats.isSymbolicLink()) {
            return await fs.realpath(linkPath);
        }
        return linkPath;
    } catch (error) {
        console.error(`Error resolving symlink at ${linkPath}:`, error);
        throw error;
    }
};

(async () => {
    try {
        console.log('Change detected! Starting BFUI build...');

        // Check if the output directory actually exists first.
        try {
            fs.statSync(process.env.OUTPUT_DIRECTORY);
        } catch {
            throw new Error(
                `The directory ${process.env.OUTPUT_DIRECTORY} does not exist. Have you pointed the variable OUTPUT_DIRECTORY in nodemon.json to your project?`
            );
        }

        childProcess
            .exec('npm run build-dev', async error => {
                if (error !== null) {
                    throw error;
                }

                const sourceDirectory = process.env.SOURCE_DIRECTORY;
                const targetDirectory = path.join(
                    process.env.OUTPUT_DIRECTORY,
                    'node_modules/@bannerflow/ui'
                );

                const resolvedTargetDirectory = await resolveSymlink(targetDirectory);

                console.log(
                    `Starting migration of BF-UI from ${process.env.SOURCE_DIRECTORY} to ${resolvedTargetDirectory}...`
                );

                console.log(`Copying ${sourceDirectory} to ${resolvedTargetDirectory}`);

                fs.copy(sourceDirectory, resolvedTargetDirectory, err => {
                    if (err) {
                        throw err;
                    }

                    /**
                     * TARGET_APP_DIRECTORY has to be defined if the
                     * angular application is not in the root folder,
                     * i.e the target app is using workspace (or likewise).
                     * The cache will otherwise not be purged and updates won't
                     * be reflected on the target
                     */
                    const appPath = process.env.TARGET_APP_DIRECTORY ?? process.env.OUTPUT_DIRECTORY;

                    const consumerProjectAngularCache = path.join(appPath, '.angular');
                    fs.rmdir(consumerProjectAngularCache, { recursive: true }, rmdirErr => {
                        const isKnownError =
                            knownErrors[rmdirErr?.code] === 'no such file or directory';

                        if (rmdirErr && isKnownError) {
                            console.warn('Did not find any .angular/ cache to purge. OK.');
                        } else if (rmdirErr) {
                            throw rmdirErr;
                        } else {
                            console.log('.angular/ cache has been purged');
                        }

                        console.log(
                            `Migration of BF-UI from ${sourceDirectory} to ${resolvedTargetDirectory} successful!`
                        );
                        console.log('BF-UI build successful!');
                    });
                });
            })
            .stdout.pipe(process.stdout); // This line makes sure we have verbose loggin on the build process.
    } catch (error) {
        console.error(error);
    }
})();
