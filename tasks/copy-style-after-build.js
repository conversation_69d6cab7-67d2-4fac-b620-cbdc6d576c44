/* eslint-disable no-console */
import fs from 'fs-extra';

console.log('# Copying styles');
(async () => {
    try {
        const outputFolder = './dist/style';
        // Copy all style documents when
        fs.copy('./projects/ui-lib/src/style', outputFolder, err => {
            if (err) {
                throw err;
            }
            console.log('Copy of folder success!');

            // Copy files individually
            const filesToCopy = [
                {
                    sourceFile: './projects/ui-lib/src/export.scss',
                    destination: 'index.scss'
                }
            ];
            const singleFileOutput = './dist';

            filesToCopy.forEach(file => {
                fs.copyFile(file.sourceFile, `${singleFileOutput}/${file.destination}`, copyFileErr => {
                    if (copyFileErr) {
                        throw copyFileErr;
                    }
                    console.log(`Copy of ${file.sourceFile} success`);
                });
            });
        });
    } catch (error) {
        console.error(error);
    }
})();
