import fs from 'fs';
import path from 'path';
import { SVG_ICONS_HTML_FILE, SVG_ICONS_TS_FILE } from './fetch-icons.constants';

export function saveTSFile(content: string): void {
    const dirname = process.cwd();
    const typeFilename = path.join(dirname, SVG_ICONS_TS_FILE);
    fs.writeFileSync(typeFilename, content);
}

export function saveHTMLFile(content: string): void {
    const dirname = process.cwd();
    const typeFilename = path.join(dirname, SVG_ICONS_HTML_FILE);
    fs.writeFileSync(typeFilename, content);
}
