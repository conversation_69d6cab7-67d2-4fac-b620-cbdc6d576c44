import { <PERSON><PERSON><PERSON> } from 'jsdom';
import {
    SVG_ID_PREFIX,
    SVG_TEMPLATE,
    SVG_TEMPLATE_DEFINITIONS,
    SVG_TEMPLATE_SYMBOLS
} from './fetch-icons.constants';
import { IconComponent } from './fetch-icons.types';

export function processSVGs(svgs: IconComponent[]): string {
    const symbols: string[] = [];
    const definitions: string[] = [];

    for (const svg of svgs) {
        const { viewBox, defsContent, elements } = convertToSymbol(svg);
        if (defsContent) {
            definitions.push(`  ${defsContent}`);
        }
        symbols.push(`  <symbol id="${SVG_ID_PREFIX + svg.name.toLowerCase()}" viewBox="${viewBox}">
    ${elements}
  </symbol>`);
    }
    return SVG_TEMPLATE.replace(SVG_TEMPLATE_DEFINITIONS, definitions.join('\n')).replace(
        SVG_TEMPLATE_SYMBOLS,
        symbols.join('\n')
    );
}

function convertToSymbol(icon: IconComponent): {
    viewBox: string;
    defsContent: string | undefined;
    elements: string;
} {
    const dom = new JSDOM(icon.svg);
    const svgElement = dom.window.document.querySelector('svg');
    if (!svgElement) {
        console.error(icon);
        throw new Error("Couldn't parse SVG");
    }

    const viewBox = svgElement.getAttribute('viewBox') ?? '';
    const defsElement = svgElement.querySelector('defs');

    if (icon.removeColor) {
        for (const element of Array.from(
            svgElement.querySelectorAll('path, rect, circle, ellipse, polygon, polyline, line')
        )) {
            if (element.hasAttribute('fill')) {
                element.removeAttribute('fill');
            }

            if (!element.hasAttribute('fill') && element.hasAttribute('stroke')) {
                element.setAttribute('fill', 'none');
            }
        }
    }

    const elements = (svgElement.innerHTML ?? '').trim();

    return { viewBox, defsContent: defsElement?.outerHTML?.replace(/\n/g, ''), elements };
}
