/* eslint-disable no-console */
import { fetchSVGData } from './fetch-icons.async';
import { saveHTMLFile, saveTSFile } from './fetch-icons.fs';
import { processSVGs } from './fetch-icons.utils';

(async function () {
    console.log('Loading icons...');
    const svgData = await fetchSVGData();

    console.log('Processing SVGs...');
    const htmlContent = processSVGs(svgData.svg);

    console.log('Saving HTML');
    saveHTMLFile(htmlContent);
    console.log('Saving TS');
    saveTSFile(svgData.ts);
    console.log('😎');
})();
