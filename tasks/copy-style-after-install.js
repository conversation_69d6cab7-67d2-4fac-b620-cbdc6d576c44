/* eslint-disable no-console */
import fs from 'fs-extra';

console.log('# Copying styles');
(async () => {
    try {
        // Copy files individually
        const filesToCopy = [
            {
                sourceFile: './node_modules/@angular/cdk/overlay-prebuilt.css',
                destination: 'style/overlay-prebuilt.css'
            },
            {
                sourceFile: './node_modules/@angular/cdk/a11y-prebuilt.css',
                destination: 'style/a11y-prebuilt.css'
            },
            {
                sourceFile: './node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css',
                destination: 'style/deeppurple-amber.css'
            }
        ];
        const destinationFolder = './projects/ui-lib/src/';

        filesToCopy.forEach(file => {
            fs.copyFile(file.sourceFile, `${destinationFolder}${file.destination}`, err => {
                if (err) {
                    throw err;
                }
            });
        });

        console.log(`Copy of ${filesToCopy.length} files success`);
    } catch (error) {
        console.error(error);
    }
})();
