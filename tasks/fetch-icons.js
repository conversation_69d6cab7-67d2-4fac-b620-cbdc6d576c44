/* eslint-disable no-console */
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { JSDOM } from 'jsdom';
import { mkdirp } from 'mkdirp';

/**
 * Fetch SVG icons from Figma and other sources, generates a sprite file and a TypeScript type file.
 */

const FIGMA_TOKEN = '*********************************************';
const FIGMA_API_URL = 'https://api.figma.com/v1';
const FIGMA_FILE_ID = '6pAapSukvpZDo8KhTsxLcV';

const framesConfig = [
    { pageId: '1:6', frameId: '5317:809', removeColor: true, description: 'Monochromatic' },
    { pageId: '1:6', frameId: '5337:219', removeColor: false, description: 'Colored' }
];

const sanitizeName = name => name.replace(/[^a-zA-Z0-9_]/g, '-').toLowerCase();
const stripPrefix = name => name.replace(/^(icon-ui-|icon-studio-|icon-misc-)/, '');
const removeExtraNewlines = svgContent => svgContent.replace(/\n\s*\n/g, '\n');

const fetchSvg = async url => {
    try {
        const response = await fetch(url);
        if (response.ok) {
            return await response.text();
        }
        throw new Error(`Failed to fetch SVG from ${url}. Status: ${response.status}`);
    } catch (error) {
        throw new Error(`Error fetching SVG from ${url}: ${error}`);
    }
};

const fetchFigmaFileData = async () => {
    const response = await fetch(`${FIGMA_API_URL}/files/${FIGMA_FILE_ID}`, {
        headers: { 'X-Figma-Token': FIGMA_TOKEN }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch file data. Status: ${response.status}`);
    }

    return response.json();
};

const findPageById = (document, pageId) => document.children.find(page => page.id === pageId);

const findFrameById = (page, frameId) => page.children.find(frame => frame.id === frameId);

const fetchFigmaComponents = async config => {
    const { pageId, frameId, removeColor, description } = config;
    const data = await fetchFigmaFileData();
    const targetPage = findPageById(data.document, pageId);

    if (!targetPage) {
        const availableCanvases = data.document.children
            .filter(child => child.type === 'CANVAS')
            .map(page => `- ID: ${page.id}, Name: ${page.name}`)
            .join('\n');
        throw new Error(`Page with ID ${pageId} not found. Available canvases:\n${availableCanvases}`);
    }

    console.log(`Inspecting the page: ${targetPage.name} - ${description}`);

    const targetFrame = findFrameById(targetPage, frameId);

    if (!targetFrame) {
        const availableFrames = targetPage.children
            .filter(child => child.type === 'FRAME')
            .map(frame => `- ID: ${frame.id}, Name: ${frame.name}`)
            .join('\n');
        throw new Error(`Frame with ID ${frameId} not found. Available frames:\n${availableFrames}`);
    }

    console.log(`Inspecting the frame: ${targetFrame.name}`);

    const components = [];
    const findComponents = nodes => {
        nodes.forEach(node => {
            if (node.type === 'COMPONENT') {
                components.push({
                    id: node.id,
                    name: `icon-ui-${sanitizeName(node.name)}`,
                    removeColor
                });
            }
            if (node.children) {
                findComponents(node.children);
            }
        });
    };
    findComponents(targetFrame.children);

    return components;
};

const fetchFigmaSvgs = async components => {
    const componentIds = components.map(component => component.id).join(',');
    const imageResponse = await fetch(
        `${FIGMA_API_URL}/images/${FIGMA_FILE_ID}?ids=${componentIds}&format=svg`,
        { headers: { 'X-Figma-Token': FIGMA_TOKEN } }
    );

    if (!imageResponse.ok) {
        throw new Error(`Failed to fetch component images. Status: ${imageResponse.status}`);
    }

    const imageData = await imageResponse.json();
    return Promise.all(
        components.map(async component => {
            const svgUrl = imageData.images[component.id];
            if (svgUrl) {
                const svgContent = await fetchSvg(svgUrl);
                if (svgContent) {
                    return { ...component, svg: svgContent };
                }
            }
            throw new Error(`No SVG URL found for component ${component.name}`);
        })
    );
};

const convertToSymbol = (svg, name, removeColor) => {
    const dom = new JSDOM(svg);
    const svgElement = dom.window.document.querySelector('svg');
    const viewBox = svgElement.getAttribute('viewBox');
    const defsElement = svgElement.querySelector('defs');
    const defsContent = defsElement ? defsElement.outerHTML : '';

    Array.from(
        svgElement.querySelectorAll('path, rect, circle, ellipse, polygon, polyline, line')
    ).forEach(element => {
        if (removeColor) {
            if (element.hasAttribute('fill')) {
                element.removeAttribute('fill');
            }

            if (!element.hasAttribute('fill') && element.hasAttribute('stroke')) {
                element.setAttribute('fill', 'none');
            }
        }
    });

    const elements = svgElement.innerHTML;

    return { viewBox, defsContent, elements };
};

const processSvgSprite = svgs => {
    const iconNames = new Set();
    const duplicateIcons = [];

    let spriteContent = `<svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n<defs>\n`;

    const symbols = svgs
        .map(({ name, svg, removeColor }) => {
            const baseName = stripPrefix(name);
            if (iconNames.has(baseName)) {
                duplicateIcons.push(baseName);
            } else {
                iconNames.add(baseName);
            }

            const { viewBox, defsContent, elements } = convertToSymbol(svg, baseName, removeColor);
            spriteContent += `${defsContent}\n`;
            return `<symbol id="${name}" viewBox="${viewBox}">\n${elements}\n</symbol>`;
        })
        .join('\n');

    if (duplicateIcons.length > 0) {
        throw new Error(`Duplicate icon names found: ${duplicateIcons.join(', ')}`);
    }

    spriteContent += `</defs>\n${symbols}\n</svg>`;
    return removeExtraNewlines(spriteContent);
};

const writeSvgSprite = async (svgContent, filename) => {
    const dirname = path.dirname(filename);
    await mkdirp(dirname);
    fs.writeFileSync(filename, svgContent);
};

const generateIconsTs = async (newIcons, typeFilename) => {
    const oldIcons = fs.existsSync(typeFilename)
        ? fs
              .readFileSync(typeFilename, 'utf-8')
              .match(/'([^']+)'/g)
              ?.map(match => match.replace(/'/g, '')) || []
        : [];

    const addedIcons = newIcons.filter(icon => !oldIcons.includes(icon));
    const removedIcons = oldIcons.filter(icon => !newIcons.includes(icon));

    if (addedIcons.length > 0) {
        console.log('Added icons:', addedIcons);
    }
    if (removedIcons.length > 0) {
        console.log('Removed icons:', removedIcons);
    }

    const iconArray = `export const icons = [\n${newIcons.map(icon => `    '${icon}'`).join(',\n')}\n] as const;`;
    const iconType = `export type Icon = typeof icons[number];`;

    const fileContent = `/** Generated by fetch-icons.js - Do not edit manually! */
${iconArray}

${iconType}
`;

    fs.writeFileSync(typeFilename, fileContent);
};

(async () => {
    try {
        const dirname = process.cwd();
        const allComponents = (await Promise.all(framesConfig.map(fetchFigmaComponents))).flat();
        if (allComponents.length === 0) {
            throw new Error('No components found in the specified frames.');
        }

        const figmaSvgs = await fetchFigmaSvgs(allComponents);
        const svgSpriteContent = processSvgSprite(figmaSvgs);

        const spriteFilename = path.join(
            dirname,
            'projects/ui-lib/src/components/icon/svg-icon/svg-icons.component.html'
        );
        await writeSvgSprite(svgSpriteContent, spriteFilename);

        const dom = new JSDOM(svgSpriteContent);
        const newIcons = Array.from(dom.window.document.querySelectorAll('symbol')).map(symbolElement =>
            stripPrefix(symbolElement.id)
        );

        const typeFilename = path.join(
            dirname,
            'projects/ui-lib/src/components/icon/svg-icon/icons.ts'
        );
        await generateIconsTs(newIcons, typeFilename);

        console.log('\nSVG icons and TypeScript type file generated successfully.');
    } catch (error) {
        console.error('\nError processing SVG icons:\n', error);
    }
})();
