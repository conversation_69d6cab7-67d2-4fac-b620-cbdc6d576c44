// For more info, see https://github.com/storybookjs/eslint-plugin-storybook#configuration-flat-config-format
import storybook from 'eslint-plugin-storybook';

// Breaking change from eslint: https://eslint.org/docs/latest/use/migrate-to-9.0.0#-new-default-config-format-eslintconfigjs
import bannerflowEslint from '@bannerflow/eslint-config-bannerflow';

export default [
    bannerflowEslint.style,
    bannerflowEslint.typeScriptWithPlugins,
    bannerflowEslint.angularWithPlugins,
    {
        rules: {
            '@typescript-eslint/naming-convention': 'off',
            '@typescript-eslint/explicit-function-return-type': 'off',
            '@typescript-eslint/no-use-before-define': 'off',
            'newline-per-chained-call': 'off',
            'no-console': ['error', { allow: ['error', 'warn'] }]
        }
    },
    {
        ignores: ['storybook-static/*', '**/docs/*', 'dist/*', 'demo/*', 'local-packages/*']
    },
    ...storybook.configs['flat/recommended']
];
