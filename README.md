# UI

---

UI is a collection of components that is used across our platform at Bannerflow.

---

>[!NOTE]
>See [NUI docs](./NUI_README.md) for UI uplift documentation

## Prerequisites

Install through NPM:

`npm install @bannerflow/ui` or `pnpm i @bannerflow/ui`

Please also check the required `peerDependencies`.

In your SCSS file, you need to import the stylus files:

`@require 'ui/src/style';`

## Usage

### Modules

In your app module, import the `UIModule`:

```ts
import { UIModule } from '@bannerflow/ui';

@NgModule({
    imports: [
        UIModule,
    ]
})
export class AppModule { }
```

### Standalone

``` ts
import { UIButtonComponent } from '@Bannerflow/ui'

@Component({
    standalone: true,
    import: [UIButtonComponent],
    ...
})
export class MyCoolComponent{}
```

## Setup in project

Too be able to use @bannerflow/ui within your project you need to edit some files.

Open `angular.json` and add ``styles``

```json
{
    "projects": {
      "YOUR_PROJECT": {
        "architect": {
          "build": {
            "options": {
              "styles": [
                "./node_modules/@bannerflow/ui/index.scss",
              ],
```

Then open `app.component.ts` and add:

```typescript
import { UIThemeService } from '@bannerflow/ui';

...
constructor(..., private uiThemeService: UIThemeService) {...}
...
```

## Development

`pnpm run watch-dev` <- Watches code changes in the library

`pnpm run storybook` <- Runs Storybook where you will be able to see your
component's stories

### Local development using `pnpm pack`

* Build or watch the library by running `pnpm run build`
* Run `cd dist` and `pnpm pack .`
* On your projects folder, open a terminal and run the link command:
`pnpm i ../bannerflow-ui/dist/bannerflow-ui-<version_number>.tgz`
* Run your project as you normally do

### Local development inside another project

If you need to develop inside UI, but test it in an already setup project you
need create `nodemon.json` file with the snippet below and specify the path to
that project inside in the `OUTPUT_DIRECTORY` environment variable.

```json
{
    "verbose": false,
    "watch": [
        "projects"
    ],
    "env": {
        "SOURCE_DIRECTORY": "./dist",
        "OUTPUT_DIRECTORY": "../../",
        "TARGET_APP_DIRECTORY": ""
    },
    "ext": "ts,json,html,scss"
}
```

Here you should point to the specific projects root. It will automatically set
up the path `/node_modules/@bannerflow/ui` for you. If you are using windows,
remember to use double backslashes because the first will be escaped.

**NOTE**  
`TARGET_APP_DIRECTORY` has to be defined if the app is in a workspace/monorepo
(i.e not the same place as node_modules (`OUTPUT_DIRECTORY`))

After this run `pnpm run local-dev` to start the local dev watcher. This will
watch everything inside the `./projects/` folder, rebuild when detecting changes
and inject it into `/node_modules/@bannerflow/ui` in your other project. Please
note that you will have to rebuild your other project every time you make
changes to Bannerflow UI.

## Creating a Component

Whenever you create a new component please place it under the correct component
folder. For example, if we create a new button component. It should be placed
under the `buttons` suite:

```txt
src/
  components/
    buttons/
      button/
        button.component.ts
        button.component.scss
        button.component.html
        button.component.spec.ts (Optional, unit tests)
        button.component.stories.ts (Encouraged, Storybook)
```

## Create release to NPM

* Implement your changes
* Create a PR towards main
    * add `(major)` to the title to have a major update
    * add `(minor)` to the title to have a minor update
    * add `(patch)` or nothing to have a patch update
* Once the PR is merged, it'll be automatically published 

## Icons

Icons are hosted & fetched directly on Figma. To update the icons, you need to
do the following:

* Run `pnpm run fetch-icons`
* Bump the package version as per the [versioning](#create-release-to-npm) section

The fetch-icons script will fetch the latest icons via the Figma API and
generate the necessary files for the icons to be used in the project. You can
preview all icons in Storybook locally by running `npm run storybook`, then
navigate to the served Storybook and navigate to
`Components -> Icons -> UISVGIcon -> All Icons`.

### NUI Icons

* Run `pnpm run fetch-icons:nui`

## Figma

* [Figma plugin code](https://github.com/nordicfactory/figma-tokens-plugin)

## Local Addons

### Storybook Root Attributes Addon

This project includes a local copy of the `storybook-addon-root-attributes`
package in the `local-packages` directory. The addon is automatically built
during installation through the prepare scripts:

#### Usage of Addon

Once configured, you can find the Root Attributes controls in the Storybook UI:

![Root Attributes Addon UI](./local-packages/storybook-addon-root-attributes/addon-screenshot.png)

1. Open any story in Storybook
2. Look for the "Root Attributes" tab in the addon panel (usually at the top right)
3. Here you can toggle different attributes like:
   * data-uinew (html) Off / On
   * data-uitheme (body) Off / Light / Dark

Note: Selecting "Off" for any attribute will completely remove that attribute
from the HTML element, rather than just setting it to a different value.

## NUI

See [the wiki](https://wikiflow.atlassian.net/wiki/spaces/COBE/pages/4574248979/UI+Uplift+Frontend+Implementation+DEMO)
