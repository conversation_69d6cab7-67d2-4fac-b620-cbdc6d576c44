{"name": "@bannerflow/ui", "description": "Bannerflow UI components", "repository": {"url": "git+ssh://**************/nordicfactory/ui-lib.git"}, "license": "SEE LICENSE IN /dev/null", "scripts": {"build": "ng run ui-lib:build:production && pnpm copy-style", "build-dev": "ng run ui-lib:build:development && pnpm copy-style", "build-storybook": "ng run ui-lib:build-storybook", "copy-style": "node ./tasks/copy-style-after-build.js", "fetch-icons": "node ./tasks/fetch-icons", "fetch-icons:nui": "tsx ./tasks/icons/fetch-icons", "format:check": "prettier format . --check", "format:write": "prettier format . --write", "lint": "eslint --cache --max-warnings=0", "lint:fix": "eslint --fix", "lint:fix:styles": "stylelint --fix projects/**/*.scss", "lint:styles": "stylelint projects/**/*.scss", "local-dev": "nodemon ./tasks/watch.js --ignore projects/ui-lib/components/icon/svg-icon/", "postinstall": "pnpm copy-style", "prepare": "husky && pnpm prepare:addons", "prepare:addons": "cd local-packages/storybook-addon-root-attributes && pnpm install && pnpm build", "prettier": "prettier projects/**/*.{js,ts,html,scss} --write", "prettier:check": "prettier projects/**/*.{js,ts,html,scss} --check", "storybook": "ng run ui-lib:storybook", "test": "ng test ui-lib --code-coverage=true", "test-ci": "ng test ui-lib --no-watch --no-progress --browsers=ChromeHeadlessNoSandbox --code-coverage=true", "test-storybook": "test-storycook", "test-storybook-ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"npx http-server storybook-static --port 6006 --silent\" \"wait-on tcp:6006 && pnpm test-storybook\"", "watch-dev": "ng run ui-lib:build:development --watch"}, "pre-commit": ["lint"], "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{js,ts,css,html,md}": "prettier --write", "*.{css,scss}": "stylelint --fix"}, "type": "module", "dependencies": {"intersection-observer": "0.12.2", "material-symbols": "^0.28.1", "tslib": "^2.3.0"}, "peerDependencies": {"@angular/animations": "^20.0.2", "@angular/cdk": "^20.0.0", "@angular/common": "^20.0.2", "@angular/core": "^20.0.2", "@angular/forms": "^20.0.2", "@angular/material": "^20.0.2", "@angular/platform-browser": "^20.0.2", "@angular/platform-browser-dynamic": "^20.0.2", "@angular/router": "^20.0.2", "dayjs": "^1.11.13", "rxjs": "^7.8.1"}, "devDependencies": {"@angular-eslint/eslint-plugin": "^19.7.1", "@angular/build": "^20.0.1", "@angular/cli": "~20.1.5", "@angular/compiler": "^20.0.2", "@angular/compiler-cli": "^20.0.2", "@angular/language-service": "^20.0.2", "@babel/runtime": "^7.25.9", "@bannerflow/eslint-config-bannerflow": "^19.1.0", "@chromatic-com/storybook": "^4.0.0", "@compodoc/compodoc": "^1.1.23", "@storybook/addon-designs": "^10.0.1", "@storybook/addon-docs": "9.0.5", "@storybook/addon-links": "^9.0.5", "@storybook/angular": "^9.0.5", "@storybook/cli": "^9.0.5", "@storybook/icons": "^1.4.0", "@storybook/test": "^8.6.14", "@testing-library/jest-dom": "^6.6.2", "@types/eslint": "^8.56.5", "@types/jasmine": "^5.1.4", "@types/jasminewd2": "2.0.13", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "~8.0.0", "@typescript-eslint/parser": "~8.0.0", "chromatic": "^11.12.6", "concurrently": "^8.2.2", "core-js": "^3.36.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-storybook": "^9.0.5", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "husky": "^9.0.11", "jasmine-core": "^5.0.0", "jasmine-spec-reporter": "^7.0.0", "jsdom": "^26.1.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.0.0", "karma-jasmine-html-reporter": "^2.1.0", "karma-junit-reporter": "^2.0.1", "lint-staged": "^15.2.2", "mkdirp": "^3.0.1", "ng-packagr": "^20.0.0", "node-fetch": "^3.3.2", "nodemon": "^3.1.7", "pre-commit": "1.2.2", "prettier": "^3.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "source-map-support": "0.5.21", "storybook": "^9.0.5", "storybook-addon-root-attributes": "file:local-packages/storybook-addon-root-attributes", "stylelint": "^16.10.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-value-no-unknown-custom-properties": "^6.0.1", "ts-node": "^10.9.2", "tslib": "2.6.2", "tsx": "^4.7.1", "typescript": "~5.8.3", "wait-on": "^8.0.4", "webpack-bundle-analyzer": "^4.10.2", "zone.js": "^0.15.0"}, "publishConfig": {"access": "restricted"}, "pnpm": {"overrides": {"tmp@<=0.2.3": ">=0.2.4", "cross-spawn@<6.0.6": ">=6.0.6", "on-headers@<1.1.0": ">=1.1.0"}, "onlyBuiltDependencies": ["@compodoc/compodoc", "@parcel/watcher", "core-js", "esbuild", "lmdb", "msgpackr-extract", "pre-commit", "spawn-sync"]}}