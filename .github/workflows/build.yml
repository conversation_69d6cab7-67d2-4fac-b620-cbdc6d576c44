name: Deploy and publish

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

on:
  push:
    branches:
      - main
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - main
  workflow_dispatch:

env:
  BUILD_CHANNEL_ID: "frontend-ci"
  BUILD_DESCRIPTION: ""

jobs:
  check-for-pr:
    name: Check for open PRs
    runs-on: ubuntu-latest
    outputs:
      have_pr: ${{ steps.check_pr.outputs.have_pr }}
      should_run: ${{ github.event_name == 'workflow_dispatch' || (github.event_name == 'push' && steps.check_pr.outputs.have_pr == 'false') }}
    permissions:
      pull-requests: read
    steps:
      - name: Check for open PRs
        id: check_pr
        uses: nordicfactory/shared-github-actions/github/check-pr@main

  build-and-publish:
    needs: check-for-pr
    if: ${{ needs.check-for-pr.outputs.should_run && github.event_name != 'pull_request' }}
    name: Build and publish
    permissions:
      actions: read
      contents: read
      id-token: write
      checks: write
      packages: write
    runs-on: ubuntu-latest
    outputs:
      publish_outcome: ${{ steps.publish.outcome }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Get release info
        id: release_info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Authenticate NPM
        uses: nordicfactory/shared-github-actions/build/node/authenticate@main
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build dist
        id: dist
        uses: nordicfactory/shared-github-actions/build/node/dist@main
        with:
          build-command: "build"
          use-pnpm: true

      - name: Reset .npmrc
        run: |
          git restore .npmrc

      - name: Publish package
        id: publish
        uses: nordicfactory/shared-github-actions/build/node/publish@main
        with:
          directory: "dist"
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}
          use-gitversion: false
          use-pnpm: true
          auto-versioning: true

      - name: Send Slack fail message
        id: send-build-failed
        if: ${{ always() && failure() && steps.publish.conclusion != 'skipped' }}
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.BUILD_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.BUILD_DESCRIPTION }} <${{ steps.build.outputs.url_html }}|test results> :x:"

      - name: Send Slack success message
        if: ${{ success() && steps.publish.conclusion != 'skipped' }}
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.BUILD_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.BUILD_DESCRIPTION }} ${{ steps.publish.outputs.npm-version }}: published :white_check_mark:"

      - name: Add summary
        run: |
            echo "Version published:  ${{ steps.publish.outputs.npm-version }}. You can run 'pnpm i @bannerflow/ui@${{ steps.publish.outputs.npm-version }}'" >> $GITHUB_STEP_SUMMARY

  build-storybook:
    needs: check-for-pr
    if: ${{ needs.check-for-pr.outputs.should_run }}
    name: Build and publish Storybook
    runs-on: ubuntu-latest
    environment:
      name: storybook
      url: ${{ steps.deploy.outputs.deployment-alias-url }}

    permissions:
      actions: read
      contents: read
      packages: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Build Storybook
        id: build-storybook
        uses: nordicfactory/shared-github-actions/build/node/build@main
        with:
          test-command: "build-storybook"
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}
          use-pnpm: true

      - name: Publish Storybook
        id: deploy
        uses: nordicfactory/shared-github-actions/deploy/cloudflare/pages@main
        with:
          api-token: ${{ secrets.CLOUDFLARE_PAGES_API_TOKEN }}
          account-id: ${{ secrets.CLOUDFLARE_PAGES_ACCOUNT_ID }}
          project-name: storybook-ui
          directory: storybook-static
          comment-pr-url: ${{ github.event_name == 'pull_request' }}

