name: "Tests and Chromatic"

permissions:
    contents: read
    checks: write

concurrency:
  group: tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  tests:
    name: Run Tests and Chromatic
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Test & Report
        id: test-ci
        uses: nordicfactory/shared-github-actions/build/node/build@main
        with:
          test-command: "test-ci"
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}
          use-pnpm: true

      - name: Build
        id: build
        if: ${{ github.event_name == 'pull_request' }}
        run: pnpm build

      - name: Set Chromatic Environment
        run: echo "CHROMATIC=true" >> $GITHUB_ENV

      - name: Run Chromatic
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          autoAcceptChanges: "main"
          onlyChanged: true
          exitOnceUploaded: true