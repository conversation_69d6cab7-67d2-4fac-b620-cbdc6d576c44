name: Update dependent repos

on:
  workflow_run:
    workflows: ["Deploy and publish"]
    types:
      - completed
    branches:
      - main

env:
  SLACK_CHANNEL_ID: "frontend-ci"

jobs:
  get-dependent-repos:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    name: Get dependent repos
    outputs:
      repo_list: ${{ steps.fetch_repos.outputs.repo_list }}
    steps:
      - name: Create GitHub App Token
        id: github_app_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.GHAPP_APP_ID }}
          private-key: ${{ secrets.GHAPP_PRIVATE_KEY }}
          owner: nordicfactory

      - name: Authenticate GitHub CLI
        run: echo "${{ steps.github_app_token.outputs.token }}" | gh auth login --with-token

      - name: Fetch repositories with @bannerflow/ui dependency
        id: fetch_repos
        run: |
          gh api orgs/nordicfactory/repos --paginate --jq '.[] | select(.archived == false) | .full_name' > repo_list.json
          echo "[]" > filtered_repos.json

          while IFS= read -r repo; do
            repo_name=$(echo "$repo" | cut -d'/' -f2)
            if gh api repos/${repo}/contents/package.json 1>/dev/null 2>&1; then
              package_json_content=$(gh api repos/${repo}/contents/package.json --jq '.content' | base64 -d)
              if echo "$package_json_content" | jq -e '.dependencies["@bannerflow/ui"]' >/dev/null; then
                jq --arg repo "$repo_name" '. + [$repo]' filtered_repos.json > tmp.json && mv tmp.json filtered_repos.json
              fi
            fi
          done < repo_list.json

          echo "Repo list in filtered_repos.json:"
          cat filtered_repos.json

          repo_list=$(jq -c . filtered_repos.json)
          echo "repo_list=$repo_list" >> $GITHUB_OUTPUT

  send-updating-notification:
    needs: get-dependent-repos
    runs-on: ubuntu-latest
    name: Send Slack updating...
    outputs:
      ts: ${{ steps.initial-message.outputs.ts }}
      channel-id: ${{ steps.initial-message.outputs.channel-id }}
    steps:
      - name: Send Initial Slack Notification
        id: initial-message
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.SLACK_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: ":githubloading: *Bannerflow UI:* Updating dependent repositories..."

  update-dependent-repos:
    needs: get-dependent-repos
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    if: success()
    name: Update repos
    strategy:
      fail-fast: false
      matrix:
        repo: ${{ fromJson(needs.get-dependent-repos.outputs.repo_list) }}
    outputs:
      failed_repos: ${{ steps.set-failures-output.outputs.failed_repos }}
    steps:
      - name: Create GitHub App Token
        id: github_app_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.GHAPP_APP_ID }}
          private-key: ${{ secrets.GHAPP_PRIVATE_KEY }}
          owner: nordicfactory

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          repository: nordicfactory/${{ matrix.repo }}
          token: ${{ steps.github_app_token.outputs.token }}
          path: ${{ matrix.repo }}

      - name: Fetch main branch version
        working-directory: ${{ matrix.repo }}
        run: |
          main_version=$(jq -r '.dependencies["@bannerflow/ui"]' package.json | sed 's/[^0-9.]//g')
          echo "main_version=$main_version" >> $GITHUB_ENV

      - name: Set up Git config
        working-directory: ${{ matrix.repo }}
        run: |
          git config user.name "bannerflow-ui[bot]"
          git config user.email "185471835+bannerflow-ui[bot]@users.noreply.github.com"

      - name: Check branch and create/update 'update-bannerflow-ui' branch
        working-directory: ${{ matrix.repo }}
        run: |
          if git ls-remote --heads origin update-bannerflow-ui | grep update-bannerflow-ui; then
            git fetch origin update-bannerflow-ui:update-bannerflow-ui
            git checkout update-bannerflow-ui
            git pull origin update-bannerflow-ui
          else
            git checkout -b update-bannerflow-ui
          fi

      - name: Authenticate NPM
        uses: nordicfactory/shared-github-actions/build/node/authenticate@main
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}
          directory: ${{ matrix.repo }}

      - name: Check current and latest versions of @bannerflow/ui
        working-directory: ${{ matrix.repo }}
        run: |
          current_version=$(jq -r '.dependencies["@bannerflow/ui"]' package.json | sed 's/[^0-9.]//g')
          latest_version=$(npm show @bannerflow/ui version)
          echo "current_version=$current_version" >> $GITHUB_ENV
          echo "latest_version=$latest_version" >> $GITHUB_ENV

      - name: Update @bannerflow/ui if necessary
        working-directory: ${{ matrix.repo }}
        run: |
          if [ "$current_version" != "$latest_version" ]; then
            set +e
            if [ -f "package-lock.json" ]; then
              npm install @bannerflow/ui@$latest_version || { echo "update_reason=Failed to update using npm" >> $GITHUB_ENV; echo "update_failed=true" >> $GITHUB_ENV; exit 0; }
              lock_file="package-lock.json"
            elif [ -f "yarn.lock" ]; then
              npm install -g yarn
              yarn add @bannerflow/ui@$latest_version || { echo "update_reason=Failed to update using yarn" >> $GITHUB_ENV; echo "update_failed=true" >> $GITHUB_ENV; exit 0; }
              lock_file="yarn.lock"
            elif [ -f "pnpm-lock.yaml" ]; then
              npm install -g pnpm
              if [ -f "pnpm-workspace.yaml" ]; then
                pnpm add @bannerflow/ui@$latest_version -w || { echo "update_reason=Failed to update using pnpm workspace" >> $GITHUB_ENV; echo "update_failed=true" >> $GITHUB_ENV; exit 0; }
              else
                pnpm add @bannerflow/ui@$latest_version || { echo "update_reason=Failed to update using pnpm" >> $GITHUB_ENV; echo "update_failed=true" >> $GITHUB_ENV; exit 0; }
              fi
              lock_file="pnpm-lock.yaml"
            else
              echo "update_reason=No recognized lock file found" >> $GITHUB_ENV
              echo "update_failed=true" >> $GITHUB_ENV
              exit 0
            fi
            set -e

            if [ -f ".npmrc" ]; then
              git restore .npmrc || echo ".npmrc not found in git history."
            fi

            if git diff --quiet; then
              echo "has_changes=false" >> $GITHUB_ENV
              echo "update_reason=No changes to commit" >> $GITHUB_ENV
            else
              git add package.json "$lock_file"
              git commit -m "update @bannerflow/ui to version $latest_version"
              git push origin update-bannerflow-ui
              echo "has_changes=true" >> $GITHUB_ENV
            fi
          else
            echo "has_changes=false" >> $GITHUB_ENV
            echo "update_reason=Already up-to-date" >> $GITHUB_ENV
          fi

      - name: Parse version numbers and determine version bump
        id: version_check
        run: |
          current_major=$(echo "${{ env.current_version }}" | cut -d'.' -f1)
          current_minor=$(echo "${{ env.current_version }}" | cut -d'.' -f2)
          current_patch=$(echo "${{ env.current_version }}" | cut -d'.' -f3)

          latest_major=$(echo "${{ env.latest_version }}" | cut -d'.' -f1)
          latest_minor=$(echo "${{ env.latest_version }}" | cut -d'.' -f2)
          latest_patch=$(echo "${{ env.latest_version }}" | cut -d'.' -f3)

          if [[ "$current_major" != "$latest_major" ]]; then
            version_bump="major"
          elif [[ "$current_minor" != "$latest_minor" ]]; then
            version_bump="minor"
          elif [[ "$current_patch" != "$latest_patch" ]]; then
            version_bump="patch"
          fi

          echo "version_bump=$version_bump" >> $GITHUB_OUTPUT

      - name: Create or update PR
        if: ${{ env.has_changes == 'true' }}
        working-directory: ${{ matrix.repo }}
        env:
          GH_TOKEN: ${{ steps.github_app_token.outputs.token }}
        id: pr_creation
        run: |
          pr_info=$(gh pr list --repo nordicfactory/${{ matrix.repo }} --head "update-bannerflow-ui" --state open --json number,url --jq '.[0]')
          pr_number=$(echo "$pr_info" | jq -r '.number')

          commit_url="https://github.com/${{ github.repository }}/commit/${{ github.sha }}"
          version_link="[version $latest_version]($commit_url)"

          if [ -n "$pr_number" ]; then
            gh pr comment $pr_number --repo nordicfactory/${{ matrix.repo }} --body "Updated to @bannerflow/ui ${version_link}."

            pr_url=$(echo "$pr_info" | jq -r '.url')
          else
            gh pr create --title "Update @bannerflow/ui" \
                                  --body "Automated PR to update @bannerflow/ui to ${version_link}." \
                                  --head update-bannerflow-ui \
                                  --repo nordicfactory/${{ matrix.repo }}

            pr_url=$(gh pr list --repo nordicfactory/${{ matrix.repo }} --head "update-bannerflow-ui" --state open --json url --jq '.[0].url')

            pr_info=$(gh pr list --repo nordicfactory/${{ matrix.repo }} --head "update-bannerflow-ui" --state open --json number,url --jq '.[0]')
            pr_number=$(echo "$pr_info" | jq -r '.number')

            if [[ "${{ steps.version_check.outputs.version_bump }}" == "patch" ]]; then
              gh pr merge $pr_number --auto --squash --repo nordicfactory/${{ matrix.repo }}
            fi
          fi

          echo "pr_url=$pr_url" >> $GITHUB_OUTPUT

      - name: Collect failed matrix jobs
        if: failure()
        id: collect-failures
        run: |
          echo "${{ matrix.repo }}" >> failed_repos_list.txt

      - name: Set failed repos output
        if: always()
        id: set-failures-output
        run: |
          if [[ -f failed_repos_list.txt ]]; then
            failed_repos=$(awk '{printf " • %s\n", $0}' failed_repos_list.txt)
          else
            failed_repos="No failed repositories."
          fi
          echo "failed_repos=$failed_repos" >> $GITHUB_OUTPUT

      - name: Update summary report
        if: always()
        run: |
          repo_url="https://github.com/nordicfactory/${{ matrix.repo }}"
          echo "### [${{ matrix.repo }}](${repo_url})" >> $GITHUB_STEP_SUMMARY

          if [[ "${{ env.main_version }}" != "${{ env.current_version }}" ]]; then
            echo "- **Main Branch**: ${{ env.main_version }}" >> $GITHUB_STEP_SUMMARY
          fi

          echo "- **Checked Out**: ${{ env.current_version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Latest**: ${{ env.latest_version }}" >> $GITHUB_STEP_SUMMARY

          if [[ "$current_version" == "$latest_version" ]]; then
            if [[ "${{ env.main_version }}" != "${{ env.current_version }}" ]]; then
              echo "- **Status**: Main branch is behind, but update PR is already up-to-date with latest version ✔️" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Status**: Already up-to-date ✔️" >> $GITHUB_STEP_SUMMARY
            fi

          elif [[ "${{ env.update_failed }}" == "true" ]]; then
            echo "- **Status**: Update failed ❌" >> $GITHUB_STEP_SUMMARY
            echo "- **Reason**: ${{ env.update_reason || 'Reason not provided' }}" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ env.has_changes }}" == "true" ]]; then
            if [ -n "${{ steps.pr_creation.outputs.pr_url }}" ]; then
              echo "- **Status**: Updated ✅ [View PR](${{ steps.pr_creation.outputs.pr_url }})" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Status**: Updated ✅" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "- **Status**: Update failed ❌" >> $GITHUB_STEP_SUMMARY
            if [[ -n "${{ env.update_reason }}" ]]; then
              echo "- **Reason**: ${{ env.update_reason }}" >> $GITHUB_STEP_SUMMARY
            fi
          fi

  send-success-notification:
    needs: [update-dependent-repos, send-updating-notification]
    runs-on: ubuntu-latest
    name: Send Slack success
    if: success()
    steps:
      - uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ needs.send-updating-notification.outputs.channel-id }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          ts: ${{ needs.send-updating-notification.outputs.ts }}
          message: |
            ✅ *Bannerflow UI:* Update dependent repositories workflow completed successfully.

  send-failure-notification:
    needs: [update-dependent-repos, send-updating-notification]
    runs-on: ubuntu-latest
    name: Send Slack failed
    if: failure()
    steps:
      - uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ needs.send-updating-notification.outputs.channel-id }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          ts: ${{ needs.send-updating-notification.outputs.ts }}
          message: |
            ❌ *Bannerflow UI:* Update dependent repos completed with the following repo(s) failing:

            ${{ needs.update-dependent-repos.outputs.failed_repos }}

            <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View logs>
