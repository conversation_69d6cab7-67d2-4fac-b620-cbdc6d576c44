version: 2
registries:
  npm-azure-artifacts:
    type: npm-registry
    url: https://pkgs.dev.azure.com/bannerflow/_packaging/bannerflow-npm/npm/registry/
    token: '${{secrets.AZURE_DEVOPS_PAT}}:'
updates:
  - package-ecosystem: "npm"
    directory: "/"
    registries:
      - npm-azure-artifacts
    schedule:
      interval: "daily"
    # Disable version updates for npm dependencies, only allow security updates
    open-pull-requests-limit: 0
