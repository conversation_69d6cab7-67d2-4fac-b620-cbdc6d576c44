{"name": "storybook-addon-root-attributes", "version": "2.1.3", "description": "Storybook Addon Root Attributes to switch `Many` html or body attribute at runtime for your story (work in storybook 7!)", "keywords": ["storybook-addons", "root-attributes", "root", "roots", "many roots"], "repository": {"type": "git", "url": "https://github.com/junghyeonsu/storybook-addon-root-attributes"}, "license": "MIT", "author": "jung<PERSON><PERSON><PERSON> <<EMAIL>>", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./manager": "./dist/manager.mjs", "./preview": "./dist/preview.mjs", "./package.json": "./package.json"}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts"], "scripts": {"build": "tsup", "build:watch": "tsup --watch", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build"}, "devDependencies": {"@storybook/react-vite": "^9.0.5", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "storybook": "^9.0.5", "tsup": "^8.5.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["src/index.ts"], "managerEntries": ["src/manager.ts"], "previewEntries": ["src/preview.ts"]}, "storybook": {"displayName": "storybook-addon-root-attributes", "supportedFrameworks": ["react", "html"]}}