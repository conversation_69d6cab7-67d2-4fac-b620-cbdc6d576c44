{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ui-lib": {"projectType": "library", "root": "projects/ui-lib", "sourceRoot": "projects/ui-lib/src", "prefix": "ui", "architect": {"build": {"builder": "@angular/build:ng-packagr", "options": {"project": "projects/ui-lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/ui-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/ui-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "projects/ui-lib/tsconfig.spec.json", "styles": ["projects/ui-lib/src/export.scss"], "assets": [{"glob": "**/*", "input": "projects/ui-lib/src/assets"}], "polyfills": ["zone.js", "zone.js/testing"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/**/*.ts", "projects/**/*.html"]}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"port": 6006, "browserTarget": "ui-lib:build", "styles": ["projects/ui-lib/src/export.scss"]}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"browserTarget": "ui-lib:build", "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "projects/ui-lib/src/export.scss"]}}}}}, "schematics": {"@schematics/angular:component": {"inlineStyle": false, "inlineTemplate": false, "prefix": "ui", "style": "scss", "skipTests": true, "type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}, "cli": {"analytics": "fc95aea2-0bce-4a19-9ccd-601e24f899a2"}}