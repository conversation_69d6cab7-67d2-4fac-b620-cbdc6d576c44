import { setCompodo<PERSON><PERSON><PERSON> } from '@storybook/addon-docs/angular';
import doc<PERSON>son from '../documentation.json';
import 'zone.js';
setCompodo<PERSON><PERSON><PERSON>(docJson);

export const parameters = {
    angularLegacyRendering: true,
    docs: {
        codePanel: true
    },
    controls: {
        matchers: {
            color: /(background|color)$/i,
            date: /Date$/
        },
        backgrounds: {
            default: '--default-background-color',
            values: [
                {
                    name: '--default-background-color',
                    value: 'var(--default-background-color)'
                },
                {
                    name: '--ui-color-background',
                    value: 'var(--ui-color-background)'
                },
                {
                    name: '--ui-color-background-second',
                    value: 'var(--ui-color-background-second)'
                }
            ]
        },
        docs: {
            inlineStories: true
        }
    },
    rootAttributesTooltip: true,
    rootAttributes: [
        {
            root: 'html',
            attribute: 'data-uinew',
            defaultState: {
                name: 'Off',
                value: null
            },
            states: [
                {
                    name: 'On',
                    value: true
                }
            ]
        },
        {
            root: 'body',
            attribute: 'data-uitheme',
            defaultState: {
                name: 'Light',
                value: 'light'
            },
            states: [
                {
                    name: 'Dark',
                    value: 'dark'
                }
            ]
        }
    ]
};
export const tags = ['autodocs'];
