const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = {
    stories: ['../projects/ui-lib/**/*.mdx', '../projects/ui-lib/**/*.stories.@(js|jsx|ts|tsx)'],
    staticDirs: ['../projects/ui-lib/src/assets'],
    addons: [
        '@chromatic-com/storybook',
        '@storybook/addon-designs',
        '@storybook/addon-links',
        '../local-packages/storybook-addon-root-attributes/dist',
        '@storybook/addon-docs'
    ],
    framework: '@storybook/angular',
    core: {
        builder: 'webpack5'
    },
    webpackFinal: async (config, { configType }) => {
        const isChromatic = process.env.CHROMATIC === 'true';

        if (configType === 'PRODUCTION' && isChromatic) {
            config.plugins.push(
                new BundleAnalyzerPlugin({
                    analyzerMode: 'disabled',
                    generateStatsFile: true,
                    statsFilename: './preview-stats.json'
                })
            );
        }
        return config;
    }
};
