# NUI Docs

> **Process & Workflow:** For project organization, review processes, and team workflows, see the [UI Uplift: Way of Working](https://bannerflow.atlassian.net/wiki/spaces/DEV/pages/UI+Uplift+Way+of+Working) document.

## How does it work?

We defined a few attributes to be set on the `<html></html>` tag, or in your
component, this attributes are:

- `data-uinew`: meant to toggle the new UI (Nui) on and off
- `data-uitheme`: meant to toggle light/dark theme
- `data-uisize`: meant to set a size on a component

## How to uplift an old component

Ensure you have access to the design.
Should be in [Figma BSD-1.5](https://www.figma.com/design/Hz8TEh0kA6gHIZaIxJ68R1/BDS-1.5)

Prep your component by:

1. Go to your component folder
2. Create a new SCSS file, call it `<my-component-name>.new.component.scss`
3. Add said file to array of styles. i.e.:

```typescript
@Component({
    ...
    styles: ['./<my-component-name>.component.scss', './<my-component-name>.new.component.scss'],
    ...
})
export class UIMyComponentNameComponent{}
```

\
4. Go to the old SCSS file (`<my-component-name>.component.scss`) and wrap old
styles inside this SCSS rule:

```css
:where(:root:not([data-uinew])) :host {
    // Your old CSS goes here
}
```

\
5. Go to your new SCSS file (`<my-component-name>.new.component.scss`) and Add
the styles inside the following rule:

```css
:where(:root[data-uinew]) :host {
    // Your new styles goes here
}
```

\
6. In your component you will most probably need to inject `UINewThemeService`.
Which is used to check if the New UI is enabled or not.

> [!NOTE]
> Keep naming consistent with other components

```typescript
import {UINewThemeService} from 'src/services/uinew-theme.service';
...
export class UIMyComponentNameComponent{
    private readonly uiNewThemeService = inject(UINewThemeService);
    ...
    
    protected isNewUI = this.UINewThemeService.isNewUIEnabled;
    ...
}
```

\
7. You will most probably also need to keep track of the size for your
component. The recommended way is to use the `host` property as follows:

```typescript
import { input, ... } from '@angular/core'; // Extra points for using signals
import { DATASET_SIZE } from 'src/services/uinew-theme.service';
import { UIComponentSize } from 'src/types/size';
...
@Component({
    ...
    host:{
        ...
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIMyComponentNameComponent{
    ...
    // Remember to define a default value, usually "md" 
    readonly size = input<UIComponentSize>('md');
    ...
}
```

\
7. You will most probably also need to keep track of the type of your
component (primary, secondary, destructive, etc). The recommended way is to
use the `host` property as follows:

```typescript
import { input, ... } from '@angular/core'; // Extra points for using signals
...
@Component({
    ...
    host:{
        ...
        '[class]': 'type()',
    }
})
export class UIMyComponentNameComponent{
    ...
    // Remember to define a default value, if possible
    readonly type = input<'primary'|'secondary'|'secondary'>('primary'); 
    ...
}
```

\
8. For your template, what we recommend is to rewrite it closer to the design
in Figma, if feasible. For that, you can use the `isNewUI` signal declared
before. Example:

```angular
@if (isNewUI()) {
    <!-- your new template code -->
}@else{
    <!-- your old template code -->
}
```

If not feasible, or rewriting is too much work. We recommend "partial surgery"
:

```angular
<some-html>
    <some-more-html>
        @if (isNewUI()) {
            <!-- your new template code -->
            <new-html></new-html>
        }@else{
            <!-- your old template code -->
            <old-html></old-html>
        }
    </some-more-html>
</some-html>
```

## How and when to use the `UIComponentSizeDirective`?

This directive is mean to be used when you need to provide a specific size on a
standard component.
Say you have this in your HTML: `<span>My awesome text</span>`.
You need to set the `font-size` to what in Figma shows as
`font-size: var(--label-regular-sm-font-size, 12px);`.
The first step would be to remove the `-sm` suffix, and add the prefix `--nui-`.
You'd get `var(--nui-label-regular-font-size);`.
The issue now is that this will resolve to `14px` instead of `12px` since the
default "size" is `md`.
To fix this issue, you'll need to use `UIComponentSizeDirective`.
Add it to your list of imports. And then modify your HTML.
It should look like this: `<span [uiSize]="'sm'">My awesome text</span>`
Note: this will set the size for that element as `'sm'` modifying all variables

## My popover has a dark background. How to fix it?

In the popover config you can pass a string property called `panelClass`. Add
the class `transparent` to it to remove the background and border.
There are other handy class names you can pass. Check the definition

## How to create a new uplifted component

If you need to have support for the old UI, follow the previous steps. If no
support is needed, just create a new component and use designs tokens to your
hearts content.

## How to use Figma tokens (CSS variables)

In Figma we use variables (also called tokens). See [this link](https://www.figma.com/design/Hz8TEh0kA6gHIZaIxJ68R1/BDS-1.5?node-id=22-2373&p=f&vars=1&m=dev)
There's a Figma plugin used by our designers to push changes to `@bannerflow/ui`
(See [this repo](https://github.com/nordicfactory/figma-tokens-plugin/))

We map any `0 core: ...` var to a `--core-<var_name>` CSS variable. And the rest
(`1 semantic: ...` and `2 component: ...`) we map to `--nui-<var_name>`.

> [!WARNING]
> IMPORTANT: any variable prefixed with `--core-` is off limits. They are meant
> for internal use ONLY.

> [!WARNING]
> IMPORTANT: These variables are meant to be READONLY. Do not update their value
> manually

When some token changes, we update the [figma_vars.scss](./projects/ui-lib/src/style/generated/figma-vars.scss)
file, and open a new PR. ([Example](https://github.com/nordicfactory/bannerflow-ui/pull/615))

To use said tokens, you will need to prefix the names with `--nui-`. For exmaple:

```css
// This is the CSS in Figma for a label 
// See [right panel](https://www.figma.com/design/Hz8TEh0kA6gHIZaIxJ68R1/BDS-1.5?node-id=671-2987&m=dev)
label {
    display: inline-flex;
    align-items: center;
    gap: var(--label-space-gap, 8px);
}
```

This should be renamed as follows on your component

```css
:where(:root[data-uinew]) :host {
    display: inline-flex;
    align-items: center;
    gap: var(--nui-label-space-gap, 8px);
}
```

## Light/Dark mode

To check which theme is the current use `this.uiNewThemeService.currentTheme()`.
To toggle themes, use: `this.uiNewThemeService.toggleTheme()`.

The current theme will be saved in the `LocalStorage` under `BF-UI-CURRENT_THEME`

### How does theming work

We use a HTML attribute on the `<html>` tag called `data-uitheme`. That sets the
correct colors on the `0 core` and `1 semantic` variables. So it should be
automatic.

## Icons

We are trying to deprecate the usage of `<ui-icon/>` since it uses some font old
font to display icons. So, the usage of `<ui-svg-icon/>` is preferred.

There is a new input added to `<ui-svg-icon/>` called `nuiIcon`, setting this input
will render said icon when Nui is enabled. Remember that the old `icon` input is
required, so you'll need to set it to the equivalent icon or `'none'`. There's
some mapping for old icons, but it's quite flaky and will be removed.

Example:

```html
<ui-svg-icon icon="status-dot" nuiIcon="status-not-approved" />
```

Nui icons are a mix between [Material design symbols](https://www.figma.com/design/wJm4qBtJ99aJahuycfeJRS/Material-design-symbols?node-id=2-3505)
and [Custom icons](https://www.figma.com/design/wJm4qBtJ99aJahuycfeJRS/Material-design-symbols?node-id=41-7&p=f)

[Material symbols dependency](https://www.npmjs.com/package/material-symbols)

> [!NOTE]
> If you need a new icon, contact your designer

There's a CloudFlare worker used to get the icons from Figma.
[Domain](https://bf-ui-icons.bannerflow.workers.dev/)
[Worker page](https://dash.cloudflare.com/ee43d1001ae7d7c041cd6c8dd3b27e50/workers/services/view/bf-ui-icons/production/metrics)
[Repo](https://github.com/nordicfactory/frontend-utilities/tree/main/projects/bf-ui-icons)

### How to update list of custom icons

Run `pnpm run fetch-icons:nui`

## Storybook

It is important to add at least one story for the new design.
Things to consider:

- Add `tag: ['Nui'],` to the story for easy filtering
- Add `globals: { rootAttributes: { 'data-uinew': 'true' } },` to enable Nui
by default on that story.

An addon called `storybook-addon-root-attributes` was forked and added to the
codebase under `./local-packages/storybook-addon-root-attributes/`. This adds
a cog icon to the top bar in Storybook, allowing you to toggle on/off the nui,
and toggling the light/dark mode.

## Stylelint

For now, its deactivated. In the future it will provide warnings over the usage
of incorrect variables (`--core-`) or hardcoded values.

## Versioning

We recommend the following versioning system:

- New component: increase minor version
- Uplift old component: increase patch version
