{"compileOnSave": false, "compilerOptions": {"baseUrl": "ui-lib", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": true, "esModuleInterop": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "paths": {}, "declaration": false, "experimentalDecorators": true, "moduleResolution": "bundler", "importHelpers": true, "target": "ES2022", "module": "es2020", "lib": ["es2017", "dom"], "strictPropertyInitialization": false, "skipLibCheck": true, "useDefineForClassFields": false}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}, "exclude": ["**/*.preview.*"]}