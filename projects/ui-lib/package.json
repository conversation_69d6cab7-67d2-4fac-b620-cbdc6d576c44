{"name": "@bannerflow/ui", "type": "module", "peerDependencies": {"@angular/common": "^20.0.0", "@angular/core": "^20.0.0", "@angular/animations": "^20.0.0", "@angular/cdk": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.0", "dayjs": "^1.11.13", "rxjs": "^7.8.1"}, "dependencies": {"intersection-observer": "0.12.2", "material-symbols": "^0.28.1", "tslib": "^2.3.0"}, "sideEffects": false}