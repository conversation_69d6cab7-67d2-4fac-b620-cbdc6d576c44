:where(:root[data-uinew]) :host {
    display: block;

    input {
        font-size: var(--nui-font-size-350);
        font-weight: var(--nui-font-font-weight-10000);
        line-height: var(--nui-font-line-height-500);
        letter-spacing: 0.18px;
        width: 100%;
        padding: 0;
        outline: none;
        border: none;
        background-color: transparent;
        text-align: left;
        justify-self: start;
        color: var(--nui-menu-item-text-input-default);

        &:focus {
            color: var(--nui-menu-item-text-input-selected);
            outline: none;
        }

        &:hover {
            color: var(--nui-menu-item-text-input-hover);
        }
    }

    .container {
        display: grid;
        grid-template-columns: 24px auto 30px;
        cursor: pointer;
        gap: var(--nui-label-space-gap);
        height: var(--nui-forms-height);
        padding: 0 var(--nui-menu-item-space-padding-horizontal);
        place-items: center start;
        align-self: stretch;
        border-radius: var(--nui-forms-radius);
        background: var(--nui-menu-item-fill-primary-default);

        &:hover {
            background: var(--nui-menu-item-fill-primary-hover);
        }

        &.focus {
            border: var(--nui-border-width-medium) solid var(--nui-border-system-focus);
        }
    }

    &.disabled {
        pointer-events: none;
        background: var(--nui-menu-item-fill-disabled);

        .right,
        .left,
        input {
            color: var(--nui-menu-item-text-label-disabled);
        }
    }

    .right {
        justify-self: end;
        color: var(--nui-menu-item-text-shortcut-primary-default);
    }

    .checkmark,
    .edit-icon {
        &.hidden {
            opacity: 0;
        }
    }
}
