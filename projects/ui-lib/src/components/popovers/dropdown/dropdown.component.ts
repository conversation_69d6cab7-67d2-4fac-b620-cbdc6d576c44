import {
    Component,
    computed,
    contentChildren,
    ContentChildren,
    effect,
    ElementRef,
    EventEmitter,
    input,
    Input,
    OnDestroy,
    Output,
    QueryList,
    signal,
    viewChild
} from '@angular/core';
import { merge, Observable, Subscription } from 'rxjs';
import { startWith, switchMap } from 'rxjs/operators';
import { UIComponentSizeDirective } from '../../../directives';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { IPositions, Offset, UITheme } from '../../../types';
import { UIPopoverTemplateDirective } from '../popover';
import { UIPopoverTargetDirective } from '../popover/popover-target.directive';
import { UIPopoverDirective } from '../popover/popover.directive';
import { UIDropdownInputComponent } from './dropdown-input.component';
import { UIDropdownItemComponent } from './dropdown-item.component';
import { IUIDropdownComponent } from './dropdown.component.interface';
import { IUIDropdownTargetDirective } from './dropdown.directive.interface';

/**
 * Dropdown menu component
 */
@Component({
    imports: [
        UIComponentSizeDirective,
        UIDropdownItemComponent,
        UIPopoverDirective,
        UIPopoverTemplateDirective
    ],
    selector: 'ui-dropdown',
    templateUrl: 'dropdown.component.html',
    styleUrls: ['./dropdown.component.scss', './dropdown.new.component.scss'],
    host: {
        class: 'ui-dropdown',
        '[class.has-header]': 'header',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIDropdownComponent implements OnDestroy, IUIDropdownComponent {
    readonly popover = viewChild.required<UIPopoverDirective>('popover');
    // TODO:(COBE-3808) refactor these into signals. Check dropdown directive
    @Output('open') openEmitter = new EventEmitter<void>();
    @Output() close = new EventEmitter();
    @Output() isHovered = new EventEmitter<boolean>();
    // TODO: Skipped for migration because:
    //  There are references to this query that cannot be migrated automatically.
    @ContentChildren(UIDropdownItemComponent) items: QueryList<UIDropdownItemComponent>;

    /**
     * @summary:
     * Optional header for the dropdown list
     */
    @Input() header?: string;

    /**
     * @summary:
     * Width of the dropdown
     */
    @Input() width = 'auto';

    /**
     * @summary:
     * MaxHeight of the dropdown
     */
    @Input() maxHeight: number;

    /**
     * @summary:
     * Use the target's width
     */
    @Input() useTargetWidth: boolean;

    /**
     * @summary:
     * The type of dropdown
     */
    @Input() type?: string;

    /**
     * @summary:
     * Position offset of the dropdown
     */
    @Input() offset = { x: 0, y: 0 };
    /**
     * @summary:
     * Positions overrides on the popover used by the dropdown
     */
    @Input() positions: IPositions[];

    /**
     * @summary:
     * Overriding theme
     */
    @Input() theme: UITheme;

    /**
     * @summary
     * The dropdowns minimum width
     */
    @Input() minWidth: number;

    /**
     * @summary
     * If the dropdown should have a backdrop. Disabling this requires you to close the dropdown manually
     */
    @Input() hasBackdrop?: boolean;

    /**
     * @summary
     * Disables the possiblity to open the dropdown
     */
    @Input() disabled?: boolean;

    /**
     * Size
     */
    size = input<'xs' | 'sm' | 'md'>('md');

    parentDropdown: UIDropdownComponent | undefined;
    target?: IUIDropdownTargetDirective;
    closed = false;
    private subscriptions: Subscription[] = [];
    private viewChildrenSubscription?: Subscription;
    private observer?: MutationObserver;

    private childrenSignal = contentChildren(UIDropdownItemComponent);
    private childrenInputSignal = contentChildren(UIDropdownInputComponent);

    // Workaround to align items correctly
    protected hasChildWithIcon = computed(() => this.computeHasChildWithIcon());
    protected status = signal<'closed' | 'opened'>('closed');

    constructor() {
        effect(() => {
            const size = this.size();
            const popover = this.popover();
            if (size) {
                popover?.updatePopoverSize(size);
            }
        });
        effect(() => {
            // Override size on items
            const items = this.childrenSignal();
            for (const child of items) {
                // TODO:(COBE-3809) make this workaround go away
                child.size = this.size;
            }
        });
        effect(() => {
            // Override size on inputs
            const inputs = this.childrenInputSignal();
            for (const child of inputs) {
                // TODO:(COBE-3809) make this workaround go away
                child.size = this.size;
            }
        });
    }

    open(target: ElementRef, targetDirective?: IUIDropdownTargetDirective): void {
        if (this.disabled) {
            return;
        }
        this.status.set('opened');
        this.closed = false;
        this.configurePopover();
        this.target = targetDirective;
        const newTargetDirective = new UIPopoverTargetDirective(target);
        this.popover().open(newTargetDirective);
        this.subscribeViewChildren();
        this.subscribeItems();
        this.openEmitter.emit();
    }

    setOffset(offset: Offset): void {
        this.popover().config.offset = offset;
    }

    closePopover(): void {
        this.status.set('closed');
        const popover = this.popover();
        popover?.close?.();
        this.unsubscribeViewChildren();
        this.unsubscribeItems();
    }

    ngOnDestroy(): void {
        this.close.complete();
        this.unsubscribeViewChildren();
        this.unsubscribeItems();
        if (this.observer) {
            this.observer.disconnect();
        }
    }

    dropdownHovered(event: MouseEvent): void {
        if (event.type === 'mouseleave') {
            this.isHovered.emit(false);
        } else {
            this.isHovered.emit(true);
        }
    }

    hovered(): Observable<UIDropdownItemComponent | null> {
        return this.items.changes.pipe(
            startWith(this.items),
            switchMap((items: QueryList<UIDropdownItemComponent>) =>
                merge(...items.map(item => item.hovered).filter(item => !item.closed))
            )
        );
    }

    hasFixedWidth(): boolean {
        return !!(this.useTargetWidth || (this.width && this.width !== 'auto'));
    }

    private subscribeItems(): void {
        this.items.changes
            .pipe(startWith(this.items))
            .subscribe((items: QueryList<UIDropdownItemComponent>) => {
                this.unsubscribeItems();
                items.forEach(item => {
                    const subscription = item.itemClicked.subscribe(
                        (dropdownItem: UIDropdownItemComponent) => {
                            if (!dropdownItem.triggersSubdropdown && !dropdownItem.isToggable) {
                                this.closed = true;
                                this.close.emit('click');
                            }
                            if (!item.preventCloseOnClick && !item.uiDropdownTarget) {
                                this.closePopover();
                            }
                        }
                    );
                    this.subscriptions.push(subscription);
                });
            });
    }

    private unsubscribeItems(): void {
        while (this.subscriptions.length > 0) {
            this.subscriptions.pop()!.unsubscribe();
        }
    }

    /**
     * Subsribe to changes on the list of view children.
     * The changes are triggered when a new child is added or removed
     */
    private subscribeViewChildren(): void {
        this.unsubscribeViewChildren();
        this.viewChildrenSubscription = this.items.changes.subscribe(_change => this.subscribeItems);
    }

    private unsubscribeViewChildren(): void {
        if (this.viewChildrenSubscription) {
            this.viewChildrenSubscription.unsubscribe();
            this.viewChildrenSubscription = undefined;
        }
    }
    private configurePopover(): void {
        const popover = this.popover();
        popover.config.width = this.width === 'auto' ? 'auto' : `${this.width}px`;
        popover.config.maxHeight = this.maxHeight ? `${this.maxHeight}px` : undefined;
        popover.config.theme = this.theme;
        popover.config.popoverType = this.type;
        popover.config.hasBackdrop =
            typeof this.hasBackdrop !== 'undefined' ? this.hasBackdrop : popover.config.hasBackdrop;
        if (this.minWidth) {
            popover.config.minWidth = `${this.minWidth}px`;
        }
        if (this.positions) {
            popover.config.positions = this.positions;
        }
    }

    private computeHasChildWithIcon(): boolean {
        this.status();
        return this.childrenSignal().some(
            component => !!component.icon || !!component.svgIcon || !!component.nuiIcon
        );
    }
}
