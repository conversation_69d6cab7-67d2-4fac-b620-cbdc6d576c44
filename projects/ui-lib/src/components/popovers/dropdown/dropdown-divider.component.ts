import { Component, computed, inject } from '@angular/core';
import { DATASET_SIZE, UINewThemeService } from '../../../services';

/**
 * Dropdown divider component
 *
 * Style params:
 * `--divider-color` sets background-color on component
 *
 * Nui styles params:
 * None
 */
@Component({
    standalone: true,
    selector: 'ui-dropdown-divider',
    template: `
        @if (isNewUI()) {
            <hr />
        }
    `,
    styles: [
        `
            :where(:root:not([data-uinew])) :host {
                margin: 3px 0;
                height: 1px;
                background: var(--divider-color);
                display: block;
            }

            :where(:root[data-uinew]) :host {
                display: flex;
                padding: var(--nui-divider-space-padding-vertical, 4px) 0px;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                align-self: stretch;

                hr {
                    margin: 0;
                    padding: 0;
                    border: none;
                    width: 100%;
                    height: 1px;
                    align-self: stretch;
                    background: var(--nui-divider-fill-primary-default, #eff0f3);
                }
            }
        `
    ],
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: '"md"'
    }
})
export class UIDropdownDividerComponent {
    private readonly uiNewThemeService = inject(UINewThemeService);
    protected isNewUI = computed(() => this.uiNewThemeService.isNewThemeEnabled());
}
