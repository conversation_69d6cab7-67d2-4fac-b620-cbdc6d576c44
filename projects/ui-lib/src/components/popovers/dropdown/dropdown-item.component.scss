:where(:root:not([data-uinew])) :host {
    --white-space: normal;

    height: var(--item-height);
    line-height: var(--item-height);
    text-align: left;
    display: grid;
    grid-template-columns: 24px auto auto;
    cursor: pointer;
    align-content: center;
    user-select: none;
    max-width: 250px;

    &:not(.ui-dropdown-header, .disabled) {
        &:hover {
            background: var(--item-hover-color);

            .right > .tiny-label {
                color: var(--ui-color-text);
            }
        }
    }

    &.ui-dropdown-header {
        cursor: default;
        border-bottom: 1px solid var(--ui-color-border);
        height: 35px;
        display: flex;
        padding-left: 10px;
        align-items: center;

        .content {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 700;
        }
    }

    &.disabled {
        color: var(--ui-color-text-second);
        pointer-events: none;
    }

    &.has-toggle {
        .left {
            padding: 0;
        }
    }

    &.is-highlighted {
        background: var(--item-hover-color);
    }

    &.has-arrow {
        grid-template-columns: 24px auto 30px;
    }

    .checkmark {
        &.hidden {
            opacity: 0;
        }
    }

    & > * {
        height: var(--item-height);
    }

    .content {
        white-space: var(--white-space);
    }

    .left {
        justify-self: center;
        align-items: center;
        padding: 0;
        display: flex;
    }

    .content {
        min-width: 0;
        display: flex;
        align-items: center;

        & ::ng-deep ui-flag {
            margin-right: 8px;
            flex-shrink: 0;
        }

        & ::ng-deep ui-icon {
            margin-right: 8px;
            font-size: 12px;
        }

        & ::ng-deep span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .right {
        justify-self: right;
        padding-right: 10px;
        min-width: 24px;

        .loader {
            position: relative;
            background: none;
        }

        .arrow {
            font-size: 14px;
            color: #ccc;
        }

        .tiny-label {
            color: #ccc;
            font-size: 10px;
        }
    }
}
