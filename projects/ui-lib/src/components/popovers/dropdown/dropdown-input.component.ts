import {
    Component,
    ElementRef,
    Input,
    OnDestroy,
    computed,
    inject,
    input,
    output
} from '@angular/core';
import { Subject } from 'rxjs';
import { DATASET_SIZE, UINewThemeService } from '../../../services';
import { UIIconComponent } from '../../icon';
import { UILabelComponent } from '../../label';

/**
 * Menu item with an text/number input
 *
 * Style params (only old ui):
 * `--font-size` sets the font-size
 * `--ui-color-surface` sets the background-color
 * `--input-item-height` sets the input line-height and height
 * `--ui-color-focus` sets the input border when input is focused
 * `--ui-color-text-third` sets the color on the right arrow
 */
@Component({
    imports: [UIIconComponent, UILabelComponent],
    selector: 'ui-dropdown-input',
    templateUrl: 'dropdown-input.component.html',
    styleUrls: ['./dropdown-input.component.scss', './dropdown-input.new.component.scss'],
    host: {
        class: 'ui-dropdown-input',
        '[class.disabled]': `disabled`,
        '[class.has-toggle]': 'isToggable',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(mouseenter)': 'mouseEnterEvent()',
        '(click)': 'clickEvent()'
    }
})
export class UIDropdownInputComponent implements OnDestroy {
    private readonly elementRef = inject<ElementRef<HTMLElement>>(ElementRef);
    private readonly uiNewThemeService = inject(UINewThemeService);

    @Input() disabled = false;
    @Input() toggle: boolean;
    @Input() placeholder: string;
    @Input() type: 'number' | 'text';
    @Input() placeholderFocus: string;
    @Input() units: string;
    @Input() icon: string;
    size = input<'xs' | 'sm' | 'md'>('md');

    toggleChange = output<boolean>();
    valueChange = output<string | number>();
    itemClicked = output<UIDropdownInputComponent>();

    get isToggable(): boolean {
        return typeof this.toggle !== 'undefined';
    }

    triggersSubdropdown = false;
    hovered = new Subject<UIDropdownInputComponent>();
    value: string | number;
    focus = false;

    protected isNewUI = computed(() => this.uiNewThemeService.isNewThemeEnabled());

    ngOnDestroy(): void {
        this.hovered.complete();
    }

    // TODO:(COBE-3807) remove this in Studio
    _getHostElement(): HTMLElement {
        return this.elementRef.nativeElement;
    }

    protected onFocus(inputElement: HTMLInputElement): void {
        this.focus = true;
        if (this.placeholderFocus) {
            inputElement.placeholder = this.placeholderFocus;
        }
        inputElement.value = inputElement.value.replace(this.units, '').replace(' ', '');
    }

    protected inputChange(inputElement: HTMLInputElement): void {
        if (this.type === 'number') {
            inputElement.value = inputElement.value.replace(/\D/g, '');
        }
        this.valueChange.emit(inputElement.value.replace(this.units, '').replace(' ', ''));
        this.value = inputElement.value;
    }

    protected focusOut(inputElement: HTMLInputElement): void {
        this.focus = false;
        if (this.units) {
            this.appendUnits(inputElement);
        }

        if (this.placeholder) {
            inputElement.placeholder = this.placeholder;
        }
    }

    protected mouseEnterEvent(): void {
        if (!this.disabled) {
            this.hovered.next(this);
        }
    }

    protected clickEvent(): void {
        if (!this.disabled) {
            if (this.isToggable) {
                this.toggle = !this.toggle;
                this.toggleChange.emit(this.toggle);
            }

            this.itemClicked.emit(this);
        }
    }

    private appendUnits(inputElement: HTMLInputElement): void {
        if (inputElement.value.length !== 0 && !inputElement.value.includes(this.units) && this.units) {
            inputElement.value = `${inputElement.value} ${this.units}`;
        }
    }
}
