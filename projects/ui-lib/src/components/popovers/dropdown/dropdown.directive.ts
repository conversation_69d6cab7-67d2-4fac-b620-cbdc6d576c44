import {
    AfterContentInit,
    Directive,
    ElementRef,
    EventEmitter,
    HostListener,
    inject,
    Input,
    OnDestroy,
    OnInit,
    Output
} from '@angular/core';
import { merge, Observable, of, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { UINewThemeService } from '../../../services';
import { UIDropdownItemComponent } from './dropdown-item.component';
import { UIDropdownComponent } from './dropdown.component';
import { IUIDropdownTargetDirective } from './dropdown.directive.interface';

@Directive({
    standalone: true,
    selector: '[uiDropdownTarget]',
    exportAs: 'uiDropdownTarget',
    host: {
        '[class.is-highlighted]': 'dropdownOpen'
    }
})
export class UIDropdownTargetDirective
    implements OnInit, OnDestroy, AfterContentInit, IUIDropdownTargetDirective
{
    private readonly uiNewThemeService = inject(UINewThemeService);
    hostElementRef = inject<ElementRef<UIDropdownComponent & HTMLElement>>(ElementRef);
    parentDropdown = inject(UIDropdownComponent, { optional: true });
    private dropdownItem = inject(UIDropdownItemComponent, { optional: true, self: true });

    private hoverSubscription$ = Subscription.EMPTY;
    private closeSubscription$ = Subscription.EMPTY;
    private dropdownSubscription = Subscription.EMPTY;

    private _dropdownOpen = false;

    @Output() dropdownOpened = new EventEmitter<void>();
    @Output() dropdownClosed = new EventEmitter<void>();
    @Input() dropdownData: any = {};

    get dropdownOpen(): boolean {
        return this._dropdownOpen;
    }

    /**
     * @summary:
     *   Set to false to enable underlying components interaction
     */
    @Input() hasBackdrop?: boolean;

    /**
     * @summary:
     * (Directive) Should target a reference variable set on the dropdown component, e.g #dropdown
     */
    @Input('uiDropdownTarget') dropdownComponent: UIDropdownComponent | undefined;

    constructor() {
        const dropdownItem = this.dropdownItem;

        if (dropdownItem) {
            dropdownItem.triggersSubdropdown = this.triggersSubDropdown();
        }
    }

    @HostListener('click') onClick(): void {
        if (!this.triggersSubDropdown()) {
            this.toggleDropdown();
        }
    }

    @HostListener('mouseenter') onMouseEnter(): void {
        if (this.triggersSubDropdown()) {
            this.subscribeHover();
            this.openDropdown();
        }
    }

    @HostListener('mouseleave') onMouseLeave(): void {
        if (this.triggersSubDropdown()) {
            this.hoverSubscription$.unsubscribe();
        }
    }

    ngOnInit(): void {
        if (!this.dropdownComponent) {
            return;
        }
        if (!(this.dropdownComponent instanceof UIDropdownComponent)) {
            throw new Error(
                'uiDropdownTarget must be referencing to a reference variable on the dropdown component, e.g #dropdown. Make sure you are not passing just a string value.'
            );
        }
    }

    toggleDropdown(): void {
        return this._dropdownOpen ? this.closeDropdown() : this.openDropdown();
    }

    ngOnDestroy(): void {
        this.dropdownComponent?.closePopover();
        this.unsubscribeToActions();
        this.dropdownClosed.unsubscribe();
    }

    ngAfterContentInit(): void {
        this.subscribeDropdownComponent();
    }

    openDropdown(): void {
        if (!this.dropdownOpen && !this.dropdownComponent?.disabled) {
            this.closeSubscription$ = this.closingActions().subscribe(() => {
                this.closeDropdown();
            });
            this.initDropdown();
        }
    }

    initDropdown(): void {
        if (!this.dropdownComponent) {
            return;
        }
        const triggersSubDropdown = this.triggersSubDropdown();

        if (triggersSubDropdown) {
            this.positionSubDropdown();
        }
        this.dropdownComponent.parentDropdown =
            triggersSubDropdown && this.parentDropdown ? this.parentDropdown : undefined;
        if (this.hasBackdrop !== undefined) {
            this.dropdownComponent.popover().config.hasBackdrop = this.hasBackdrop;
        } else {
            this.dropdownComponent.popover().config.hasBackdrop = !this.triggersSubDropdown();
        }

        this.dropdownComponent.closePopover();
        this.dropdownComponent.open(this.hostElementRef, this);

        this.setIsDropdownOpen(true);
    }

    closeDropdown(): void {
        this.dropdownComponent?.close.emit();
        this._dropdownOpen = false;
    }

    triggersSubDropdown(): boolean {
        return !!(this.dropdownItem && this.parentDropdown && !this.parentDropdown?.closed);
    }

    private positionSubDropdown(): void {
        if (!this.dropdownComponent) {
            return;
        }
        const { x, width, height } =
            this.hostElementRef.nativeElement.getBoundingClientRect() as DOMRect;
        const subDropdownWidth = +this.dropdownComponent.width ? +this.dropdownComponent.width : width;
        let offsetX = window.innerWidth - x - width > subDropdownWidth ? width : -subDropdownWidth;
        let offsetY = 5;

        if (this.uiNewThemeService.isNewThemeEnabled()) {
            offsetY = 0;
            const el = this.parentDropdown?.popover().popoverRef?.overlayRef.hostElement;
            if (el) {
                const popoverMaster = el.querySelector('ui-popover-master');
                if (popoverMaster && popoverMaster.scrollHeight > popoverMaster.clientHeight) {
                    offsetX += offsetX > 0 ? 8 : -8; // scrollbar width
                }
            }
        }
        this.dropdownComponent.setOffset({ x: offsetX, y: -height - offsetY });
    }

    private closingActions(): Observable<unknown> {
        const backdrop = this.dropdownComponent?.popover().onClose.asObservable() ?? of(undefined);
        const parentClose = this.parentDropdown
            ? this.parentDropdown.close.asObservable()
            : of(undefined);
        const hover = this.parentDropdown
            ? this.parentDropdown.hovered().pipe(
                  filter(active => !!active && active !== this.dropdownItem),
                  filter(() => this._dropdownOpen)
              )
            : of(undefined);

        return merge(backdrop, parentClose, hover);
    }

    private destroyDropdown(): void {
        if (!this.dropdownComponent) {
            return;
        }
        if (this.dropdownOpen) {
            this.setIsDropdownOpen(false);
            this.dropdownComponent.closePopover();
            if (!this.parentDropdown) {
                // If root dropdown is closed, make sure everything else is closed as well
                this.dropdownComponent.items.forEach(item => {
                    if (item.uiDropdownTarget && item.uiDropdownTarget instanceof UIDropdownComponent) {
                        item.uiDropdownTarget.closePopover();
                    }
                });
            }
            this.closeSubscription$.unsubscribe();
        }
    }

    private unsubscribeToActions(): void {
        this.closeSubscription$.unsubscribe();
        this.hoverSubscription$.unsubscribe();
        this.dropdownSubscription.unsubscribe();
    }

    private setIsDropdownOpen(isOpen: boolean): void {
        this._dropdownOpen = isOpen;
        if (this._dropdownOpen) {
            this.dropdownOpened.emit();
        } else {
            this.dropdownClosed.emit();
        }
    }

    private subscribeHover(): void {
        const hoverSubscription = this.parentDropdown
            ?.hovered()
            .pipe(filter(activeDropdownitem => activeDropdownitem === this.dropdownItem))
            .subscribe(() => this.openDropdown());
        if (hoverSubscription) {
            this.hoverSubscription$ = hoverSubscription;
        }
    }

    private subscribeDropdownComponent(): void {
        if (!this.dropdownComponent) {
            return;
        }
        this.dropdownSubscription = this.dropdownComponent.close.subscribe(
            (reason: 'click' | undefined) => {
                this.destroyDropdown();
                // If clicked we should close the chain of nested dropdowns
                if (reason === 'click' && this.parentDropdown) {
                    this.parentDropdown.close.emit(reason);
                }
            }
        );
    }
}
