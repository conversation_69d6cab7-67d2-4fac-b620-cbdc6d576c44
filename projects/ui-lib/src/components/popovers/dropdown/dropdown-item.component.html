@if (isNewUI()) {
    <div class="option">
        <ui-label
            [size]="size()"
            [type]="'primary'"
            [leadingIcon]="nuiIcon || (isToggable && toggle ? 'check' : undefined)"
            [class.has-icon]="!!(nuiIcon || (isToggable && toggle ? 'check' : undefined))">
            <ng-container *ngTemplateOutlet="content"></ng-container>
        </ui-label>
    </div>
    <div class="shortcut">
        @if (shortcutTemplate()) {
            <ng-content select="[ui-dropdown-item-shortcut]" />
        } @else {
            @if (label) {
                <ui-label
                    [size]="size()"
                    [type]="'primary'">
                    {{ label }}
                </ui-label>
            }
            <div class="right-icon-wrapper">
                @if (loading) {
                    <ui-loader
                        class="loader"
                        [inline]="true" />
                }
                @if (!loading && uiDropdownTarget) {
                    <ui-svg-icon
                        class="arrow"
                        icon="none"
                        nuiIcon="chevron_right"
                        [size]="size()" />
                }
            </div>
        }
    </div>
} @else {
    <div class="left">
        @if (isToggable) {
            <ui-icon
                class="checkmark"
                [class.hidden]="!toggle"
                [icon]="'checkmark'"></ui-icon>
        }
        @if (icon) {
            <ui-icon
                class="icon"
                [icon]="icon"></ui-icon>
        }
        @if (svgIcon) {
            <ui-svg-icon
                class="icon"
                [icon]="svgIcon"></ui-svg-icon>
        }
    </div>
    <div class="content">
        <ng-container *ngTemplateOutlet="content"></ng-container>
    </div>
    <div class="right">
        @if (loading) {
            <ui-loader class="loader"> </ui-loader>
        }
        @if (!loading && uiDropdownTarget) {
            <ui-icon
                class="arrow"
                icon="arrow-right">
            </ui-icon>
        }
        @if (label) {
            <div class="tiny-label">
                {{ label }}
            </div>
        }
    </div>
}

<ng-template #content> <ng-content></ng-content> </ng-template>
