import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UISVGIconComponent } from '../../icon';
import { UIPopoverDirective, UIPopoverTemplateDirective } from '../popover';
import { UITooltipDirective } from '../tooltip';
import { UIDropdownDividerComponent } from './dropdown-divider.component';
import { UIDropdownInputComponent } from './dropdown-input.component';
import { UIDropdownItemComponent } from './dropdown-item.component';
import { UIDropdownComponent } from './dropdown.component';
import { UIDropdownTargetDirective } from './dropdown.directive';

const meta: Meta<UIDropdownComponent> = {
    title: 'Components/Popovers/Dropdown',
    component: UIDropdownComponent,
    decorators: [
        moduleMetadata({
            imports: [
                BrowserAnimationsModule,
                UIDropdownInputComponent,
                UIDropdownDividerComponent,
                UIPopoverDirective,
                UIPopoverTemplateDirective,
                UIDropdownItemComponent,
                UIDropdownTargetDirective,
                UISVGIconComponent,
                UITooltipDirective
            ]
        })
    ],
    argTypes: {
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] }
    },
    parameters: {
        controls: {
            include: ['iconEnabled', 'size', 'width']
        }
    }
};
export default meta;

type Story = StoryObj<UIDropdownComponent>;

export const Default: Story = {
    args: {},
    render: args => ({
        ...args,
        props: {
            ...args,
            alert: (message: string): void => {
                window.alert(message);
            }
        },
        template: `
<div>
    <ui-svg-icon
        icon="kebab"
        [uiDropdownTarget]="moreMenu" />

    <ui-dropdown [size]="size" #moreMenu type="menu" [width]="width || 'auto'">
        <ui-dropdown-item
            (click)="alert('clicked first option')">
            First option
        </ui-dropdown-item>
        <ui-dropdown-item svgIcon="menu-checked" [label]="'Shortcut'">
            Option with label
        </ui-dropdown-item>
        <ui-dropdown-divider/>
        <ui-dropdown-item
            (click)="alert('clicked option after divider')">
            Option after divider
        </ui-dropdown-item>
        <ui-dropdown-item [toggle]="true">
            Toggle
        </ui-dropdown-item>
        <ui-dropdown-item [toggle]="true" [preventCloseOnClick]="true">
            Toggle with preventCloseOnClick
        </ui-dropdown-item>
       <ui-dropdown-item svgIcon="menu-checked" [loading]="true">
            Loading item
        </ui-dropdown-item>
        <ui-dropdown-item
            [uiDropdownTarget]="submenu1">
            Submenu 1
        </ui-dropdown-item>
         <ui-dropdown-item
            [uiDropdownTarget]="submenu1">
            Submenu 2
        </ui-dropdown-item>
        <ui-dropdown-item
            [uiDropdownTarget]="submenu2">
            Empty submenu
        </ui-dropdown-item>
        <ui-dropdown-divider/>
        <ui-dropdown-item [disabled]="true" >
            Disabled option
        </ui-dropdown-item>
        <ui-dropdown-divider />
        <ui-dropdown-input icon="edit" placeholder="input with icon"/>
        <ui-dropdown-input units="px" placeholder="some pixeles"/>
        <ui-dropdown-input units="cm" placeholder="disabled" [disabled]="true"/>
        <ui-dropdown-divider />
        <ui-dropdown-item class="destructive" svgIcon="menu-checked" [label]="'⌘  Del'" >
            Destructive item
        </ui-dropdown-item>
        <ui-dropdown-item class="destructive" svgIcon="menu-checked" [disabled]="true">
            Disabled destructive item
        </ui-dropdown-item>
    </ui-dropdown>
    <ui-dropdown #submenu1 type="menu">
        <ui-dropdown-item >
            Option in submenu 1
        </ui-dropdown-item>
        <ui-dropdown-item
            [uiDropdownTarget]="subSubmenu">
            Sub-submenu
        </ui-dropdown-item>
    </ui-dropdown>

    <ui-dropdown #subSubmenu type="menu">
        <ui-dropdown-item>
            Option in sub-submenu
        </ui-dropdown-item>
        <ui-dropdown-item [disabled]="true">
            Disabled option in sub-submenu
        </ui-dropdown-item>
    </ui-dropdown>

    <ui-dropdown #submenu2 type="menu"></ui-dropdown>
</div>
`
    })
};

export const DropdownWithTooltip: Story = {
    args: {},
    render: args => ({
        ...args,
        props: {
            ...args,
            alert: (message: string): void => {
                window.alert(message);
            }
        },
        template: `
<span [uiTooltip]="'This is a tooltip'" [uiTooltipPosition]="'right'">
    Hover here to see the tooltip
</span>
<div>
    <ui-svg-icon
        icon="kebab"
        [uiDropdownTarget]="moreMenu" />

    <ui-dropdown [size]="size" #moreMenu type="menu" [width]="width || 'auto'">
        <ui-dropdown-item
            (click)="alert('clicked first option')">
            First option
        </ui-dropdown-item>
    </ui-dropdown>
</div>
`
    })
};
