:where(:root[data-uinew]) :host-context(.has-child-with-icon) {
    :host:not(.has-icon) .option ui-label:not(.has-icon) {
        padding-left: calc(var(--nui-icon-font-icon-size) + var(--nui-menu-item-space-gap-option));
    }
}

:where(:root[data-uinew]) :host {
    display: flex;
    height: var(--nui-forms-height);
    padding: 0 var(--nui-menu-item-space-padding-horizontal);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    cursor: pointer;
    border-radius: var(--nui-menu-item-radius);
    background: var(--nui-menu-item-fill-primary-default);

    --width: 340px; // default "md" uisize

    width: var(--width);

    &[data-uisize='sm'] {
        --width: 280px;
    }

    &[data-uisize='xs'] {
        --width: 260px;
    }

    :host-context(.ui-dropdown-list.has-fixed-width) {
        --width: 100%;
    }

    .checkmark.hidden {
        opacity: 0;
    }

    &.has-icon {
        .icon-wrapper {
            width: var(--nui-icon-width);
            height: var(--nui-icon-height);
        }
    }

    .option {
        display: flex;
        align-items: center;
        gap: var(--nui-menu-item-space-gap-option);

        ui-icon.icon {
            color: var(--nui-menu-item-icon-label-primary-default);
        }

        ui-svg-icon {
            --color: var(--nui-menu-item-icon-label-primary-default);
        }

        ui-label {
            --color: var(--nui-menu-item-text-label-primary-default);
        }
    }

    .shortcut {
        display: flex;
        align-items: center;
        gap: var(--nui-menu-item-space-gap-shortcut);

        ui-label {
            --color: var(--nui-menu-item-text-shortcut-primary-default);
        }

        ui-svg-icon {
            --color: var(--nui-menu-item-icon-shortcut-primary-default);
        }

        .right-icon-wrapper {
            width: var(--nui-icon-width);
            height: var(--nui-icon-height);
            display: flex;
            align-items: center;

            &:empty {
                display: none;
            }
        }
    }

    &.has-sub-menu-open,
    &:hover {
        background: var(--nui-menu-item-fill-primary-hover);

        .option {
            ui-icon.icon {
                color: var(--nui-menu-item-icon-label-primary-hover);
            }

            ui-svg-icon {
                --color: var(--nui-menu-item-icon-label-primary-hover);
            }

            ui-label {
                --color: var(--nui-menu-item-text-label-primary-hover);
            }
        }

        .shortcut {
            ui-label {
                --color: var(--nui-menu-item-text-shortcut-primary-hover);
            }

            ui-svg-icon {
                --color: var(--nui-menu-item-icon-shortcut-primary-hover);
            }
        }
    }

    &:active {
        background: var(--nui-menu-item-fill-primary-pressed);

        .option {
            ui-icon.icon {
                color: var(--nui-menu-item-icon-label-primary-selected);
            }

            ui-svg-icon {
                --color: var(--nui-menu-item-icon-label-primary-selected);
            }

            ui-label {
                --color: var(--nui-menu-item-text-label-primary-selected);
            }
        }

        .shortcut {
            ui-svg-icon {
                --color: var(--nui-menu-item-icon-shortcut-primary-selected);
            }

            ui-label {
                --color: var(--nui-menu-item-text-shortcut-primary-selected);
            }
        }
    }

    &.disabled {
        pointer-events: none;
        background: var(--nui-menu-item-fill-disabled);

        .option {
            ui-icon.icon {
                color: var(--nui-menu-item-icon-label-disabled);
            }

            ui-svg-icon {
                --color: var(--nui-menu-item-icon-label-disabled);
            }

            ui-label {
                --color: var(--nui-menu-item-text-label-disabled);
            }
        }

        .shortcut {
            ui-svg-icon {
                --color: var(--nui-menu-item-icon-shortcut-disabled);
            }

            ui-label {
                --color: var(--nui-menu-item-text-shortcut-disabled);
            }
        }
    }

    &.destructive {
        background: var(--nui-menu-item-fill-destructive-default);

        .option {
            ui-icon.icon {
                color: var(--nui-menu-item-icon-label-destructive-default);
            }

            ui-svg-icon {
                --color: var(--nui-menu-item-icon-label-destructive-default);
            }

            ui-label {
                --color: var(--nui-menu-item-text-label-destructive-default);
            }
        }

        .shortcut {
            ui-label {
                --color: var(--nui-menu-item-text-shortcut-destructive-default);
            }

            ui-svg-icon {
                --color: var(--nui-menu-item-icon-shortcut-destructive-default);
            }
        }

        &:hover {
            background: var(--nui-menu-item-fill-destructive-hover);

            .option {
                ui-icon.icon {
                    color: var(--nui-menu-item-icon-label-destructive-hover);
                }

                ui-svg-icon {
                    --color: var(--nui-menu-item-icon-label-destructive-hover);
                }

                ui-label {
                    --color: var(--nui-menu-item-text-label-destructive-hover);
                }
            }

            .shortcut {
                ui-label {
                    --color: var(--nui-menu-item-text-shortcut-destructive-hover);
                }

                ui-svg-icon {
                    --color: var(--nui-menu-item-icon-shortcut-destructive-hover);
                }
            }
        }

        &:active {
            background: var(--nui-menu-item-fill-destructive-pressed);

            .option {
                ui-icon.icon {
                    color: var(--nui-menu-item-icon-label-destructive-selected);
                }

                ui-svg-icon {
                    --color: var(--nui-menu-item-icon-label-destructive-selected);
                }

                ui-label {
                    --color: var(--nui-menu-item-text-label-destructive-selected);
                }
            }

            .shortcut {
                ui-svg-icon {
                    --color: var(--nui-menu-item-icon-shortcut-destructive-selected);
                }

                ui-label {
                    --color: var(--nui-menu-item-text-shortcut-destructive-selected);
                }
            }
        }

        &.disabled {
            background: var(--nui-menu-item-fill-disabled);

            .option {
                ui-icon.icon {
                    color: var(--nui-menu-item-icon-label-disabled);
                }

                ui-svg-icon {
                    --color: var(--nui-menu-item-icon-label-disabled);
                }

                ui-label {
                    --color: var(--nui-menu-item-text-label-disabled);
                }
            }

            .shortcut {
                ui-svg-icon {
                    --color: var(--nui-menu-item-icon-shortcut-disabled);
                }

                ui-label {
                    --color: var(--nui-menu-item-text-shortcut-disabled);
                }
            }
        }
    }
}
