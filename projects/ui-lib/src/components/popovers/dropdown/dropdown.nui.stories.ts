import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { userEvent, within } from 'storybook/test';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UISVGIconComponent } from '../../icon';
import { UIPopoverDirective, UIPopoverTemplateDirective } from '../popover';
import { UITooltipDirective } from '../tooltip';
import { UIDropdownDividerComponent } from './dropdown-divider.component';
import { UIDropdownInputComponent } from './dropdown-input.component';
import { UIDropdownItemShortcutDirective } from './dropdown-item-shortcut.directive';
import { UIDropdownItemComponent } from './dropdown-item.component';
import { UIDropdownComponent } from './dropdown.component';
import { UIDropdownTargetDirective } from './dropdown.directive';

const meta: Meta<UIDropdownComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Dropdown',
    component: UIDropdownComponent,
    decorators: [
        moduleMetadata({
            imports: [
                BrowserAnimationsModule,
                UIDropdownInputComponent,
                UIDropdownDividerComponent,
                UIPopoverDirective,
                UIPopoverTemplateDirective,
                UIDropdownItemComponent,
                UIDropdownTargetDirective,
                UISVGIconComponent,
                UITooltipDirective,
                UIDropdownItemShortcutDirective
            ]
        })
    ],
    argTypes: {
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] }
    },
    parameters: {
        controls: {
            include: ['iconEnabled', 'size', 'width']
        }
    }
};
export default meta;

export const Playground: StoryObj<UIDropdownComponent & { iconEnabled: boolean }> = {
    argTypes: {
        iconEnabled: {
            type: 'boolean',
            control: {
                type: 'boolean'
            },
            description: 'enables/disables icons. Need to reopen dropdown'
        }
    },
    args: { iconEnabled: true },
    play: async ({ canvasElement }) => {
        // Force NUI before play function finishes
        canvasElement.ownerDocument.documentElement.dataset['uinew'] = 'true';
        await new Promise<void>(res => {
            const intervalId = setInterval(() => {
                if (canvasElement.ownerDocument.documentElement.dataset['uinew']) {
                    res();
                    clearInterval(intervalId);
                }
            }, 100);
        });
        const canvas = within(canvasElement.parentElement as HTMLElement);
        const settingsIcon = canvas.getByTestId('settings-icon');
        await userEvent.click(settingsIcon);
        const submenu1 = canvas.getByTestId('submenu1');
        await userEvent.click(submenu1);
    },
    render: args => ({
        ...args,
        props: {
            ...args
        },
        template: `
<div>
    <ui-svg-icon
        data-testId="settings-icon"
        icon="kebab"
        [uiDropdownTarget]="moreMenu" />

    <ui-dropdown [size]="size" #moreMenu type="menu" [width]="width || 'auto'">
        <ui-dropdown-item
            (click)="alert('clicked first option')">
            First option
        </ui-dropdown-item>
        <ui-dropdown-item
            [nuiIcon]="iconEnabled ? 'check' : undefined"
            [label]="'Shortcut'">
            Option with label
        </ui-dropdown-item>
        <ui-dropdown-divider/>
        <ui-dropdown-item
            (click)="alert('clicked option after divider')">
            Option after divider
        </ui-dropdown-item>
       <ui-dropdown-item
            [nuiIcon]="iconEnabled ? 'check' : undefined"
            [loading]="true">
            Loading item
        </ui-dropdown-item>
        <ui-dropdown-item [toggle]="true">
            Toggle
        </ui-dropdown-item>
        <ui-dropdown-item [toggle]="true" [preventCloseOnClick]="true">
            Toggle with preventCloseOnClick
        </ui-dropdown-item>
        <ui-dropdown-item
            data-testid="submenu1"
            [uiDropdownTarget]="submenu1">
            Submenu 1
        </ui-dropdown-item>
        <ui-dropdown-item
            data-testid="submenu2"
            [uiDropdownTarget]="submenu1">
            Submenu 2
        </ui-dropdown-item>
        <ui-dropdown-item
            data-testid="submenu2"
            [disabled]="true"
            [uiDropdownTarget]="submenu1">
            Submenu 2 but disabled
        </ui-dropdown-item>
        <ui-dropdown-item
            label="Test"
            [uiDropdownTarget]="submenu2">
            Empty submenu
        </ui-dropdown-item>
        <ui-dropdown-divider/>
        <ui-dropdown-item [disabled]="true" >
            Disabled option
        </ui-dropdown-item>
        <ui-dropdown-divider />
        <ui-dropdown-input icon="edit" placeholder="input with icon"/>
        <ui-dropdown-input units="px" placeholder="some pixeles"/>
        <ui-dropdown-input units="cm" placeholder="disabled" [disabled]="true"/>
        <ui-dropdown-divider />
        <ui-dropdown-item class="destructive"
            [nuiIcon]="iconEnabled ? 'check' : undefined"
            [label]="'⌘  Del'" >
            Destructive item
        </ui-dropdown-item>
        <ui-dropdown-item class="destructive"
            [nuiIcon]="iconEnabled ? 'check' : undefined"
            [disabled]="true">
            Disabled destructive item
        </ui-dropdown-item>
        <ui-dropdown-item>
            With custom shortcut
            <ng-container [ui-dropdown-item-shortcut]>
                <a href="https://bannerflow.com"
                    class="ui-link"
                    target="_blank"
                    (click)="$event.stopPropagation()">
                    visit bannerflow
                </a>
            </ng-container>
        </ui-dropdown-item>
    </ui-dropdown>

    <ui-dropdown #submenu1 [size]="size" type="menu">
        <ui-dropdown-item >
        Option in submenu 1
        </ui-dropdown-item>
        <ui-dropdown-item
        [uiDropdownTarget]="subSubmenu">
        Sub-submenu
        </ui-dropdown-item>
    </ui-dropdown>

    <ui-dropdown #subSubmenu [size]="size" type="menu">
        <ui-dropdown-item>
        Option in sub-submenu
        </ui-dropdown-item>
        <ui-dropdown-item [disabled]="true">
        Disabled option in sub-submenu
        </ui-dropdown-item>
    </ui-dropdown>

    <ui-dropdown #submenu2 [size]="size" type="menu"></ui-dropdown>
</div>
`
    })
};
