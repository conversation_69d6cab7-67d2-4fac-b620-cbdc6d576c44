<ui-popover
    #popover="ui-popover"
    [config]="{
        panelClass: 'ui-dropdown',
        hasBackdrop: false,
        width: width,
        useTargetWidth: useTargetWidth,
        offset: offset
    }">
    <ng-template
        ui-popover-template
        #viewContent>
        @if (header) {
            <ui-dropdown-item class="ui-dropdown-header">
                {{ header }}
            </ui-dropdown-item>
        }
        <div
            class="ui-dropdown-list ui-scrollbar"
            (mouseenter)="dropdownHovered($event)"
            (mouseleave)="dropdownHovered($event)"
            [uiSize]="size()"
            [class.has-child-with-icon]="hasChildWithIcon()"
            [style.width]="hasFixedWidth() ? '100%' : 'auto'"
            [class.has-fixed-width]="hasFixedWidth()"
            [style.maxHeight]="maxHeight + 'px'">
            <ng-content></ng-content>
        </div>
    </ng-template>
</ui-popover>
