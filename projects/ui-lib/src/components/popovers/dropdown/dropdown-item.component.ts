import { NgTemplateOutlet } from '@angular/common';
import {
    Component,
    computed,
    contentChild,
    DestroyRef,
    ElementRef,
    EventEmitter,
    inject,
    Input,
    input,
    OnDestroy,
    Output,
    output,
    signal
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, take } from 'rxjs';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UINUIIcon } from '../../../types/icons';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { Icon } from '../../icon/svg-icon/icons';
import { UILabelComponent } from '../../label';
import { UILoaderComponent } from '../../loader/loader.component';
import { IUIDropdownComponent } from './dropdown.component.interface';
import { UIDropdownItemShortcutDirective } from './dropdown-item-shortcut.directive';

/**
 * Dropdown menu item component
 *
 * Style params (old ui only):
 * `--white-space` sets the `white-space` property on content
 * `--item-height` sets `height` and `line-height`
 * `--item-hover-color` sets `background` on hover and not disabled
 * `--ui-color-text` sets `color` on label
 * `--ui-color-border` sets `color` on ui-dropdown-header
 * `--ui-color-text-second` sets color when disabled
 *
 * Nui Style params:
 * `--width`: sets the width property
 */
@Component({
    imports: [
        UIIconComponent,
        UILoaderComponent,
        UISVGIconComponent,
        UILabelComponent,
        NgTemplateOutlet
    ],
    selector: 'ui-dropdown-item',
    templateUrl: 'dropdown-item.component.html',
    styleUrls: ['./dropdown-item.component.scss', './dropdown-item.new.component.scss'],
    host: {
        class: 'ui-dropdown-item',
        '[class.disabled]': `disabled`,
        '[class.has-toggle]': 'isToggable',
        '[class.has-arrow]': 'uiDropdownTarget',
        '[class.destructive]': 'destructive',
        '[class.has-icon]': '!!icon || !!svgIcon || !!nuiIcon',
        '[class.has-sub-menu-open]': 'hasSubMenuOpen()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(mouseenter)': 'mouseEnterEvent()',
        '(mouseleave)': 'mouseLeaveEvent()',
        '(click)': 'clickEvent($event)'
    }
})
export class UIDropdownItemComponent implements OnDestroy {
    private readonly elementRef = inject<ElementRef<HTMLElement>>(ElementRef);
    private readonly destroyRef = inject(DestroyRef);
    private readonly uiNewThemeService = inject(UINewThemeService);

    @Input() disabled?: boolean;
    @Input() toggle?: boolean;
    @Input() loading?: boolean;
    @Input() label: string;
    @Input() icon: string;
    @Input() svgIcon: Icon | '';
    @Input() nuiIcon: UINUIIcon | undefined;
    @Input() uiDropdownTarget?: any | IUIDropdownComponent | undefined;
    @Input() preventCloseOnClick: boolean;
    @Input() preventToggleChange?: boolean;
    @Input() destructive?: boolean;

    size = input<'xs' | 'sm' | 'md'>('md');

    readonly shortcut = input<string>();

    toggleChange = output<boolean>();
    // TODO:(COBE-3808) refactor parent to use signals instead
    @Output() itemClicked = new EventEmitter<UIDropdownItemComponent>();

    shortcutTemplate = contentChild(UIDropdownItemShortcutDirective);

    get isToggable(): boolean {
        return typeof this.toggle !== 'undefined';
    }

    triggersSubdropdown = false;
    hovered = new BehaviorSubject<UIDropdownItemComponent | null>(null);

    protected hasSubMenuOpen = signal<boolean>(false);

    protected isNewUI = computed(() => this.uiNewThemeService.isNewThemeEnabled());

    constructor() {
        this.hovered.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(hovered => {
            const target = this.uiDropdownTarget as IUIDropdownComponent | undefined;
            if (target) {
                target.openEmitter.pipe(take(1)).subscribe(() => {
                    if (hovered) {
                        this.hasSubMenuOpen.set(true);
                    } else {
                        this.hasSubMenuOpen.set(false);
                    }
                });
                target.close.pipe(take(1)).subscribe(() => {
                    this.hasSubMenuOpen.set(false);
                });
            }
        });
    }

    ngOnDestroy(): void {
        this.hovered.complete();
    }

    // TODO:(COBE-3807) remove this in Studio
    _getHostElement(): HTMLElement {
        return this.elementRef.nativeElement;
    }

    protected mouseEnterEvent(): void {
        if (!this.disabled || !this.loading) {
            this.hovered.next(this);
        }
    }
    protected mouseLeaveEvent(): void {
        if (!this.disabled || !this.loading) {
            this.hovered.next(null);
        }
    }
    protected clickEvent(): void {
        if (this.preventToggleChange) {
            return;
        }

        if (!this.disabled || !this.loading) {
            if (this.isToggable) {
                this.toggle = !this.toggle;
                this.toggleChange.emit(this.toggle);
            }
            this.itemClicked.emit(this);
        }
    }
}
