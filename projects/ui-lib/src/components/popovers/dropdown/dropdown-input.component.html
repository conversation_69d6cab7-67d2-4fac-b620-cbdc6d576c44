@if (isNewUI()) {
    <!-- TODO:(COBE-3804) implement correct structure here -->
    <div class="option">
        <ui-label type="destructive">WIP</ui-label>
    </div>
    <div class="shortcut"></div>
} @else {
    <div [class]="focus ? 'container focus' : 'container'">
        <div class="left">
            @if (isToggable) {
                <ui-icon
                    class="checkmark"
                    [class.hidden]="!toggle"
                    icon="checkmark"></ui-icon>
            }
        </div>
        <div class="center">
            <input
                #input
                type="text"
                (keydown.escape)="input.blur()"
                (input)="inputChange(input)"
                (focus)="onFocus(input)"
                (focusout)="focusOut(input)"
                [placeholder]="placeholder ? placeholder : ''" />
        </div>
        <div class="right">
            @if (!focus) {
                <ui-icon
                    class="edit-icon"
                    [class.hidden]="!icon"
                    icon="edit"></ui-icon>
            } @else {
                {{ units }}
            }
        </div>
        <ng-template #edit>
            {{ units }}
        </ng-template>
    </div>
}
