:where(:root:not([data-uinew])) :host {
    display: block;

    input {
        font-size: var(--font-size);
        place-self: center center;
        padding: 0;
        outline-style: none;
        box-shadow: none;
        border: 1px solid transparent;
        width: 100%;
        background-color: var(--ui-color-surface);

        &:focus {
            outline: none 0;
        }

        &::-webkit-inner-spin-button,
        ::-webkit-outer-spin-button {
            appearance: none;
            margin: 0;
            outline: 0;
        }
    }

    .container {
        display: grid;
        grid-template-columns: 24px auto auto;
        place-items: center center;
        border: 1px solid transparent;
        height: var(--input-item-height);
        line-height: var(--input-item-height);
        text-align: left;
        cursor: pointer;
        user-select: none;

        &.focus {
            border: 1px solid var(--ui-color-focus);
        }
    }

    .right {
        color: var(--ui-color-text-third);
        justify-self: end;
        padding-right: 10px;
        min-width: 24px;
    }

    .checkmark {
        &.hidden {
            opacity: 0;
        }
    }

    .edit-icon {
        &.hidden {
            opacity: 0;
        }
    }

    .left {
        padding: 0;
    }

    ui-icon {
        display: grid;
        justify-content: center;
        align-items: center;
    }
}
