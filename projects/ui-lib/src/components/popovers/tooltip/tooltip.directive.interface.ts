export type TooltipType = 'default' | 'discrete';

export interface IUITooltipDirective {
    uiTooltip: string | undefined;
    uiTooltipWidth: number | 'auto';
    uiTooltipMaxWidth: string | 'none';
    uiTooltipHideArrow: boolean;
    uiTooltipPosition: 'top' | 'left' | 'bottom' | 'right';
    uiTooltipClass: 'ui-tooltip' | string;
    uiTooltipUseTargetWidth: boolean;
    uiTooltipTrigger: 'click' | 'hover';
    uiTooltipDisabled: boolean;
    uiTooltipOnlyWhenTruncated: boolean;
    uiTooltipTriggerHost: HTMLElement;
    uiTooltipType: TooltipType;
    uiTooltipDelay: number;
    uiTooltipInteractive: boolean;
    uiTooltipTrustedContent: boolean;
    onMouseOut(): void;
    create(): void;
    close(): void;
}
