import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UITooltipComponent } from './tooltip.component';
import { UITooltipDirective } from './tooltip.directive';

const meta: Meta<UITooltipComponent> = {
    title: 'Components/Popovers/Tooltip',
    component: UITooltipComponent,
    decorators: [
        moduleMetadata({
            imports: [BrowserAnimationsModule, UITooltipDirective]
        })
    ]
};
export default meta;

type Story = StoryObj<UITooltipComponent>;

export const Default: Story = {
    render: args => ({
        ...args,
        template: `
<span [uiTooltip]="'This is a tooltip'" [uiTooltipPosition]="'right'">
Hover here to see the tooltip
</span>
`
    })
};
