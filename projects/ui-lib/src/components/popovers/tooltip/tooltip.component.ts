import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
    OnChanges,
    SimpleChanges,
    ViewRef,
    inject,
    viewChild
} from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { UIPopoverDirective, UIPopoverTargetDirective, UIPopoverTemplateDirective } from '../popover';
import { IUITooltipDirective } from './tooltip.directive.interface';

@Component({
    imports: [UIPopoverDirective, UIPopoverTemplateDirective],
    selector: 'ui-tooltip',
    templateUrl: 'tooltip.component.html',
    styleUrls: ['tooltip.component.scss'],
    providers: [UIPopoverTargetDirective],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UITooltipComponent implements OnChanges {
    domSanitizer = inject(DomSanitizer);
    private changeDetector = inject(ChangeDetectorRef);

    readonly popover = viewChild.required<UIPopoverDirective>('popover');

    @Input()
    text: string;
    sanitizedText: SafeHtml;

    ngOnChanges(simpleChanges: SimpleChanges): void {
        if (simpleChanges['text'].currentValue) {
            this.sanitizeText();
        }
    }

    sanitizeText(): void {
        this.sanitizedText = this.domSanitizer.bypassSecurityTrustHtml(this.text);
        if (!(<ViewRef>this.changeDetector).destroyed) {
            this.changeDetector.detectChanges();
        }
    }

    open(host: UIPopoverTargetDirective, tooltipDirective: IUITooltipDirective): void {
        this.sanitizeText();
        const popoverRef = this.popover().open(host);
        popoverRef.componentInstance.host.nativeElement.classList.add('ui-tooltip');
        if (tooltipDirective.uiTooltipInteractive) {
            popoverRef.componentInstance.host.nativeElement.classList.add('interactive');
        }
    }

    close(): void {
        this.popover().close();
    }
}
