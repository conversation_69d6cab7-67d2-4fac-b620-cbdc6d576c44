import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, input, output } from '@angular/core';
import { UIPREnvService } from '../../../services/pr-env.service';
import { PREnvToken, PullRequestItem } from '../../../types/pr-env';
import { UIInlineLoaderComponent } from '../../graphics/inline-loader';
import { UIDropdownComponent, UIDropdownItemComponent, UIDropdownTargetDirective } from '../dropdown';
import { UITooltipDirective } from '../tooltip';

interface PullRequestItemOption extends PullRequestItem {
    selected: boolean;
}
interface RepoOption {
    repoName: string;
    hasPrSelected: boolean;
    pullRequests: PullRequestItemOption[];
}

@Component({
    imports: [
        NgTemplateOutlet,
        UIDropdownComponent,
        UIDropdownItemComponent,
        UIDropdownTargetDirective,
        UIInlineLoaderComponent,
        UITooltipDirective
    ],
    templateUrl: './pr-env-picker.component.html',
    selector: 'ui-pr-env-picker',
    styleUrl: './pr-env-picker.component.scss'
})
export class UIPREnvPickerComponent {
    private readonly uiPrEnvService = inject(UIPREnvService);
    private readonly config = inject(PREnvToken);

    reload = input(true);

    prEnvClicked = output<{ repo: string; pr: PullRequestItem }>();

    protected prEnvs = this.uiPrEnvService.prEnvs;
    protected selectedPrEnvs = this.uiPrEnvService.selectedPrEnvs;
    protected hasSelectedPrEnv = computed(() => !!this.selectedPrEnvs().length);

    protected prEnvsOptions = computed(() => this.computePrEnvsOptions());

    onRepoPrClicked(repo: string, pr: PullRequestItemOption): void {
        repo = repo.toLowerCase();

        this.prEnvClicked.emit({ repo, pr });

        if (pr.selected) {
            this.uiPrEnvService.deselectPrEnv(repo);
            return;
        }
        this.uiPrEnvService.selectPrEnv(repo, pr.formattedName.toLowerCase());
    }

    private computePrEnvsOptions(): { beRepos: RepoOption[]; feRepos: RepoOption[] } {
        const prEnvs = this.uiPrEnvService.prEnvs.value();
        if (!prEnvs?.success) {
            return { beRepos: [], feRepos: [] };
        }
        const selectedPrEnvs = this.selectedPrEnvs();
        const mapRepoToRepoOption = (currentRepo: { repoName: string }): RepoOption => {
            const pullRequestItems =
                prEnvs.data[currentRepo.repoName]?.map(pullRequestItem => ({
                    ...pullRequestItem,
                    selected: selectedPrEnvs.some(
                        ([repo, branch]) =>
                            repo === currentRepo.repoName && branch === pullRequestItem.formattedName
                    )
                })) ?? [];
            return {
                repoName: currentRepo.repoName,
                hasPrSelected: pullRequestItems.some(({ selected }) => selected),
                pullRequests: pullRequestItems
            };
        };
        const beRepos: RepoOption[] = this.config.beRepos.map(mapRepoToRepoOption);
        const feRepos: RepoOption[] = this.config.feRepos.map(mapRepoToRepoOption);
        return { beRepos, feRepos };
    }
}
