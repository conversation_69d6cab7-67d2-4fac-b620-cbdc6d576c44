<div class="wrapper">
    <button
        [class.error]="!!prEnvs.error()"
        [class.has-pr-selected]="hasSelectedPrEnv()"
        [uiTooltip]="'PR environment picker'"
        [uiTooltipPosition]="'bottom'"
        [uiDropdownTarget]="prEnvs.isLoading() ? undefined : prMenu">
        @if (prEnvs.isLoading()) {
            <ui-inline-loader />
        } @else {
            <svg
                fill="#000000"
                width="16px"
                height="16px"
                viewBox="0 0 512 512"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M192,96a64,64,0,1,0-96,55.39V360.61a64,64,0,1,0,64,0V151.39A64,64,0,0,0,192,96ZM128,64A32,32,0,1,1,96,96,32,32,0,0,1,128,64Zm0,384a32,32,0,1,1,32-32A32,32,0,0,1,128,448Z" />
                <path
                    d="M416,360.61V156a92.1,92.1,0,0,0-92-92H304V32a16,16,0,0,0-27.31-11.31l-64,64a16,16,0,0,0,0,22.62l64,64A16,16,0,0,0,304,160V128h20a28,28,0,0,1,28,28V360.61a64,64,0,1,0,64,0ZM384,448a32,32,0,1,1,32-32A32,32,0,0,1,384,448Z" />
            </svg>
        }
    </button>
    <ui-dropdown #prMenu>
        @let _prEnvsOptions = prEnvsOptions();

        @if (_prEnvsOptions.feRepos.length) {
            <span class="header"> Frontend repos </span>
            @for (repo of _prEnvsOptions.feRepos; track repo.repoName) {
                <ng-container *ngTemplateOutlet="repoOption; context: { repo: repo }" />
            }
        }

        @if (_prEnvsOptions.beRepos.length) {
            <span class="header"> Backend repos </span>
            @for (repo of _prEnvsOptions.beRepos; track repo.repoName) {
                <ng-container *ngTemplateOutlet="repoOption; context: { repo: repo }" />
            }
        }
    </ui-dropdown>
</div>

<ng-template
    #repoOption
    let-repo="repo">
    <ui-dropdown-item
        [uiDropdownTarget]="submenu"
        [svgIcon]="repo.hasPrSelected ? 'checkmark-large' : ''">
        {{ repo.repoName }}
    </ui-dropdown-item>
    <ui-dropdown #submenu>
        @for (prInfo of repo.pullRequests; track $index) {
            <ui-dropdown-item
                [svgIcon]="prInfo.selected ? 'checkmark-large' : ''"
                (click)="onRepoPrClicked(repo.repoName, prInfo)">
                <span class="name-container">
                    {{ prInfo.name }}
                </span>
            </ui-dropdown-item>
        } @empty {
            <span class="empty-pr"> No Prs yet</span>
        }
    </ui-dropdown>
</ng-template>
