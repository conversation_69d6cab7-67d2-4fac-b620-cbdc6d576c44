import { APP_BASE_HREF } from '@angular/common';
import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { Injector } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withHashLocation } from '@angular/router';
import { applicationConfig, Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { providePREnvPicker } from '../../../providers/';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';
import { UIDropdownComponent } from '../dropdown';
import { UIPREnvPickerComponent } from './pr-env-picker.component';

const meta: Meta<UIDropdownComponent> = {
    title: 'Components/PREnvPicker',
    component: UIPREnvPickerComponent,
    parameters: {
        injectInjectorToProps: true,
        controls: { disable: true }
    },
    decorators: [
        applicationConfig({
            providers: [
                provideRouter([{ path: '**', component: UIPREnvPickerComponent }], withHashLocation()),
                provideAnimationsAsync('noop'),
                providePREnvPicker({
                    beRepos: [
                        { repoName: 'studio-backend', path: 'studio' },
                        { repoName: 'comment-service-api', path: 'comment' }
                    ],
                    feRepos: [
                        { repoName: 'studio', skipRedirection: false },
                        { repoName: 'fontmanager', skipRedirection: true },
                        { repoName: 'notification-service-client', skipRedirection: true }
                    ]
                }),
                provideHttpClient(withInterceptorsFromDi())
            ]
        }),
        moduleMetadata({
            providers: [{ provide: APP_BASE_HREF, useValue: '/' }],
            imports: [UIButtonComponent]
        }),
        injectInjectorToProps()
    ]
};
export default meta;

type Story = StoryObj<UIPREnvPickerComponent>;

export const Default: Story = {
    render: () => ({
        props: {
            sendRequestToStudio: (injector: Injector) => {
                injector
                    .get(HttpClient)
                    .get('https://sandbox-api.bannerflow.com/studio/health/live')
                    .subscribe(data => {
                        console.warn('Request sent, response received:', data);
                    });
            }
        },
        template: `
<ui-pr-env-picker/>
<br/>
<ui-button text="Test Studio Backend request" (click)="sendRequestToStudio(injector)" />
`
    })
};
