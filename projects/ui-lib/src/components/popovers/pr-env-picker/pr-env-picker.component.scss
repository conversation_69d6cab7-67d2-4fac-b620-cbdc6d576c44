.wrapper {
    button {
        background: transparent;
        border-radius: 4px;

        --color: var(--ui-color-border-highlight);

        border: 1px solid var(--color);
        cursor: pointer;

        &.error {
            --color: red;
        }

        &.has-pr-selected {
            --color: green;
        }

        svg {
            fill: var(--color);

        }
    }

    .name-container {
        white-space: nowrap;
    }
}

ui-dropdown-item {
    width: 200px;

    span.type {
        margin-left: 8px;
        font-size: 12px;
        color: var(--ui-color-text-second);
    }
}

.header,
.empty-pr {
    display: flex;
    padding-left: 8px;
    min-width: 200px;
}
