import {
    ContentChild,
    Directive,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
    TemplateRef,
    inject
} from '@angular/core';
import { Subscription } from 'rxjs';
import { IUIPopoverConfig } from '../../../types';
import { UIPopoverRef } from './popover-ref';
import { UIPopoverTargetDirective } from './popover-target.directive';
import { UIPopoverTemplateDirective } from './popover-template.directive';
import { IUIPopover } from './popover.interface';
import { UIPopoverService } from './popover.service';

/**
 * Popover component. Used by tooltips and dropdow menus
 *
 * Style params (only old ui)
 * `--popover-backgroundColor` sets the background-color
 *
 * Nui Style params:
 * `--background-color` sets the background-color
 * `--border-radius` sets border-radius
 * `--border` sets border`
 * `--box-shadow` sets shadow`
 */
@Directive({
    standalone: true,
    selector: 'ui-popover',
    exportAs: 'ui-popover'
})
export class UIPopoverDirective implements On<PERSON><PERSON>roy, IUIPopover {
    private popoverService = inject(UIPopoverService);

    _config: IUIPopoverConfig;

    @Input()
    set config(config: IUIPopoverConfig) {
        if (config.openOnHover) {
            config.hasBackdrop = false;
        }
        this._config = config;
    }

    get config(): IUIPopoverConfig {
        return this._config;
    }

    // TODO: Skipped for migration because:
    //  This query overrides a field from a superclass, while the superclass field
    //  is not migrated.
    @ContentChild(UIPopoverTemplateDirective, { read: TemplateRef })
    popoverTemplate: TemplateRef<any>;

    @Output('close') onClose = new EventEmitter();

    public isPopoverOpen: boolean;

    private onCloseSubscription = Subscription.EMPTY;

    popoverRef: UIPopoverRef | undefined;

    open(target: UIPopoverTargetDirective): UIPopoverRef {
        if (this.popoverRef) {
            this.popoverRef.destroy();
        }
        this.popoverRef = this.popoverService.openTemplate(
            target.host,
            this.popoverTemplate,
            this.config
        );
        this.isPopoverOpen = true;
        target.onPopoverOpen();

        // Listen to close call
        this.onCloseSubscription = this.popoverRef.onClose.subscribe(() => {
            this.onClose.emit();
            this.isPopoverOpen = false;
            target.onPopoverClosed();
            this.onCloseSubscription.unsubscribe();
        });

        return this.popoverRef;
    }

    close(): void {
        if (this.popoverRef) {
            this.popoverRef.close();
            delete this.popoverRef;
        }
        if (this.onCloseSubscription && !this.onCloseSubscription.closed) {
            this.onCloseSubscription.unsubscribe();
        }
    }

    ngOnDestroy(): void {
        if (this.popoverRef) {
            this.popoverRef.destroy();
        }
        if (this.onCloseSubscription && !this.onCloseSubscription.closed) {
            this.onCloseSubscription.unsubscribe();
        }
    }

    updatePopoverSize(newSize: 'xs' | 'sm' | 'md'): void {
        this.popoverRef?.componentInstance.setSize(newSize);
    }

    resize({ width, height }: { width?: string; height?: string }): void {
        const element = this.popoverRef?.componentInstance?.host?.nativeElement;
        if (!element) {
            return;
        }
        if (width) {
            element.parentElement!.style.width = 'auto';
            element.style.width = width;
        }
        if (height) {
            element.parentElement!.style.height = 'auto';
            element.style.height = height;
        }
    }
}
