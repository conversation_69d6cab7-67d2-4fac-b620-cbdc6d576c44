import { Directive, ElementRef, HostBinding } from '@angular/core';

@Directive({
    standalone: true,
    selector: '[ui-popover-target]',
    exportAs: 'ui-popover-target'
})
export class UIPopoverTargetDirective {
    @HostBinding('class.popover-active')
    public isPopoverActive: boolean;

    // host cannot be refactored, it breaks when injected
    constructor(public host: ElementRef) {}

    onPopoverOpen(): void {
        this.isPopoverActive = true;
    }

    onPopoverClosed(): void {
        this.isPopoverActive = false;
    }
}
