import { AnimationEvent } from '@angular/animations';
import { ElementRef, EventEmitter, Signal, ViewContainerRef } from '@angular/core';
import { IUIPopoverConfig } from '../../../types/popover';

export interface IUIPopoverComponent {
    animationState: 'void' | 'enter' | 'leave';
    config: IUIPopoverConfig;
    host: ElementRef<HTMLElement>;
    viewContainerRef: Signal<ViewContainerRef | undefined>;

    animationStateChanged: EventEmitter<AnimationEvent>;

    startExitAnimation(): void;
    close(): void;

    setSize(newSize: 'xs' | 'sm' | 'md'): void;
}
