:where(:root[data-uinew]) :host {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    align-items: stretch;
    padding: 0;
    overflow: hidden;
    width: 100%;

    // Common popover
    --background-color: var(--nui-fill-neutral-boldest);
    --border-radius: var(--nui-border-radius-xs, 2px);
    --border: 1px solid var(--nui-fill-neutral-boldest);
    --border-top: 1px solid var(--nui-fill-neutral-boldest);
    --box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 10%);
    --width: 100%;

    // Popover for dropdown
    &.ui-dropdown {
        --background-color: var(--nui-menu-fill-primary-default, #fff);
        --border-radius: var(--nui-menu-radius, 8px);
        --border: var(--nui-border-width-small, 1px) solid var(--nui-menu-border-primary-default, #cdced7);
        --border-top: var(--nui-border-width-small, 1px) solid var(--nui-menu-border-primary-default, #cdced7);
        --box-shadow: 0px 5px 20px 0px rgba(0, 8, 48, 27%);
        --width: 340px;

        display: flex;
        width: 340px;
        padding: var(--nui-menu-space-padding-vertical, 0) var(--nui-menu-space-padding-horizontal, 0);
        flex-direction: column;
        align-items: flex-start;
        overflow: hidden auto;
    }

    // Popover for select input
    &.ui-select {
        display: flex;
        padding: var(--nui-menu-space-padding-vertical, 0) var(--nui-menu-space-padding-horizontal, 0);
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;

        --width: 100%;
        --border-radius: 0px 0px var(--nui-menu-radius, 8px) var(--nui-menu-radius, 8px);
        --border: var(--nui-border-width-small, 1px) solid var(--nui-menu-border-primary-selected, #005eeb);
        --border-top: 0;
        --background-color: var(--nui-menu-item-fill-primary-default, #fff);
        --box-shadow: 0px 3px 10px 0px rgba(0, 6, 46, 20%);
        --clip-path: inset(0px -10px -10px -10px);

        &.content-loaded>.content {
            overflow-y: auto;
        }

        &.ui-select-secondary {
            --background-color: var(--nui-menu-item-fill-secondary-default, #EFF0F3);
            --box-shadow: none;
        }

        &.ui-select-invalid {
            --border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-primary-error, #C72B47);
        }
    }

    // Popover for tooltip
    &.ui-tooltip {
        color: var(--nui-tooltip-text-default);

        &:not(.interactive) {
            pointer-events: none;

            * {
                pointer-events: none;
            }
        }

        &.interactive {
            pointer-events: auto;
        }
    }

    // Popover with no background
    &.transparent {
        --background-color: transparent;
        --border-radius: 0;
        --border: none;
        --border-top: none;
        --box-shadow: none;
    }

    // Card popover
    &.ui-card-popover {
        --background-color: var(--nui-card-fill-default);
        --border-radius: var(--nui-card-radius, 12px);
        --border: var(--nui-border-width-small, 1px) solid var(--nui-card-border-default, #cdced7);
        --border-top: var(--nui-border-width-small, 1px) solid var(--nui-card-border-default, #cdced7);
        --box-shadow: var(--nui-shadow-subtle-x) var(--nui-shadow-subtle-y) var(--nui-shadow-subtle-blur) var(--nui-shadow-subtle-spread) var(--nui-shadows-subtle);

        padding: var(--nui-card-space-padding-vertical, 16px) var(--nui-card-space-padding-horizontal, 16px);
    }

    &.content-loaded {
        background-color: var(--background-color);
        border: var(--border);
        border-top: var(--border-top);
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        width: var(--width);
        clip-path: var(--clip-path, 'none');

        & > .content {
            width: inherit;
        }
    }

    &.arrow {
        overflow: visible;

        &::after,
        &::before {
            width: 0;
            height: 0;
            display: block;
            content: '';
            position: absolute;
        }

        &-top {
            margin-top: 12px;

            &::after,
            &::before {
                top: -5px;
                transform: translateX(-50%);
                left: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--background-color);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--background-color);
                top: -6px;
                transform: translateX(-50%) scale(1.2);
            }
        }

        &-bottom {
            margin-bottom: 7px;

            &::after,
            &::before {
                bottom: -5px;
                transform: translateX(-50%) rotate(180deg);
                left: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--background-color);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--background-color);
                bottom: -6px;
                transform: translateX(-50%) scale(1.2) rotate(180deg);
            }
        }

        &-left {
            margin-left: 7px;

            &::after,
            &::before {
                transform: translateY(-50%) rotate(-90deg);
                left: -7px;
                top: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--background-color);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--background-color);
                left: -8px;
                transform: translateY(-50%) scale(1.2) rotate(-90deg);
            }
        }

        &-right {
            margin-right: 7px;

            &::after,
            &::before {
                transform: translateY(-50%) rotate(90deg);
                right: -7px;
                top: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--background-color);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--background-color);
                right: -8px;
                transform: translateY(-50%) scale(1.2) rotate(90deg);
            }
        }
    }

    .content {
        &.ui-tooltip {
            padding: var(--nui-space-100, 4px) var(--nui-space-200, 8px);
            word-wrap: break-word;
        }
    }
}
