import { CdkScrollableModule } from '@angular/cdk/scrolling';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UIButtonComponent } from '../../buttons';
import { UISVGIconComponent } from '../../icon';
import { UIPopoverTemplateDirective } from './popover-template.directive';
import { UIPopoverComponent } from './popover.component';
import { UIPopoverDirective } from './popover.directive';
import { UIPopoverTargetDirective } from './popover-target.directive';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UIPopoverComponent> = {
    title: 'Components/Popovers/Popover',
    component: UIPopoverComponent,
    decorators: [
        moduleMetadata({
            imports: [
                CdkScrollableModule,
                UIPopoverTemplateDirective,
                UIPopoverComponent,
                UIPopoverDirective,
                UIButtonComponent,
                UIPopoverTargetDirective,
                BrowserAnimationsModule,
                UISVGIconComponent,
                UIComponentSizeDirective
            ]
        })
    ]
};
export default meta;

type Story = StoryObj<UIPopoverComponent>;

export const Default: Story = {
    args: {},
    render: args => ({
        ...args,
        template: `
<div
    class="hover-area"
    (mouseover)="hoverPopoverRoot.open(hoverTarget)"
    ui-popover-target
    #hoverTarget="ui-popover-target">
Hover here to see the popover
</div>
<ui-popover
    #hoverPopoverRoot="ui-popover"
    [config]="{
        arrowPosition: 'top',
        closeButton:true,
        position: 'bottom',
        maxWidth: 500,
        width: 'auto',
        backdropClickClose: true,
        hasBackdrop: true,
        popoverType: 'menu',
    }">
    <ng-template ui-popover-template>
        <div class="wrapper with-content">
            <h3 [uiSize]="'sm'"> This is a popover </h3>
            <span> Press ESC to close </span>
            <span> Or click the backdrop </span>
        </div>
    </ng-template>
</ui-popover>
`
    })
};
