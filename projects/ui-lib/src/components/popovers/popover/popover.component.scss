@use '../../../style/mixins';

:where(:root:not([data-uinew])) :host {
    --popover-backgroundColor: var(--ui-color-surface);

    position: relative;
    display: flex;
    flex-flow: column nowrap;
    align-items: stretch;
    padding: 0;
    overflow: hidden;
    width: 100%;

    &.content-loaded {
        box-shadow: var(--ui-box-shadow);
        border: 1px solid var(--ui-color-border);
        border-radius: var(--ui-border-radius);
        background-color: var(--ui-color-surface);
    }

    &.ui-tooltip {
        &:not(.interactive) {
            pointer-events: none;

            * {
                pointer-events: none;
            }
        }

        &.interactive {
            pointer-events: auto;
        }
    }

    &.discrete {
        background: var(--ui-color-grey-61);
        color: #fff;
        border-radius: 2px;

        --ui-color-border: var(--ui-color-grey-61);
        --ui-color-surface: var(--ui-color-grey-61);

        .ui-tooltip {
            padding: 4px 8px;
        }
    }

    &.menu {
        box-shadow: var(--menu-box-shadow);
        border: 1px solid var(--menu-border-color);
        border-radius: var(--menu-border-radius);
    }

    &.arrow {
        overflow: visible;

        &::after,
        &::before {
            width: 0;
            height: 0;
            display: block;
            content: '';
            position: absolute;
        }

        &-top {
            margin-top: 12px;

            &::after,
            &::before {
                top: -5px;
                transform: translateX(-50%);
                left: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--ui-color-surface);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--ui-color-border);
                top: -6px;
                transform: translateX(-50%) scale(1.2);
            }
        }

        &-bottom {
            margin-bottom: 7px;

            &::after,
            &::before {
                bottom: -5px;
                transform: translateX(-50%) rotate(180deg);
                left: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--ui-color-surface);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--ui-color-border);
                bottom: -6px;
                transform: translateX(-50%) scale(1.2) rotate(180deg);
            }
        }

        &-left {
            margin-left: 7px;

            &::after,
            &::before {
                transform: translateY(-50%) rotate(-90deg);
                left: -7px;
                top: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--ui-color-surface);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--ui-color-border);
                left: -8px;
                transform: translateY(-50%) scale(1.2) rotate(-90deg);
            }
        }

        &-right {
            margin-right: 7px;

            &::after,
            &::before {
                transform: translateY(-50%) rotate(90deg);
                right: -7px;
                top: 50%;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid var(--ui-color-surface);
            }

            &::before {
                border-width: 5px;
                border-bottom: 5px solid var(--ui-color-border);
                right: -8px;
                transform: translateY(-50%) scale(1.2) rotate(90deg);
            }
        }
    }

    .content {
        padding: var(--ui-padding);
        position: relative;
        width: 100%;
        overflow: auto;
        line-height: var(--ui-line-height);
        max-width: var(--maxWidth);

        @include mixins.scrollbar(var(--ui-static-color-grey-84));

        &.no-padding {
            padding: 0;
        }

        &.ui-tooltip {
            padding: var(--padding);
            word-wrap: break-word;
        }
    }

    ::ng-deep {
        .cdk-overlay-pane.ui-tooltip {
            pointer-events: unset !important;
        }
    }
}
