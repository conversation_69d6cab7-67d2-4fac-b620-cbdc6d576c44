:where(:root[data-uinew]) :host {
    display: inline-flex;
    height: var(--nui-chips-height, 24px);
    max-width: 450px;
    padding: var(--nui-chips-space-padding-vertical, 2px) var(--nui-chips-space-padding-horizontal, 8px);
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: var(--nui-chips-radius, 2px);
    border: var(--nui-chips-border, 0.5px) solid transparent;
    cursor: pointer;

    // TODO: Remove this when --nui-chips-height has correct value
    &[data-uisize='sm'] {
        height: fit-content;
    }

    ui-label {
        text-overflow: clip;
        overflow: hidden;
        white-space: nowrap;
    }

    /* Elevation/Drop Shadow/Subtlest */
    box-shadow: 0 4px 8px 0 rgba(0, 0, 51, 6%);

    &.primary {
        border-color: var(--nui-chips-border-primary-default, #cdced7);
        background: var(--nui-chips-fill-primary-default, #fff);

        &:hover {
            border-color: var(--nui-chips-border-primary-default-hover, #cdced7);
            background: var(--niu-chips-fill-primary-default-hover, #eff0f3);
        }

        &.selected {
            border-color: var(--nui-chips-border-primary-selected, rgba(255, 255, 255, 0%));
            background: var(--nui-chips-fill-primary-selected, #006cfd);

            &:hover {
                border-color: var(--nui-chips-border-primary-selected-hover, rgba(255, 255, 255, 0%));
                background: var(--nui-chips-fill-primary-selected-hover, #005eeb);
            }
        }
    }

    &.secondary {
        border-color: var(--nui-chips-border-secondary-default, #cdced7);
        background: var(--nui-chips-fill-secondary-default, #eff0f3);

        &:hover {
            border-color: var(--nui-chips-border-secondary-default-hover, #cdced7);
            background: var(--nui-chips-fill-secondary-default-hover, #e7e8ec);
        }

        &.selected {
            border-color: var(--nui-chips-border-secondary-selected, rgba(255, 255, 255, 0%));
            background: var(--nui-chips-fill-secondary-selected, #1e1f24);

            &:hover {
                border-color: var(--nui-chips-border-secondary-selected-hover, #eff0f3);
                background: var(--nui-chips-fill-secondary-selected-hover, #1e1f24);
            }
        }
    }
}
