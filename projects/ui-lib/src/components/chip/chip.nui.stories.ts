import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIChipComponent } from './chip.component';
import { UIComponentSizeDirective } from '../../directives';

const meta: Meta<UIChipComponent & { text: string }> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Chips',
    component: UIChipComponent,
    decorators: [moduleMetadata({ imports: [UIChipComponent, UIComponentSizeDirective] })],
    argTypes: {
        text: { type: 'string' },
        nuiIcon: { type: 'string' },
        size: { control: 'inline-radio', options: ['sm', 'md'] }
    },
    parameters: {
        controls: {
            include: ['type', 'size', 'nuiIcon', 'text']
        }
    }
};

export default meta;
type Story = StoryObj<UIChipComponent & { text: string }>;

export const NuiChip: Story = {
    args: {
        text: 'Chip',
        size: 'md',
        nuiIcon: 'check'
    },
    render: args => ({
        args,
        props: args,
        styles: ['ui-chip{margin-bottom: 24px;}'],
        template: `
<h3 [uiSize]="'sm'">Selected:</h3>
<br/>
<ui-chip
    [size]="size"
    [type]="'primary'"
    [selected]="true"
    [nuiIcon]="nuiIcon"
    >{{text}}</ui-chip>
<br/>
<ui-chip
    [size]="size"
    [type]="'secondary'"
    [selected]="true"
    [nuiIcon]="nuiIcon"
>{{text}}</ui-chip>
<br/>
<h3 [uiSize]="'sm'">Not selected:</h3>
<br/>
<ui-chip
    [size]="size"
    [type]="'primary'"
    [selected]="false"
    [nuiIcon]="nuiIcon"
    >{{text}}</ui-chip>
<br/>
<ui-chip
    [size]="size"
    [type]="'secondary'"
    [selected]="false"
    [nuiIcon]="nuiIcon"
>{{text}}</ui-chip>
`
    })
};
