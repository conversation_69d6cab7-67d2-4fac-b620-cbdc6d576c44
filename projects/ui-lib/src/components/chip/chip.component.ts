import { Component, computed, input, output } from '@angular/core';
import { DATASET_SIZE } from '../../services';
import { UINUIIcon } from '../../types';
import { UILabelComponent, UILabelType } from '../label';

export type UIChipType = 'primary' | 'secondary';

/**
 * Chip component only for NUI
 */
@Component({
    imports: [UILabelComponent],
    selector: 'ui-chip',
    template: `<ui-label
        [size]="labelSize()"
        [type]="labelType()"
        [leadingIcon]="nuiIcon()"
        (leadingIconClick)="chipClick.emit($event)"
        (trailingIconClick)="closeClick.emit($event)"
        trailingIcon="close_small">
        <ng-content></ng-content>
    </ui-label>`,
    styleUrl: './chip.new.component.scss',
    host: {
        '[class]': 'type()',
        '[class.selected]': 'selected()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(click)': 'chipClick.emit()'
    }
})
export class UIChipComponent {
    type = input<UIChipType>('primary');

    /**
     * NUI size
     */
    size = input<'sm' | 'md'>('md');

    /**
     * NUI size for label
     */
    labelSize = input<'sm' | 'md'>('sm');

    /**
     * Leading icon, if any
     */
    nuiIcon = input<UINUIIcon>();

    selected = input(false);

    chipClick = output<MouseEvent>();
    closeClick = output<MouseEvent>();

    protected labelType = computed<UILabelType>(() => (this.selected() ? 'inverted' : 'primary'));
}
