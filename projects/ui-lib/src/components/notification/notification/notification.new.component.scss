:where(:root[data-uinew]) :host {
    --main-color: var(--nui-snackbar-border-info, #008ffe);
    --accent-color: var(--nui-snackbar-fill-info, #cce9ff);

    /* Positioning */
    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;

    /* Display and layout */
    display: flex;
    justify-content: flex-start;
    align-items: center;

    /* Box model */
    padding: var(--nui-snackbar-space-padding-vertical, 8px)
        var(--nui-snackbar-space-padding-horizontal, 12px);
    border: 1px solid var(--main-color);
    border-radius: var(--nui-border-radius-medium, 8px);

    /* Visual */
    background: var(--accent-color);
    color: var(--nui-text-primary);

    /* Typography */
    font-size: var(--nui-font-size-350, 14px);
    font-weight: var(--nui-font-font-weight-12500, 500);
    line-height: var(--nui-font-line-height-500, 20px);

    /* Animation */
    animation-duration: 0.2s;
    animation-fill-mode: forwards;

    &.success {
        --main-color: var(--nui-snackbar-border-success, #5cbfa6);
        --accent-color: var(--nui-snackbar-fill-success, #c6e9e0);
    }

    &.warning {
        --main-color: var(--nui-snackbar-border-warning, #f7cc7e);
        --accent-color: var(--nui-snackbar-fill-warning, #fcedd2);
    }

    &.error {
        --main-color: var(--nui-snackbar-border-error, #e17181);
        --accent-color: var(--nui-snackbar-fill-error, #f3c6cd);
    }

    .icon {
        cursor: pointer;
    }

    &.top {
        bottom: unset;
        top: 6rem;
    }

    &.left {
        left: 8%;
    }

    &.right {
        left: 92%;
    }

    .wrapper-notification {
        display: flex;
        flex-direction: column;
        width: 100%;

        .text {
            margin-right: 0.2rem;
        }

        .notification {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .checkbox {
            margin-top: 1rem;
        }
    }

    @keyframes notificationInTop {
        from {
            transform: translateY(-20px) translateX(-50%);
            opacity: 0;
        }

        to {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }
    }

    @keyframes notificationOutTop {
        from {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }

        to {
            transform: translateY(-20px) translateX(-50%);
            opacity: 0;
        }
    }

    @keyframes notificationInBottom {
        from {
            transform: translateY(20px) translateX(-50%);
            opacity: 0;
        }

        to {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }
    }

    @keyframes notificationOutBottom {
        from {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }

        to {
            transform: translateY(20px) translateX(-50%);
            opacity: 0;
        }
    }
}
