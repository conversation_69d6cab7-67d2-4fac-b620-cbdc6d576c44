import { CommonModule } from '@angular/common';
import { Injector } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UINotificationComponent } from './notification.component';
import { UINotificationService } from './notification.service';

const meta: Meta<UINotificationComponent> = {
    title: 'Components/Notification/Notification',
    component: UINotificationComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UINotificationComponent,
                UIIconComponent,
                UISVGIconComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                BrowserAnimationsModule
            ]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true
    }
};
export default meta;
type Story = StoryObj<UINotificationComponent>;

export const Notification: Story = {
    render: args => ({
        props: {
            args,
            openError: (injector: Injector): void => {
                injector
                    .get<UINotificationService>(UINotificationService)
                    .open('Hello!', { type: 'error' });
            },
            openSuccess: (injector: Injector): void => {
                injector
                    .get<UINotificationService>(UINotificationService)
                    .open('Hello', { type: 'success' });
            },
            openWarning: (injector: Injector): void => {
                injector
                    .get<UINotificationService>(UINotificationService)
                    .open('Hello', { type: 'warning' });
            },
            openInfo: (injector: Injector): void => {
                injector
                    .get<UINotificationService>(UINotificationService)
                    .open('Hello', { type: 'info' });
            },
            openDialog1: (injector: Injector): void => {
                injector.get<UINotificationService>(UINotificationService).open('Hello', {
                    type: 'info',
                    placement: 'bottom',
                    icon: 'close',
                    autoCloseDelay: 20000,
                    hideCloseIcon: true,
                    width: 58
                });
            }
        },
        template: `
                <button (click)="openError(injector)">Error </button>
                <button (click)="openSuccess(injector)">Success </button>
                <button (click)="openWarning(injector)">Warning </button>
                <button (click)="openInfo(injector)">Info </button>
                <button (click)="openDialog1(injector)">Custom </button>
        `
    })
};
