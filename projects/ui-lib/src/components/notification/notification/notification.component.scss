:where(:root:not([data-uinew])) :host {
    --main-color: var(--ui-color-text);
    --accent-color: var(--ui-color-background);

    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    width: var(--width);
    border: 1px solid var(--main-color);
    border-radius: var(--ui-border-radius);
    font-weight: var(--ui-font-weight-bold);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: var(--padding);
    background: var(--accent-color);
    box-shadow: var(--ui-box-shadow);
    color: var(--main-color);
    z-index: 10000;
    animation-duration: 0.2s;
    animation-fill-mode: forwards;

    &.error {
        --main-color: var(--ui-color-alert);
        --accent-color: var(--ui-color-surface-alert);

        cursor: default;

        .icon {
            cursor: pointer;
        }
    }

    &.success {
        --main-color: var(--ui-color-success);
        --accent-color: var(--ui-color-selected-background);
    }

    &.warning {
        --main-color: var(--ui-color-warning);
        --accent-color: var(--ui-color-surface-warning);
    }

    &.top {
        bottom: unset;
        top: 6rem;
    }

    &.left {
        left: 8%;
    }

    &.right {
        left: 92%;
    }

    .wrapper-notification {
        display: flex;
        flex-direction: column;
        width: 100%;

        .text {
            margin-right: 0.2rem;
        }

        .notification {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .checkbox {
            margin-top: 1rem;
        }
    }

    @keyframes notificationInTop {
        from {
            transform: translateY(-20px) translateX(-50%);
            opacity: 0;
        }

        to {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }
    }

    @keyframes notificationOutTop {
        from {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }

        to {
            transform: translateY(-20px) translateX(-50%);
            opacity: 0;
        }
    }

    @keyframes notificationInBottom {
        from {
            transform: translateY(20px) translateX(-50%);
            opacity: 0;
        }

        to {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }
    }

    @keyframes notificationOutBottom {
        from {
            transform: translateY(0) translateX(-50%);
            opacity: 1;
        }

        to {
            transform: translateY(20px) translateX(-50%);
            opacity: 0;
        }
    }
}
