import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UILoaderComponent } from '../../loader';
import { UIStickyNotificationComponent } from './sticky-notification.component';

const meta: Meta<UIStickyNotificationComponent> = {
    title: 'Components/Notification/StickyNotification',
    component: UIStickyNotificationComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIStickyNotificationComponent,
                UILoaderComponent,
                UIIconComponent,
                UISVGIconComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                BrowserAnimationsModule
            ]
        })
    ]
};
export default meta;

type Story = StoryObj<UIStickyNotificationComponent>;

export const StickyNotification: Story = {
    args: {
        type: 'success'
    },
    render: args => ({
        props: args,
        template: `
<div class="wrapper">
    <ui-sticky-notification [type]="type" class="notification">
        <ui-loader class="loader"></ui-loader>
        <div> Moving... </div>
    </ui-sticky-notification>
    <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi turpis felis, porttitor eu enim at, dignissim commodo augue. Sed turpis massa, semper lobortis consectetur eget, bibendum ut ex. Proin efficitur ipsum eu tincidunt volutpat. Praesent dapibus, nisl quis condimentum imperdiet, tortor est egestas ipsum, vel blandit libero felis et eros. Vivamus neque ligula, pretium molestie auctor sed, sodales in dui. Vivamus a nunc enim. Mauris vitae mollis libero, vel imperdiet libero. Morbi imperdiet, dolor vitae elementum commodo, metus tortor aliquam elit, auctor lobortis nunc eros at libero. Maecenas laoreet condimentum ligula nec consectetur. Aliquam auctor, neque eget ornare egestas, sapien justo congue est, eget cursus lectus quam id arcu. Sed aliquam, leo ac porttitor mattis, lectus nisl fringilla ex, ut commodo justo augue nec lacus. Cras in sagittis ligula. Mauris eu consequat lorem, vel auctor nunc. Phasellus fringilla, turpis in auctor hendrerit, tortor elit pulvinar odio, vitae molestie arcu dolor non nunc. Aliquam scelerisque sem et urna sollicitudin pellentesque. Phasellus dui arcu, luctus ac magna at, vehicula consectetur nisi.
    </p>
    <p>
    Pellentesque pharetra luctus consequat. Aliquam fermentum suscipit nisi at placerat. Etiam consectetur elit enim, a iaculis dui varius sed. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc quis ultricies nibh. Proin consequat est vel nunc porta, eu ultricies felis egestas. Aenean tincidunt maximus elit nec congue. Duis sit amet tristique tellus, at rutrum velit. Duis tempor elit eros, id accumsan elit pellentesque ut. Sed mattis purus eu mattis feugiat. Suspendisse sapien augue, malesuada sit amet ante ut, consectetur pellentesque leo. Cras volutpat erat ante, a tempor lorem consectetur quis.
    </p>
    <p>
    Duis facilisis pretium quam non condimentum. Ut congue magna a est vulputate, in iaculis urna maximus. Mauris pharetra purus tortor, sed dictum ipsum egestas nec. Quisque efficitur ultricies blandit. Nam tempor nibh leo, id posuere nulla auctor ac. Integer purus lacus, lobortis id volutpat ac, gravida eu quam. Sed in erat ex. Morbi augue sapien, consectetur ac congue ac, vehicula a lacus. Pellentesque consequat velit metus. Donec nec accumsan turpis, ut imperdiet ligula.
    </p>
    <p>
    Sed at leo massa. Praesent convallis nec neque vitae aliquam. Suspendisse euismod egestas pretium. Integer porttitor in nibh id convallis. Curabitur lacinia dolor quis sapien auctor sodales. Maecenas nec eros ut ex semper feugiat. Vestibulum efficitur felis nisl, vel feugiat elit sodales in. Sed eu vestibulum ex, et dapibus nisl. Pellentesque vulputate ornare volutpat. Aliquam at scelerisque nunc, sit amet sodales nisl. Ut mattis egestas purus. Fusce ultricies elit enim, mattis sollicitudin nibh venenatis nec.
    </p>
    <p>
    Fusce viverra, nisi a scelerisque consectetur, dui augue ullamcorper diam, sit amet bibendum ex orci nec ante. Phasellus mattis libero eu tellus elementum, sit amet congue dui vulputate. Cras vitae justo enim. Vivamus eros magna, consectetur at euismod et, pretium ut nulla. Aliquam condimentum eros vitae odio suscipit ultricies. Aliquam tempor justo ut velit luctus, a consequat est volutpat. In hac habitasse platea dictumst. Donec eu nisl eleifend elit rutrum pulvinar. Nullam eu gravida turpis. Cras congue, velit vel facilisis placerat, nulla augue ornare erat, et eleifend nulla neque id dolor. Integer urna dolor, laoreet vitae est eu, vehicula congue metus. Aliquam condimentum magna quis vulputate ultricies.
    </p>
    <p>
    Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed tempor massa ante, eget suscipit felis hendrerit eget. Mauris mattis lacus non lorem tempor accumsan. Nunc tellus lorem, laoreet nec ex id, interdum accumsan dolor. Vivamus dignissim at ligula quis lacinia. Quisque ut urna nec dolor suscipit varius sit amet vitae augue. Mauris sagittis tincidunt sem. Maecenas viverra euismod elementum. Integer convallis imperdiet metus nec tincidunt. Nunc facilisis in tortor et tincidunt. Nulla massa mauris, lobortis sed facilisis non, suscipit vel dolor. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Vestibulum ac commodo nulla, nec ullamcorper leo. Etiam venenatis tortor ut lectus maximus hendrerit. Sed consectetur hendrerit leo id sodales.
    </p>
    <p>
    Suspendisse eget sem id lacus cursus faucibus. Nam vel efficitur nisl. Morbi quam dolor, ultrices sit amet mi ultrices, porta lobortis tortor. Mauris non magna ut lacus scelerisque condimentum. Proin eleifend id nulla nec varius. Nam et arcu sed risus ornare dictum. Nam tristique arcu tristique scelerisque condimentum. Cras justo neque, egestas vel faucibus ac, ultrices sit amet mauris. Etiam tristique ligula augue, at varius arcu ultricies sed. Morbi venenatis est ac metus iaculis aliquet. Nunc sed ante id arcu hendrerit tincidunt et a tellus. Etiam sed imperdiet arcu. Aenean lacus eros, ullamcorper sollicitudin urna sed, hendrerit accumsan ex. Pellentesque hendrerit, nunc sit amet tincidunt aliquet, ex felis ultricies nulla, hendrerit lacinia libero ante at lacus. Morbi pulvinar cursus scelerisque. Quisque euismod mollis accumsan.
    </p>
    <p>
    Ut tincidunt maximus libero. Donec suscipit libero ut lacinia placerat. Integer et libero nec mauris vulputate convallis eget aliquam nisi. Nam eleifend, turpis condimentum condimentum tempor, tortor magna finibus odio, quis malesuada elit nisl nec libero. Aenean id ultrices nisi. Phasellus ac pulvinar risus. Praesent ante mauris, mattis ut lectus eu, fermentum venenatis orci. Quisque semper dictum aliquam. Mauris dictum nisi neque, in porttitor neque cursus in. Donec at aliquam ante. Suspendisse sed urna nec metus facilisis aliquam at vel elit. In volutpat fringilla felis, tempor ullamcorper turpis tristique id. Aenean sagittis, risus sed commodo molestie, ex est pellentesque erat, ut dignissim dui ligula id ligula. Pellentesque sed justo quis tortor ultrices facilisis. Integer mollis fringilla felis, in feugiat metus blandit nec. Vestibulum eleifend justo ipsum, vel vehicula leo fringilla vel.
    </p>
    <p>
    Nam id tortor placerat, pellentesque felis in, aliquam ante. Morbi ultrices, nisi sit amet fermentum dapibus, neque est iaculis erat, sit amet malesuada tellus purus congue libero. Duis lobortis, dui ut vulputate tristique, nisl leo tincidunt augue, at laoreet libero mi non dui. Curabitur faucibus, lectus vitae tempus finibus, nulla nunc auctor erat, eleifend efficitur arcu tellus eget ex. Praesent et vehicula massa, viverra euismod diam. Curabitur sed tellus id sem ultricies luctus. Pellentesque ut ullamcorper mi, ac faucibus eros. Sed id lacinia nisl. Donec mattis arcu nec ipsum laoreet finibus. Pellentesque suscipit vel elit nec iaculis. Sed euismod semper magna cursus rhoncus. Vestibulum tincidunt nulla lacus, et scelerisque felis mollis et. Nunc velit mauris, lobortis id pellentesque eu, mollis sed massa. Cras sit amet purus luctus metus consectetur tempor. Aliquam est ipsum, lobortis id risus sit amet, interdum aliquet leo.
    </p>
    <p>
    Cras maximus scelerisque ligula non iaculis. Aliquam sodales nisi convallis urna convallis porttitor quis a arcu. Vestibulum ex leo, bibendum nec placerat a, tincidunt sed nisi. Fusce gravida vulputate tristique. Nunc nec mollis quam. Vestibulum felis ante, vulputate eget blandit eu, gravida sed est. Mauris quis efficitur tortor, a iaculis dolor
    </p>
</div>
    `,
        styles: [
            `
.wrapper {
    max-height: 400px
    overflow-y: scroll;
}
.notification {
    display: flex;
    position: sticky;
    top: 0;
}
.loader {
    width: auto;
    position: relative;
    margin-right: 5px;
}
    `
        ]
    })
};
