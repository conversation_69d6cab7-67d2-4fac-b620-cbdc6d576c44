.sticky-notification {
    --background-color: var(--ui-color-background);
    --border-color: var(--ui-color-text);
    --text-color: var(--ui-color-text);
    --width: 100%;
    --margin-right: 0;
    --font-size: 1.2rem;
    --line-height: 1.4rem;

    background-color: var(--background-color);
    border-bottom: 0.1rem solid var(--border-color);
    color: var(--text-color);
    width: var(--width);
    margin-right: var(--margin-right);
    padding: 0.8rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: var(--font-size);
    line-height: var(--line-height);

    &.success {
        --background-color: var(--ui-color-selected-background);
        --border-color: var(--ui-color-primary);
        --text-color: var(--ui-color-primary);
    }

    &.error {
        --background-color: var(--ui-color-surface-alert);
        --border-color: var(--ui-color-alert);
        --text-color: var(--ui-color-alert);
    }

    &.warning {
        --background-color: var(--ui-color-surface-warning);
        --border-color: var(--ui-color-warning);
        --text-color: var(--ui-color-warning);
    }
}
