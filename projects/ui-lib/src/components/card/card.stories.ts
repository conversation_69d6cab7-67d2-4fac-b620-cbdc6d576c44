import { moduleMetadata } from '@storybook/angular';
import type { <PERSON>a, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIButtonComponent } from '../buttons/button/button.component';
import { UiCardImports } from './ui-card.exports';
import { UIComponentSizeDirective } from '../../directives';

interface CardStoryArgs {
    uiCardTitle?: string;
    uiCardContent?: string;
    uiCardFooter?: string;
    size?: string;
    buttonText?: string;
    showButton?: boolean;
    selected?: boolean;
    hoverable?: boolean;
    inset?: boolean;
}

const meta: Meta<CardStoryArgs> = {
    title: 'NUI/Card',
    decorators: [
        moduleMetadata({
            imports: [...UiCardImports, UIButtonComponent, UIComponentSizeDirective]
        })
    ],
    ...NUI_STORY_SETTINGS,
    argTypes: {
        uiCardTitle: { control: 'text', description: 'The title for the card header.' },
        uiCardContent: { control: 'text', description: 'The main content of the card.' },
        uiCardFooter: { control: 'text', description: 'Text to display in the card footer.' },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        buttonText: { control: 'text', description: 'Text for the button in the card footer.' },
        showButton: { control: 'boolean', description: 'Whether to show the button in the footer.' },
        selected: { control: 'boolean', description: 'Whether the card is selected.' },
        inset: { control: 'boolean', description: 'Whether the card is inset.' },
        hoverable: { control: 'boolean', description: 'Whether the card is hoverable.' }
    },
    args: {
        hoverable: false,
        selected: false,
        inset: false,
        size: 'md'
    },
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
        <style>
            h6 {
                margin: 16px 0;
            }
        </style>
<div uiCard style="width: 350px;" [size]="size" [inset]="inset" [selected]="selected" [hoverable]="hoverable">
        <div uiCardHeader>
            <h4 uiCardTitle>{{ uiCardTitle }}</h4>
        </div>

        <div uiCardContent>
            <p>{{ uiCardContent }}</p>
        </div>

        <div uiCardFooter style="display:flex; justify-content: space-between;">
                <p [uiSize]="'sm'">{{ uiCardFooter }}</p>
                @if(showButton) {
                    <ui-button [text]="buttonText" type="primary"></ui-button>
                }
        </div>
</div>
`
    })
};

export default meta;
type Story = StoryObj<CardStoryArgs>;

export const DefaultCard: Story = {
    args: {
        uiCardTitle: 'Default Card Title',
        uiCardContent:
            'This is the main uiCardContent of the card. It can be filled with any relevant information or components.',
        uiCardFooter: 'Footer information',
        buttonText: 'Action',
        showButton: true
    }
};

export const CardWithHeaderAndContent: Story = {
    args: {},
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;">
        <div uiCardHeader>
            <h4 uiCardTitle>Card with Header & Content</h4>
        </div>

        <div uiCardContent>
            <p>This card only has a header and uiCardContent section. No footer is present.</p>
        </div>
</div>
`
    })
};

export const CardWithContentAndFooter: Story = {
    args: {},
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;">
        <div uiCardContent>
            <p>This card has uiCardContent and a footer, but no header section.</p>
        </div>

        <div uiCardFooter style="display:flex; justify-content: space-between;">
            <p [uiSize]="'sm'">Footer without header</p>
            <ui-button text="Proceed" type="primary"></ui-button>
        </div>
</div>
`
    })
};

export const ContentOnlyCard: Story = {
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;">
        <p uiCardContent>
            This card displays only the uiCardContent section. Ideal for simple information display.
        </p>
</div>
`
    })
};

export const CardSelected: Story = {
    args: {},
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;" [selected]="true">
        <div uiCardHeader>
            <h4 uiCardTitle>Card with Header & Content</h4>
        </div>

        <div uiCardContent>
            <p>This card only has a header and uiCardContent section. No footer is present.</p>
        </div>
</div>
`
    })
};

export const CardInset: Story = {
    args: {},
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;" [inset]="true">
        <div uiCardHeader>
            <h4 uiCardTitle>Card with Header & Content</h4>
        </div>

        <div uiCardContent>
            <p>This card only has a header and uiCardContent section. No footer is present.</p>
        </div>
</div>
`
    })
};

export const HoverCard: Story = {
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;" [hoverable]="true">
        <div uiCardHeader>
            <h4 uiCardTitle>Hoverable Card</h4>
        </div>

        <div uiCardContent>
            <p>This card only has a header and uiCardContent section. No footer is present. On hover, it gets highlighted.</p>
        </div>
</div>
`
    })
};

export const SizedCardXS: Story = {
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;" size="xs">
        <div uiCardHeader>
            <h4 uiCardTitle>Size XS Card</h4>
        </div>

        <div uiCardContent>
            <p>This card only has a header and uiCardContent section. No footer is present.</p>
        </div>
</div>
`
    })
};

export const SizedCardSM: Story = {
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;" size="sm">
        <div uiCardHeader>
            <h4 uiCardTitle>Size SM Card</h4>
        </div>

        <div uiCardContent>
            <p>This card only has a header and uiCardContent section. No footer is present.</p>
        </div>
</div>
`
    })
};

export const EmptyCard: Story = {
    render: (args: CardStoryArgs) => ({
        props: args,
        template: `
<div uiCard style="width: 350px;"></div>
`
    })
};
