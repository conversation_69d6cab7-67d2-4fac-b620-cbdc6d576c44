import { computed, Directive, input, signal } from '@angular/core';
import { UIComponentSizeDirective } from '../../directives';

/**
 * The `uiCard` directive is used to create a card component with a specific style and layout.
 * Create a card with `uiCardHeader`, `uiCardContent`, and `uiCardFooter` directives.
 */
@Directive({
    selector: '[uiCard]',
    host: {
        '[style.display]': '"flex"',
        '[style.flex-direction]': '"column"',
        '[style.border]': '"1px solid"',
        '[style.border-radius]': '"var(--nui-card-radius, 4px)"',
        '[style.box-shadow]': '"0px 4px 8px 0px rgba(0, 0, 51, 0.06)"',
        '[style.overflow]': '"hidden"',
        '[style.background-color]': 'backgroundColor()',
        '[style.border-color]': 'borderColor()',
        '[style.padding]': 'padding()',
        '(mouseenter)': 'hoverState.set(true)',
        '(mouseleave)': 'hoverState.set(false)'
    },
    hostDirectives: [
        {
            directive: UIComponentSizeDirective,
            inputs: ['uiSize: size']
        }
    ]
})
export class UiCardDirective {
    inset = input<boolean>(false);
    selected = input<boolean>(false);
    hoverable = input<boolean>(false);

    protected hovered = computed<boolean>(() => {
        const hoverable = this.hoverable();
        if (!hoverable) {
            return false;
        }
        return this.hoverState();
    });

    protected hoverState = signal<boolean>(false);

    protected backgroundColor = computed(() => {
        if (this.selected()) {
            return 'var(--nui-card-fill-selected, #F7F8FA)';
        }
        return this.hovered() ? 'var(--nui-card-fill-hover)' : 'var(--nui-card-fill-default, #FFFFFF)';
    });

    protected borderColor = computed(() => {
        if (this.selected()) {
            return 'var(--nui-card-border-selected, #0072FF)';
        }
        return this.hovered()
            ? 'var(--nui-card-border-hover)'
            : 'var(--nui-card-border-default, #CDCED7)';
    });

    protected padding = computed(() =>
        this.inset()
            ? '0'
            : 'var(--nui-card-space-padding-vertical, 8px) var(--nui-card-space-padding-horizontal, 8px)'
    );
}
