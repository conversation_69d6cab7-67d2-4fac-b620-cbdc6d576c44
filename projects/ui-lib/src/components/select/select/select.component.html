@let invalid = validation?.invalid && (validation?.touched || !validation?.pristine);
@let valid = validation?.valid && (validation?.touched || !validation?.pristine);

@if (isNewUI()) {
    @if (label() && type() !== 'secondary') {
        <div class="label-wrapper">
            <ui-label
                class="label"
                [size]="size() === 'xs' ? 'xs' : 'sm'"
                [type]="disabled ? 'disabled' : invalid ? 'destructive' : 'primary'">
                {{ label() }}
            </ui-label>
        </div>
    }

    <button
        class="button input-area"
        ui-popover-target
        data-testid="select-button"
        #selectButton
        #target="ui-popover-target"
        tabindex="0"
        [class.disabled]="disabled"
        [class.invalid]="invalid"
        [class.valid]="valid"
        [class.token-field-selected]="tokenField && !isEmpty"
        (keydown)="onKeyDown($event)"
        (click)="open()">
        @if (customLabel()) {
            <div class="custom-label-container">
                <ng-container *ngTemplateOutlet="customLabelTemplate"></ng-container>
            </div>
        } @else if (hasSelectedValue()) {
            @if (showThumbnails()) {
                <div class="thumbnails-container">
                    @let limit = 4;
                    @let _thumbnails = thumbnails();
                    @if (_thumbnails.length === 1) {
                        <ui-label
                            class="single-selection"
                            [size]="size()"
                            [leadingFlag]="_thumbnails[0].flag"
                            [type]="disabled ? 'disabled' : 'primary'"
                            [truncate]="true">
                            {{ _thumbnails[0].value }}
                        </ui-label>
                    } @else {
                        @for (thumbnail of _thumbnails; track $index; let index = $index) {
                            @if (index < limit) {
                                @if (thumbnail?.flag) {
                                    <ui-flag
                                        class="flag"
                                        [nuiSize]="size()"
                                        [nuiFlag]="thumbnail.flag" />
                                }
                            }
                        }
                        @if (_thumbnails.length > limit) {
                            <ui-label
                                [type]="disabled ? 'disabled' : 'primary'"
                                [size]="size()">
                                and {{ _thumbnails.length - limit }} more
                            </ui-label>
                        }
                    }
                </div>
            } @else if (multiSelect) {
                <div class="chips-container">
                    @for (item of value; track $index; let index = $index) {
                        @let itemText = getItemTextByValue(item);
                        <ui-chip
                            [size]="size() === 'xs' ? 'sm' : 'md'"
                            [type]="chipType()"
                            (closeClick)="$event.stopPropagation(); deselect(item)">
                            {{ itemText }}
                        </ui-chip>
                    }
                </div>
            } @else {
                <ui-label
                    [size]="size()"
                    class="selected-item"
                    [type]="disabled ? 'disabled' : 'primary'"
                    [leadingIcon]="selectedIcon()">
                    @let itemText = getItemTextByValue(selected);
                    {{ itemText }}
                </ui-label>
            }
        } @else {
            <ui-label
                [size]="size()"
                class="placeholder"
                [type]="disabled ? 'disabled' : 'secondary'">
                {{ placeholder }}
            </ui-label>
        }

        <ui-svg-icon
            [size]="size()"
            [nuiIcon]="popoverOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
            icon="none"
            class="arrow"
            [attr.data-testid]="'select-dropdown-arrow'" />
    </button>

    @if (helpText() && type() !== 'secondary') {
        <div class="help-text-wrapper">
            <ui-label
                class="help-text"
                size="xs"
                [leadingIcon]="invalid ? 'error' : undefined"
                [type]="disabled ? 'disabled' : invalid ? 'destructive' : 'secondary'">
                {{ helpText() }}
            </ui-label>
        </div>
    }
} @else {
    <button
        class="button"
        ui-popover-target
        data-testid="select-button"
        #selectButton
        #target="ui-popover-target"
        tabindex="0"
        [class.disabled]="disabled"
        [class.invalid]="validation?.invalid && (validation?.touched || !validation?.pristine)"
        [class.valid]="validation?.valid && (validation?.touched || !validation?.pristine)"
        [class.token-field-selected]="tokenField && !isEmpty"
        (keydown)="onKeyDown($event)"
        (click)="open()">
        @if ((selected || selectedViewText || value) && selected?.toString()) {
            <div
                class="text"
                #text>
                @if (customLabel()) {
                    <ng-container *ngTemplateOutlet="customLabelTemplate"></ng-container>
                }

                @if (!multiSelect) {
                    @if (selectables && getIconByType(selected, 'svgIcon')) {
                        <ui-svg-icon [icon]="$any(getIconByType(selected, 'svgIcon')!)"></ui-svg-icon>
                    }
                    @if (
                        selectables &&
                        getIconByType(selected, 'icon') &&
                        !getIconByType(selected, 'svgIcon')
                    ) {
                        <ui-icon
                            class="icon"
                            [icon]="getIconByType(selected, 'icon')"></ui-icon>
                    }
                    @if (getIconByType(selected, 'image')) {
                        <ui-image
                            class="selected-image"
                            [image]="getIconByType(selected, 'image')"
                            [bordered]="isItemBordered(selected)"></ui-image>
                    }
                    @if (getIconByType(selected, 'flag'); as flagValue) {
                        <ui-flag
                            class="flag"
                            size="small"
                            [culture]="flagValue"></ui-flag>
                    }
                    @if (!customLabel()) {
                        <span class="text-wrapper">
                            {{ selectedViewText }}
                        </span>
                    }
                }

                @if (multiSelect && !tokenField) {
                    @if (!customLabel() && value?.length) {
                        <span
                            class="selected-summary-data"
                            [attr.data-testid]="'selected-item-with-flag'">
                            @for (item of value; track $index; let last = $last; let index = $index) {
                                @if (getIconByType(item, 'icon') && index < displaySelectedLimit) {
                                    <ui-icon
                                        class="icon"
                                        [icon]="getIconByType(item, 'icon')"></ui-icon>
                                }
                                @if (getIconByType(item, 'image') && index < displaySelectedLimit) {
                                    <ui-image
                                        class="image"
                                        [image]="getIconByType(item, 'image')"
                                        [bordered]="isItemBordered(selected)"></ui-image>
                                }
                                @if (getIconByType(item, 'flag') && index < displaySelectedLimit) {
                                    <ui-flag
                                        class="flag"
                                        size="small"
                                        [culture]="getIconByType(item, 'flag')"></ui-flag>
                                }

                                @if (value.length === 1) {
                                    <span class="selected-summary-value">
                                        <ng-container>
                                            {{ getItemTextByValue(item) }}{{ last ? '' : ',' }}
                                            <ui-icon
                                                icon="remove"
                                                class="clear"
                                                (click)="deselectAll()"
                                                ui-prevent-click></ui-icon>
                                        </ng-container>
                                    </span>
                                }
                            }
                        </span>
                    }
                    @if (value.length > 1) {
                        <span
                            class="selected-calculations"
                            [attr.data-testid]="'selected-calculated-items'">
                            @if (customSummaryValue()) {
                                <ng-content select="[ui-select-summary-value]"></ng-content>
                            } @else {
                                {{
                                    value.length === 1
                                        ? getItemTextByValue(value[0])
                                        : value.length + ' of ' + selectables.length + ' selected'
                                }}
                            }
                            <ui-icon
                                icon="remove"
                                class="clear"
                                (click)="deselectAll()"
                                ui-prevent-click></ui-icon>
                        </span>
                    }
                }

                @if (multiSelect && tokenField) {
                    @if (!customLabel() && value?.length) {
                        <span class="token-field-container">
                            @for (item of value; track $index; let last = $last) {
                                @let itemText = getItemTextByValue(item);
                                <span class="token-field">
                                    @if (getIconByType(item, 'icon'); as icon) {
                                        <ui-icon
                                            class="icon"
                                            [icon]="icon"></ui-icon>
                                    }
                                    @if (getIconByType(item, 'flag'); as flag) {
                                        <ui-flag
                                            class="flag"
                                            [culture]="flag"></ui-flag>
                                    }

                                    @let _customTokenField = customTokenField();
                                    @if (_customTokenField) {
                                        <ng-container
                                            *ngTemplateOutlet="
                                                _customTokenField;
                                                context: { $implicit: itemText, value: item }
                                            ">
                                        </ng-container>
                                    } @else {
                                        <ng-container>
                                            {{ itemText }}
                                        </ng-container>
                                    }

                                    <ui-svg-icon
                                        class="remove"
                                        icon="cross-small"
                                        (click)="deselect(item)"
                                        ui-prevent-click>
                                    </ui-svg-icon>
                                </span>
                            }
                        </span>
                    }
                }
            </div>
        }
        @if (!selected?.toString()) {
            <div class="text placeholder">
                @if (placeholderIcon) {
                    <ui-icon [icon]="placeholderIcon"></ui-icon>
                }
                <span class="text-wrapper">{{ placeholder }}</span>
            </div>
        }
        <div class="arrow-wrapper">
            <ui-svg-icon
                [icon]="popoverOpen ? 'arrow-up' : 'arrow-down'"
                class="arrow"
                [attr.data-testid]="'select-dropdown-arrow'"></ui-svg-icon>
        </div>
    </button>
}

<ui-popover
    #popover="ui-popover"
    (close)="onPopoverClose()"
    [config]="{
        panelClass: 'no-padding ui-scrollbar',
        useTargetWidth: isNewUI() || useTargetWidth,
        minWidth: listMinWidth,
        maxHeight: maxHeight,
        offset: { x: 0, y: -1 },
        theme: theme,
        width: isNewUI() ? undefined : width,
        popoverType: 'ui-select',
        type: type(),
        invalid
    }">
    <ng-template ui-popover-template>
        @if (searchable) {
            @if (isNewUI()) {
                <ui-search-input-option
                    #searchInput
                    [size]="size()"
                    (clearPreview)="clearPreview()"
                    [searchPlaceholder]="searchPlaceholder"
                    (onKeyDownSearch)="onKeyDownSearch($event)"
                    (onKeyUp)="onKeyUp()" />
            } @else {
                <input
                    id="interaction-search-input"
                    class="old-ui search"
                    #input
                    type="text"
                    (focus)="clearPreview()"
                    autocomplete="off"
                    [placeholder]="searchPlaceholder"
                    (keydown)="onKeyDownSearch($event)"
                    (keyup)="onKeyUp()" />
            }
        }

        @if (showSelectAll && multiSelect) {
            <ui-option
                class="selectable"
                (click)="selectAllList()">
                {{ value && value.length === selectablesLength ? 'Deselect all' : 'Select all' }}
            </ui-option>
        }
        <ng-container *ngTemplateOutlet="optionsContent" />
    </ng-template>
</ui-popover>

<ng-template #optionsContent>
    <ng-content></ng-content>
</ng-template>

<ng-template #customLabelTemplate>
    <ng-content select="[ui-select-label]"></ng-content>
</ng-template>
