import { NgTemplateOutlet } from '@angular/common';
import {
    Component,
    ElementRef,
    OnInit,
    SimpleChanges,
    computed,
    inject,
    input,
    signal,
    viewChild
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { debounceTime } from 'rxjs';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UINUIIcon } from '../../../types/icons';
import {
    UIFlagComponent,
    UIIconComponent,
    UIImageComponent,
    UINUIFlag,
    UISVGIconComponent
} from '../../icon/';
import { UISelectableBaseDirective } from '../../inputs/selectable-list/selectable.component';
import { UILabelComponent, UILabelWeight } from '../../label';
import { UITooltipDirective } from '../../popovers';
import { UISelectService } from './select.service';

/**
 * `--label-line-height`: sets the line-height property for the label
 */
@Component({
    imports: [
        NgTemplateOutlet,
        UIFlagComponent,
        UIIconComponent,
        UIImageComponent,
        UILabelComponent,
        UISVGIconComponent,
        UITooltipDirective
    ],
    selector: 'ui-option',
    templateUrl: 'option.component.html',
    styleUrls: ['./option.component.scss', './option.new.component.scss'],
    providers: [
        {
            provide: UISelectableBaseDirective,
            useExisting: UIOptionComponent,
            multi: true
        }
    ],
    host: {
        '[class]': 'type()',
        '[class.ui-option]': 'true',
        '[class.selected]': 'selected',
        '[class.disabled]': 'disabled',
        '[class.non-interactable]': '!interactable()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(click)': 'onClick($event)',
        '(mouseover)': 'onMouseOver($event)'
    }
})
export class UIOptionComponent extends UISelectableBaseDirective implements OnInit {
    private readonly uiNewThemeService = inject(UINewThemeService);
    private readonly uiSelectService = inject(UISelectService);

    readonly alwaysVisible = input(false);
    readonly chipText = input<string>();
    readonly interactable = input(true);
    readonly nuiFlag = input<UINUIFlag>();
    readonly nuiIcon = input<UINUIIcon>();
    readonly size = input<'xs' | 'sm' | 'md'>('md');
    readonly type = input<'primary' | 'secondary'>('primary');
    readonly weight = input<UILabelWeight>('regular');

    protected showMultiselectIcon = this.uiSelectService.showMultiselectIcon.asReadonly();

    protected computedIcon = computed(() => {
        const nuiIcon = this.nuiIcon();
        if (nuiIcon) {
            return nuiIcon;
        }
        return this.showMultiselectIcon() && this.multiSelect && this.selectedSignal()
            ? 'check'
            : undefined;
    });

    public multiSelect = false;
    public tokenField = false;

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    private labelElement = viewChild('label', { read: ElementRef<HTMLElement> });

    // debounceTime needed for allowing render of select popover
    private selectOpen = toSignal(this.uiSelectService.open$.pipe(debounceTime(1)), {
        initialValue: null
    });

    protected tooltipText = computed(() => this.computeTooltipText());
    protected tooltipWidth = computed(() => (this.host.nativeElement?.clientWidth ?? 0) * 1.5);

    private selectedSignal = signal(this.selected);

    ngOnInit(): void {
        this.multiSelect = this.uiSelectService.isMultiSelect;
        this.tokenField = this.uiSelectService.isTokenField;
    }

    override ngOnChanges(change: SimpleChanges): void {
        super.ngOnChanges(change);
        if ('selected' in change) {
            this.selectedSignal.set(change['selected'].currentValue);
        }
    }

    protected onClick(event: MouseEvent): void {
        this.shiftPressed = this.multiSelect && !this.selected && event.shiftKey;
        if (this.disabled) {
            return;
        }
        this.toggleSelect();
        this.selectedSignal.set(this.selected);
        if (!this.multiSelect) {
            this.changeDetectorRef.detectChanges();
            this.uiSelectService.close();
        }
    }

    protected onMouseOver(): void {
        if (this.disabled) {
            return;
        }

        this.emitHoverPreview();
    }

    private computeTooltipText(): string | undefined {
        // trigger computed when select opens (needed for real width)
        this.selectOpen();
        if (!this.isNewUI()) {
            return;
        }
        const labelElement = this.labelElement();

        if (labelElement?.nativeElement instanceof HTMLElement) {
            // Check if element is being truncated
            if (labelElement.nativeElement.scrollWidth > labelElement.nativeElement.offsetWidth) {
                return labelElement.nativeElement.textContent?.trim();
            }
        }

        return undefined;
    }
}
