:where(:root[data-uinew]) :host {
    display: flex;
    width: 100%;
    height: var(--nui-forms-height, 40px);
    padding: 0 var(--nui-menu-item-space-padding-horizontal, 12px);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    cursor: pointer;
    text-wrap: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;

    --label-line-height: var(--nui-label-regular-line-height);

    &.non-interactable {
        pointer-events: none;
    }

    &.disabled {
        cursor: not-allowed;
    }

    ui-label {
        max-width: 100%;

        --color: var(--nui-menu-item-text-label-primary-default, #1E1F24);
        --line-height: var(--label-line-height);
    }

    &.previewed,
    &:hover {
        border-radius: var(--nui-menu-item-radius, 0);
        background: var(--nui-menu-item-fill-primary-hover, #E7E8EC);

        ui-label {
            --color: var(--nui-menu-item-text-label-primary-hover, #1E1F24);
        }
    }

    &.selected {
        border-radius: var(--nui-menu-item-radius, 0);
        background: var(--nui-menu-item-fill-primary-pressed, #006CFD);

        ui-label {
            --color: var(--nui-menu-item-text-label-primary-pressed, #FFF);
        }
    }

    &.secondary {
        ui-label {
            --color: var(--nui-menu-item-text-label-secondary-default, #1E1F24);
        }

        &.previewed,
        &:hover {
            background: var(--nui-menu-item-fill-secondary-hover, #E0E1E6);

            ui-label {
                --color: var(--nui-menu-item-text-label-secondary-hover, #1E1F24);
            }
        }

        &.selected {
            background: var(--nui-menu-item-fill-secondary-pressed, #006CFD);

            ui-label {
                --color: var(--nui-menu-item-text-label-secondary-pressed, #FFF);
            }
        }
    }
}
