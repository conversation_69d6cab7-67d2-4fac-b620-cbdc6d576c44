import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { userEvent, within } from '@storybook/test';
import { UIComponentSizeDirective } from '../../../directives';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UINUIIcon } from '../../../types';
import { NUI_FLAGS, UINUIFlag } from '../../icon';
import { materialIcons } from '../../icon/svg-icon/nui-material.icons';
import { icons } from '../../icon/svg-icon/nui.icons';
import { UICheckboxComponent, UIRadioComponent } from '../../inputs';
import { UILabelComponent } from '../../label';
import { UIOptionComponent } from './option.component';
import { UISelectLabelDirective } from './select-label.directive';
import { UISelectComponent } from './select.component';
import { UISelectService } from './select.service';
import { UISelectTokenFieldTemplateDirective } from './templates/select-token-field-template.directive';

const meta: Meta<UISelectComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Select',
    component: UISelectComponent,
    decorators: [
        moduleMetadata({
            providers: [UISelectService],
            imports: [
                UICheckboxComponent,
                UILabelComponent,
                UIOptionComponent,
                UIRadioComponent,
                UISelectComponent,
                UISelectLabelDirective,
                UISelectTokenFieldTemplateDirective,

                CommonModule,
                BrowserAnimationsModule,
                OverlayModule,
                UIComponentSizeDirective
            ]
        })
    ],
    argTypes: {
        label: { type: 'string' },
        placeholder: { type: 'string' },
        helpText: { type: 'string' },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        type: { control: 'inline-radio', options: ['primary', 'secondary'] }
    }
};
export default meta;

type Choice = {
    culture: string;
    value: string;
    id: number;
};

type Story = StoryObj<UISelectComponent>;
type PlaygroundStory = StoryObj<
    UISelectComponent & { nuiIcon: UINUIIcon | undefined; nuiFlag: UINUIFlag | undefined }
>;

const choices: Choice[] = [
    { culture: 'us', value: 'Apple', id: 0 },
    { culture: 'me', value: 'Banana', id: 1 },
    { culture: 'bq', value: 'Orange', id: 2 },
    { culture: 'al', value: 'Pineapple', id: 3 },
    { culture: 'gb', value: 'Physalis', id: 4 },
    { culture: 'sg', value: 'Melon', id: 5 },
    {
        culture: 'sg',
        value: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. ",
        id: 6
    }
];

export const Playground: PlaygroundStory = {
    parameters: {
        controls: {
            include: [
                'disabled',
                'helpText',
                'label',
                'multiSelect',
                'placeholder',
                'searchable',
                'size',
                'nuiIcon',
                'nuiFlag',
                'type',
                'showThumbnails',
                'showMultiselectIcon'
            ]
        }
    },
    argTypes: {
        label: { type: 'string' },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        type: { control: 'inline-radio', options: ['primary', 'secondary'] },
        nuiIcon: { control: 'select', options: ['none', ...icons, ...materialIcons] },
        nuiFlag: { control: 'select', options: ['none', ...NUI_FLAGS] }
    },
    args: {
        disabled: false,
        helpText: 'Help text',
        label: 'Label',
        multiSelect: false,
        placeholder: 'Placeholder',
        searchable: true,
        showThumbnails: false,
        showMultiselectIcon: false,
        size: 'md',
        type: 'primary'
    },
    render: args => ({
        args,
        props: {
            ...args,
            choices
        },
        template: `
        <ui-select
            [size]="size"
            [type]="type"
            [multiSelect]="multiSelect"
            [searchable]="searchable"
            [disabled]="disabled"
            [label]="label"
            [placeholder]="placeholder"
            [helpText]="helpText"
            [showThumbnails]="showThumbnails"
            [showMultiselectIcon]="showMultiselectIcon"
            (selectedChange)="selectedValue = $event"
            [selected]="selectedValue">
            <div>
                @for(item of choices; track item.id){
                    <ui-option
                        [nuiIcon]="nuiIcon !== 'none'? nuiIcon:undefined"
                        [nuiFlag]="nuiFlag !== 'none'? nuiFlag:undefined"
                        [type]="type"
                        [value]="item.value">
                        {{item.value}}
                    </ui-option>
                }
            </div>
        </ui-select>
    `
    })
};

export const NuiSelect: Story = {
    parameters: {
        controls: {
            include: [
                'label',
                'helpText',
                'placeholder',
                'searchPlaceholder',
                'disabled',
                'size',
                'type',
                'showMultiselectIcon'
            ]
        }
    },
    argTypes: {
        disabled: { type: 'boolean' },
        helpText: { type: 'string' },
        label: { type: 'string' },
        placeholder: { type: 'string' },
        searchPlaceholder: { type: 'string' },
        showMultiselectIcon: { type: 'boolean' },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        type: { control: 'inline-radio', options: ['primary', 'secondary'] }
    },
    args: {
        disabled: false,
        helpText: 'Help text',
        label: 'Label',
        placeholder: 'Select text',
        searchPlaceholder: 'Search',
        showMultiselectIcon: true,
        size: 'md'
    },
    render: args => ({
        args,
        props: {
            ...args,
            choices
        },
        template: `
        <style>
        h6 {
        margin-bottom: 16px;
        }
        </style>
<h6 [uiSize]="'md'">Single select:</h6>
<ui-select
    [type]="'primary'"
    [multiSelect]="false"
    [label]="label"
    [placeholder]="placeholder"
    [disabled]="disabled"
    [showThumbnails]="showThumbnails"
    [searchable]="true"
    [size]="size"
    [searchPlaceholder]="searchPlaceholder"
    [helpText]="helpText">
    <div>
    <ui-option
        [size]="size"
        [type]="'secondary'"
        [weight]="'bold'"
        [interactable]="false">
        Non interactable
    </ui-option>
    <ui-option [size]="size">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis at nibh ut est tempor
    </ui-option>
    <ui-option [size]="size">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis at nibh ut est tempor dictum. Proin sit amet cursus lorem, nec pretium diam. Phasellus semper elit libero, id viverra leo commodo vitae. Proin vestibulum, enim et pellentesque porttitor, neque ex vehicula nunc, vulputate dapibus orci ipsum at nibh. Ut nec sollicitudin sapien. Donec et risus et arcu gravida ullamcorper. Sed luctus iaculis magna eget pellentesque. Quisque dictum posuere odio, at fermentum urna ullamcorper ut.
    </ui-option>
    @for(item of choices; track item.id){
        <ui-option
            [size]="size"
            [value]="item">
            {{item.value}}
        </ui-option>
    }
    </div>
</ui-select>
<br />
<hr/>
<h6 [uiSize]="'md'">Multi select (chips):</h6>
<ui-select
    [style.width]="'fit-content'"
    [style.min-width]="'260px'"
    [multiSelect]="true"
    [showThumbnails]="false"
    [showMultiselectIcon]="showMultiselectIcon"
    [placeholder]="placeholder"
    [size]="size"
    [label]="label"
    [disabled]="disabled"
    [searchable]="true"
    [searchPlaceholder]="searchPlaceholder"
    [helpText]="helpText"
    (selectedChange)="selectedValueChips = $event"
    [selected]="selectedValueChips ?? choices">
    <div>
    @for(item of choices; track item.id){
        <ui-option
            [size]="size"
            [value]="item"
            [chipText]="item.value">
            {{item.value}}
        </ui-option>
    }
    </div>
</ui-select>
<br />
<h6 [uiSize]="'md'">Multi select (thumbnail):</h6>
<ui-select
    [searchable]="true"
    [multiSelect]="true"
    [showMultiselectIcon]="showMultiselectIcon"
    [showThumbnails]="true"
    [placeholder]="placeholder"
    [label]="label"
    [size]="size"
    [disabled]="disabled"
    [searchPlaceholder]="searchPlaceholder"
    [helpText]="helpText"
    (selectedChange)="selectedValueThumb = $event"
    [selected]="selectedValueThumb ?? choices">
    <div>
    @for(item of choices; track item.id){
        <ui-option
            [size]="size"
            [value]="item"
            [flag]="item.culture">
            {{item.value}}
        </ui-option>
    }
    </div>
</ui-select>
    `
    })
};

export const NuiSelectOpened: Story = {
    ...NUI_STORY_SETTINGS,
    parameters: {
        controls: {
            include: [
                'label',
                'helpText',
                'placeholder',
                'searchPlaceholder',
                'disabled',
                'showMultiselectIcon'
            ]
        }
    },
    play: async ({ canvasElement }) => {
        // Force NUI before play function finishes
        canvasElement.ownerDocument.documentElement.dataset['uinew'] = 'true';
        await new Promise<void>(res => {
            const intervalId = setInterval(() => {
                if (canvasElement.ownerDocument.documentElement.dataset['uinew']) {
                    res();
                    clearInterval(intervalId);
                }
            }, 100);
        });

        const canvas = within(canvasElement);
        const button = await canvas.findByTestId('select-button');
        await userEvent.click(button);
    },
    argTypes: {
        label: { type: 'string' },
        helpText: { type: 'string' },
        placeholder: { type: 'string' },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        disabled: { type: 'boolean' }
    },
    args: {
        label: 'Label',
        helpText: 'Help text',
        placeholder: 'Select text',
        disabled: false,
        size: 'md'
    },
    render: args => ({
        args,
        props: {
            ...args,
            choices
        },
        template: `
<ui-select
    [multiSelect]="false"
    [label]="label"
    [placeholder]="placeholder"
    [disabled]="disabled"
    [showThumbnails]="showThumbnails"
    [searchable]="true"
    [helpText]="helpText">
    <div>
        <ui-option>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis at nibh ut est tempor dictum. Proin sit amet cursus lorem, nec pretium diam. Phasellus semper elit libero, id viverra leo commodo vitae. Proin vestibulum, enim et pellentesque porttitor, neque ex vehicula nunc, vulputate dapibus orci ipsum at nibh. Ut nec sollicitudin sapien. Donec et risus et arcu gravida ullamcorper. Sed luctus iaculis magna eget pellentesque. Quisque dictum posuere odio, at fermentum urna ullamcorper ut.
        </ui-option>
        @for(item of choices; track item.id){
            <ui-option [value]="item" [size]="size">
                {{item.value}}
            </ui-option>
        }
    </div>
</ui-select>
`
    })
};

export const NuiSelectCustomLabel: Story = {
    ...NUI_STORY_SETTINGS,
    parameters: {
        controls: {
            include: []
        }
    },
    render: args => ({
        args,
        props: {
            ...args,
            choices
        },
        styles: [
            `
ui-select {
    width: 200px;
}
`
        ],
        template: `
<ui-select
    [multiSelect]="false"
    [label]="label"
    [placeholder]="placeholder"
    [disabled]="disabled"
    [showThumbnails]="showThumbnails"
    [searchable]="true"
    [helpText]="helpText">
    <ng-container ui-select-label>
        <ui-label type="primary" class="placeholder">Something</ui-label>
    </ng-container>
    <div>
        <ui-option>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis at nibh ut est tempor dictum. Proin sit amet cursus lorem, nec pretium diam. Phasellus semper elit libero, id viverra leo commodo vitae. Proin vestibulum, enim et pellentesque porttitor, neque ex vehicula nunc, vulputate dapibus orci ipsum at nibh. Ut nec sollicitudin sapien. Donec et risus et arcu gravida ullamcorper. Sed luctus iaculis magna eget pellentesque. Quisque dictum posuere odio, at fermentum urna ullamcorper ut.
        </ui-option>
        @for(item of choices; track item.id){
            <ui-option [value]="item" [size]="size">
                {{item.value}}
            </ui-option>
        }
    </div>
</ui-select>
`
    })
};

export const NuiSelectSecondary: Story = {
    parameters: {
        controls: {
            include: ['placeholder', 'disabled', 'showMultiselectIcon']
        }
    },
    args: {
        disabled: false,
        placeholder: 'Choose an option',
        showMultiselectIcon: false,
        size: 'md'
    },
    render: args => ({
        args,
        props: {
            ...args,
            choices
        },
        template: `
<ui-select
    [type]="'secondary'"
    [multiSelect]="false"
    [placeholder]="placeholder"
    [disabled]="disabled"
    [showThumbnails]="false"
    [size]="size"
    (selectedChange)="selectedValue = $event"
    [selected]="selectedValue">
    <div>
        @for(item of choices; track item.id){
            <ui-option [value]="item" [size]="size" [type]="'secondary'">
                {{item.value}}
            </ui-option>
        }
    </div>
</ui-select>
`
    })
};
