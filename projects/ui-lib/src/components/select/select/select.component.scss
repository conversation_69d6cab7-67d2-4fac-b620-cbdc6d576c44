@use '../../../style/animation/index';

@keyframes text {
    0% {
        transform: translateY(2rem);
    }

    100% {
        transform: translateY(0);
    }
}

:host-context([ui-theme='tiny']) {
    .arrow-wrapper {
        border-left: none;
        background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjMiIHZpZXdCb3g9IjAgMCA1IDMiPgogICAgPHBhdGggZmlsbD0iI0I1QjVCNSIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMi41IDNMMCAwaDV6Ii8+Cjwvc3ZnPgo=');
        background-repeat: no-repeat;
        background-position: 50%;

        .arrow {
            display: none;
        }
    }
}

:where(:root:not([data-uinew])) :host {
    display: inline-block;

    --font-weight: normal;
    --min-width: 15rem;
    --arrow-color: var(--ui-color-border);
    --button-height: auto;
    --button-min-height: initial;

    &.discrete:not(.open) {
        .button {
            border-color: transparent;
            background: transparent;
        }

        .arrow-wrapper {
            border-color: transparent;
            background: transparent;

            .arrow {
                color: transparent;
                background: transparent;
            }
        }

        &:hover {
            .button {
                border-color: var(--ui-color-border);
                background: var(--ui-color-surface);
            }

            .arrow-wrapper {
                border-color: var(--ui-color-border);
                background: var(--ui-color-surface);

                .arrow {
                    color: var(--ui-color-border);
                    background: var(--ui-color-surface);
                }
            }
        }
    }

    .button {
        display: flex;
        justify-content: space-between;
        width: 100%;
        max-width: 100%;
        min-width: var(--min-width);
        height: var(--button-height);
        min-height: var(--button-min-height);
        background: var(--ui-color-surface);
        border: 1px solid var(--ui-color-border);
        border-radius: 0.2rem;
        user-select: none;
        cursor: pointer;
        overflow: hidden;
        align-items: normal;
        padding: 0;

        &.disabled {
            pointer-events: none;
            background-color: var(--ui-color-disabled);

            .text,
            .selected-summary-data,
            .selected-calculations {
                color: var(--ui-color-text-disabled);
            }
        }

        &:focus-visible {
            border: 1px solid var(--ui-color-focus);
            outline: none;
        }
    }

    .invalid {
        border: 1px solid var(--ui-color-alert);
    }

    .text {
        height: 100%;
        font-size: var(--ui-font-size);
        font-weight: var(--font-weight);
        letter-spacing: 0;
        text-shadow: none;
        text-decoration: none;
        text-align: left;
        line-height: calc(var(--height) - 0.2rem);
        padding: 0 0 0 1rem;
        display: flex;
        align-items: center;
        flex-grow: 1;
        overflow: hidden;
        color: var(--ui-color-text);

        &.animate {
            transform: translateY(2rem);
            animation: text 0.15s ease;
            animation-fill-mode: forwards;
        }

        &.placeholder {
            color: var(--ui-color-grey-61);
        }

        & ::ng-deep [ui-select-label] {
            display: flex;
            align-items: center;
        }

        &>ui-flag,
        &>ui-icon,
        &>ui-svg-icon {
            margin-right: 0.8rem;
        }
    }

    .text-wrapper {
        min-width: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .icon {
        color: var(--ui-color-grey-61);
        font-size: 1.1667em; // If text is 12px, make icon 14px
    }

    .flag {
        font-size: var(--font-size);
    }

    .selected-summary {
        overflow: hidden;
    }

    .selected-summary-data {
        color: var(--ui-color-primary);
        overflow: hidden;
        max-width: 100%;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        ui-icon,
        ui-image,
        ui-flag {
            flex-shrink: 0;
            margin-right: 0.5rem;

            &:last-child {
                margin-right: 0.8rem;
            }

            &+.selected-summary-value {
                margin-left: 0.3rem;
            }
        }
    }

    .selected-calculations {
        color: var(--ui-color-primary);
        min-width: 0;
    }

    .selected-calculations,
    .selected-summary-value {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .clear {
            line-height: 1;
            margin-left: 0.2rem;
        }
    }

    .arrow-wrapper {
        width: var(--height);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;

        .arrow {
            color: var(--arrow-color);
            font-size: 1.4rem;
        }
    }


    .selected-image {
        margin-right: 0.3rem;
    }

    .token-field {
        width: var(--width);
        height: calc(var(--height) - 0.6rem);
        background-color: var(--ui-color-surface);
        border: 1px solid var(--ui-color-border);
        border-radius: 0.2rem;
        padding: 0 1.2rem;
        margin: 0.1rem;
        display: flex;
        align-items: center;
        line-height: var(--ui-line-height);

        ui-flag {
            --size: 1.2rem;

            margin-right: 0.8rem;
        }

        ui-icon {
            margin-right: 0.8rem;
        }

        .remove {
            font-size: var(--font-size);
            margin-left: 0.8rem;
        }
    }

    .token-field-selected {
        .text {
            padding: 0;
        }
    }

    .token-field-container {
        display: flex;
        flex-wrap: wrap;
        padding: 0.1rem;
    }
}

.old-ui.search {
    padding-left: 1rem;
    width: 100%;
    height: var(--ui-height);
    position: inherit;
    outline: none;
    border: 1px solid transparent;
    border-bottom: 1px solid var(--ui-color-border);
    background: var(--ui-color-surface);
    font-size: var(--ui-font-size);

    &:focus {
        border: 1px solid var(--ui-color-primary);
    }
}
