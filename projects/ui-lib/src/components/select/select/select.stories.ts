import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UICheckboxComponent, UIRadioComponent } from '../../inputs';
import { UILabelComponent } from '../../label';
import { UIOptionComponent } from './option.component';
import { UISelectLabelDirective } from './select-label.directive';
import { UISelectComponent } from './select.component';
import { UISelectService } from './select.service';
import { UISelectTokenFieldTemplateDirective } from './templates/select-token-field-template.directive';

const meta: Meta<UISelectComponent> = {
    title: 'Components/Select/Select',
    component: UISelectComponent,
    decorators: [
        moduleMetadata({
            providers: [UISelectService],
            imports: [
                UICheckboxComponent,
                UILabelComponent,
                UIOptionComponent,
                UIRadioComponent,
                UISelectComponent,
                UISelectLabelDirective,
                UISelectTokenFieldTemplateDirective,

                CommonModule,
                BrowserAnimationsModule,
                OverlayModule
            ]
        })
    ]
};
export default meta;

type Choice = {
    culture: string;
    value: string;
    id: number;
};

type Story = StoryObj<UISelectComponent>;

const choices: Choice[] = [
    { culture: 'us', value: 'Apple', id: 0 },
    { culture: 'me', value: 'Banana', id: 1 },
    { culture: 'bq', value: 'Orange', id: 2 },
    { culture: 'al', value: 'Pineapple', id: 3 },
    { culture: 'gb', value: 'Physalis', id: 4 },
    { culture: 'sg', value: 'Melon', id: 5 }
];

const render: Story['render'] = args => ({
    args,
    props: {
        ...args,
        choices
    },
    template: `
        <ui-select
            [tokenField]="tokenField"
            [multiSelect]="multiSelect"
            [discrete]="discrete"
            [searchable]="true"
            [listMinWidth]="listMinWidth"
            [disabled]="disabled"
            [placeholderIcon]="placeholderIcon"
            [useTargetWidth]="true"
            (selectedChange)="selectedValue = $event"
            [selected]="selectedValue">
            <div>
            @for(item of choices; track item.id){
                <ui-option [value]="item.value">
                    {{item.value}}
                </ui-option>
            }
            </div>
        </ui-select>
    `
});

export const Default: Story = {
    args: {
        tokenField: false,
        multiSelect: false,
        discrete: false,
        listMinWidth: 250,
        width: '350px',
        disabled: false,
        placeholderIcon: '',
        showMultiselectIcon: true
    },
    render
};

export const TokenField: Story = {
    render,
    args: {
        tokenField: true,
        multiSelect: true,
        discrete: false,
        listMinWidth: 250,
        width: '350px',
        disabled: false,
        placeholderIcon: ''
    }
};

export const MultiSelect: Story = {
    render,
    args: {
        tokenField: false,
        multiSelect: true,
        discrete: false,
        listMinWidth: 250,
        width: '350px',
        disabled: false,
        placeholderIcon: ''
    }
};

export const Disabled: Story = {
    render,
    args: {
        tokenField: false,
        multiSelect: false,
        discrete: false,
        listMinWidth: 250,
        width: '350px',
        disabled: true,
        placeholderIcon: ''
    }
};
const renderCustomTokenLabel: Story['render'] = args => ({
    args,
    props: {
        ...args,
        choices
    },
    template: `
        <ui-select
            [tokenField]="true"
            [multiSelect]="true"
            [discrete]="discrete"
            [searchable]="false"
            >

            <ng-template
                [ui-select-token-field-template]
                let-selected="value">
                <span>Text: {{selected.value}}</span>
            </ng-template>

            <div>
                @for(item of choices; track item.id){
                    <ui-option [value]="item">
                        {{item.value}}
                    </ui-option>
                }
            </div>
        </ui-select>
    `
});
export const CustomTokenLabel: Story = {
    render: renderCustomTokenLabel,
    args: {
        discrete: false
    }
};
