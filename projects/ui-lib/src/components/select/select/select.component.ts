import { NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    contentChild,
    effect,
    ElementRef,
    EventEmitter,
    inject,
    input,
    Input,
    OnChanges,
    OnDestroy,
    Output,
    QueryList,
    Renderer2,
    TemplateRef,
    viewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UntypedFormControl } from '@angular/forms';
import { UIPreventClickDirective } from '../../../directives';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UINUIIcon, UITheme } from '../../../types';
import { UIChipComponent, UIChipType } from '../../chip';
import {
    isNuiFlag,
    UIFlagComponent,
    UIIconComponent,
    UIImageComponent,
    UINUIFlag,
    UISVGIconComponent
} from '../../icon';
import { UISelectableBaseDirective, UISelectableListComponent } from '../../inputs/selectable-list';
import { UILabelComponent } from '../../label';
import {
    UIPopoverDirective,
    UIPopoverRef,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective
} from '../../popovers';
import { UIOptionComponent } from './option.component';
import { UISearchInputOptionComponent } from './search-input-option.component';
import { UISelectLabelDirective } from './select-label.directive';
import { UISelectSummaryValueDirective } from './select-summary-value.directive';
import { UISelectService } from './select.service';
import { UISelectTokenFieldTemplateDirective } from './templates/select-token-field-template.directive';

@Component({
    imports: [
        NgTemplateOutlet,
        UIChipComponent,
        UIFlagComponent,
        UIIconComponent,
        UIImageComponent,
        UILabelComponent,
        UIOptionComponent,
        UIPopoverDirective,
        UIPopoverTargetDirective,
        UIPopoverTemplateDirective,
        UIPreventClickDirective,
        UISVGIconComponent,
        UISearchInputOptionComponent
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'ui-select',
    templateUrl: 'select.component.html',
    providers: [UISelectService],
    styleUrls: ['select.component.scss', 'select.new.component.scss'],
    host: {
        class: 'ui-select',
        '[class.primary]': 'type() === "primary"',
        '[class.secondary]': 'type() === "secondary"',
        '[class.disabled]': 'disabled',
        '[class.discrete]': 'discrete',
        '[class.open]': 'popoverOpen',
        '[class.filled]': 'filled',
        '[style.width]': 'effectiveWidth',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UISelectComponent
    extends UISelectableListComponent
    implements OnChanges, AfterViewInit, OnDestroy
{
    protected override host: ElementRef;
    protected override renderer: Renderer2;
    protected override changeDetectorRef: ChangeDetectorRef;
    private uiSelectService = inject(UISelectService);
    private readonly uiNewThemeService = inject(UINewThemeService);

    readonly popover = viewChild<UIPopoverDirective>('popover');
    readonly target = viewChild<UIPopoverTargetDirective>('target');
    readonly textElement = viewChild<ElementRef>('text');
    readonly input = viewChild<ElementRef>('input');
    readonly searchInput = viewChild<UISearchInputOptionComponent>('searchInput');
    readonly selectButton = viewChild<ElementRef>('selectButton');
    readonly customLabel = contentChild(UISelectLabelDirective);

    customTokenField = contentChild(UISelectTokenFieldTemplateDirective, { read: TemplateRef });

    readonly customSummaryValue = contentChild(UISelectSummaryValueDirective);
    @Input() placeholder = 'No Item Selected';
    @Input() placeholderIcon: string;
    @Input() discrete = false;
    @Input() theme?: UITheme;
    @Input() useTargetWidth = false;
    @Input() validation?: UntypedFormControl;
    @Input() displaySelectedLimit = 3;
    @Input() backdropCss = '';
    @Input() maxHeight = 200;
    @Input() listMinWidth = 0;

    /**
     * @note For new UI: This input is ignored. Use CSS classes/styles to override the default.
     */
    @Input() width? = 'auto';

    /**
     * @summary:
     * Show 'Select All' in the options.
     * @description:
     * By default 'Select All' is not shown but in some cases there is a need to show it.
     */
    @Input() showSelectAll = false;
    @Input() searchPlaceholder = 'Type to search...';

    readonly chipType = input<UIChipType>('primary');
    readonly type = input<'primary' | 'secondary'>('primary');
    readonly size = input<'xs' | 'sm' | 'md'>('md');
    readonly label = input<string>();
    readonly helpText = input<string>();
    readonly showThumbnails = input<boolean>(false);
    readonly showMultiselectIcon = input<boolean>(true);

    @Output() onOpen = new EventEmitter<void>();
    @Output() onSearch = new EventEmitter<string>();

    popoverOpen = false;
    override selectables: QueryList<UISelectableBaseDirective> =
        new QueryList<UISelectableBaseDirective>();
    filteredItems: UISelectableBaseDirective[] = [];
    private currentPopoverRef: UIPopoverRef;

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    protected readonly thumbnails = computed(() => this.computeThumbnails());
    protected readonly selectedIcon = computed(() => this.computeSelectedIcon());
    protected readonly hasSelectedValue = computed(() => this.computeHasSelectedValue());

    private resizeObserver: ResizeObserver;

    get effectiveWidth(): string | undefined {
        return this.isNewUI() ? undefined : this.width;
    }

    get filled(): boolean {
        if (typeof this.selected === 'undefined') {
            return false;
        }
        return Array.isArray(this.selected) ? !!this.selected.length : !!this.selected;
    }

    constructor() {
        const host = inject(ElementRef);
        const renderer = inject(Renderer2);
        const changeDetectorRef = inject(ChangeDetectorRef);

        super();
        this.host = host;
        this.renderer = renderer;
        this.changeDetectorRef = changeDetectorRef;

        this.uiSelectService.open$.pipe(takeUntilDestroyed()).subscribe(() => {
            this.openSelectPopover();
        });

        this.uiSelectService.close$
            .pipe(takeUntilDestroyed())
            .subscribe(() => this.onCloseSelectPopover());

        this.selectables.changes.pipe(takeUntilDestroyed()).subscribe(() => {
            this.initialize();
            this.changeDetectorRef.detectChanges();
        });

        this.selectedChange.pipe(takeUntilDestroyed()).subscribe(() => {
            if (this.currentPopoverRef && this.popoverOpen) {
                setTimeout(() => {
                    this.currentPopoverRef.overlayRef.updatePosition();
                });
            }
        });
        effect(() => {
            this.uiSelectService.showMultiselectIcon.set(this.showMultiselectIcon());
        });
    }

    ngAfterViewInit(): void {
        this.resizeObserver = new ResizeObserver(entries => {
            if (!this.isNewUI()) {
                return;
            }
            for (const entry of entries) {
                if (entry.target === this.host.nativeElement) {
                    const popover = this.popover();
                    if (popover) {
                        popover.resize({ width: `${entry.contentRect.width}px` });
                    }
                }
            }
        });

        this.resizeObserver.observe(this.host.nativeElement);
    }

    override ngOnDestroy(): void {
        super.ngOnDestroy();
        this.resizeObserver.disconnect();
    }

    ngOnChanges(): void {
        if (this.theme) {
            this.host.nativeElement.setAttribute('ui-theme', this.theme);
            const popover = this.popover();
            if (popover?.config) {
                popover.config.theme = this.theme;
            }
        }
        this.uiSelectService.isMultiSelect = this.multiSelect;
        this.uiSelectService.isTokenField = this.tokenField;

        this.changeDetectorRef.detectChanges();
    }

    close(): void {
        this.uiSelectService.close();
    }

    open(): void {
        this.uiSelectService.open();
    }

    private onCloseSelectPopover(): void {
        this.changeDetectorRef.detectChanges();
        this.popover()?.close();
    }

    private openSelectPopover(): void {
        if (this.disabled) {
            return;
        }
        const target = this.target();
        const popover = this.popover();
        if (!target || !popover) {
            return;
        }
        this.popoverOpen = true;
        this.currentPopoverRef = popover.open(target);

        if (!this.searchable) {
            const option: (UISelectableBaseDirective | undefined)[] = [];

            if (Array.isArray(this.selected)) {
                this.selected.forEach(item => {
                    option?.push(this.getComponentByValue(item));
                });
            } else {
                option?.push(this.getComponentByValue(this.selected));
            }
            if (option) {
                setTimeout(() => {
                    option[0]?.host.nativeElement.scrollIntoView();
                });
            }
        } else {
            // TODO: would be better if we had popover.onDisplay() etc. to know when everything is rendered
            setTimeout(() => {
                this.changeDetectorRef.detectChanges();
                this.input()?.nativeElement?.focus();
                this.searchInput()?.focusInput();
            });
        }

        setTimeout(() => {
            if (this.currentPopoverRef.overlayRef.backdropElement) {
                for (const css of this.backdropCss.split(' ')) {
                    if (css) {
                        this.currentPopoverRef.overlayRef.backdropElement.classList.add(css);
                    }
                }
            }
        });

        this.onOpen.next();
    }

    onKeyDown(event: KeyboardEvent): void {
        if (!this.popoverOpen) {
            return;
        }
        event.stopPropagation();

        const selectables = this.filteredItems.length ? this.filteredItems : this.selectables.toArray();
        const filteredSelectables = selectables.filter(
            item => item.host.nativeElement.style.display !== 'none' && !item.disabled
        );

        let index = filteredSelectables.findIndex(selectable => selectable.previewed);

        const selectedIndex = this.multiSelect
            ? index
            : filteredSelectables.findIndex(selectable => selectable.selected);

        // if no item is previewed, start from the current selection if not multiselect
        index = index === -1 ? selectedIndex : index;

        const previewedItem: UISelectableBaseDirective | undefined = filteredSelectables[index];

        switch (event.key) {
            case 'ArrowDown':
                this.onArrowUpOrDownPreview(event, previewedItem, filteredSelectables, index);
                break;
            case 'ArrowUp':
                {
                    const shouldFocusSearch = index === 0 && this.searchable;
                    if (shouldFocusSearch && previewedItem) {
                        previewedItem.setPreview(false);
                        this.selectButton()?.nativeElement.blur();
                        this.input()?.nativeElement.focus();
                        this.searchInput()?.focusInput();
                        return;
                    }

                    this.onArrowUpOrDownPreview(event, previewedItem, filteredSelectables, index);
                }
                break;
            case 'Enter':
                this.onEnterPreview(event, previewedItem);
                break;
            case 'Escape':
                this.close();
                break;
            default:
                break;
        }
    }

    private onArrowUpOrDownPreview(
        event: KeyboardEvent,
        previewedItem: UISelectableBaseDirective | undefined,
        selectables: UISelectableBaseDirective[],
        index: number
    ): void {
        const isArrowDown = event.key === 'ArrowDown';
        const isArrowUp = event.key === 'ArrowUp';

        if (isArrowDown || isArrowUp) {
            event.preventDefault();

            const isFirstOptionPreviewed = isArrowUp && index === 0;
            const isLastOptionPreviewed = isArrowDown && index === selectables.length - 1;
            if (isFirstOptionPreviewed || isLastOptionPreviewed) {
                return;
            }

            let nextIndex = 0;
            if (index === -1) {
                nextIndex = 0;
            } else if (isArrowDown) {
                nextIndex = index + 1;
            } else {
                nextIndex = index - 1;
            }

            if (previewedItem) {
                previewedItem.setPreview(false);
            }

            const newPreviewedItem = selectables[nextIndex];
            newPreviewedItem.setPreview(true);

            this.scrollIntoView(newPreviewedItem.host.nativeElement);
            this.previewChange.emit(newPreviewedItem.getValue());
        }
    }

    private onEnterPreview(
        event: KeyboardEvent,
        previewedItem: UISelectableBaseDirective | undefined
    ): void {
        if (!previewedItem) {
            return;
        }

        event.preventDefault();
        previewedItem.toggleSelect();

        if (this.tokenField) {
            previewedItem.setPreview(false);
        }

        if (!this.multiSelect) {
            this.close();
        }
    }

    private scrollIntoView(element: HTMLElement): void {
        const scrollableParentElement =
            this.popover()?.popoverTemplate.elementRef.nativeElement.parentElement;
        if (!scrollableParentElement) {
            return;
        }
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (!entry.isIntersecting) {
                    element.scrollIntoView({ block: 'nearest', inline: 'nearest' });
                }
                observer.disconnect();
            },
            {
                root: scrollableParentElement,
                threshold: 1.0
            }
        );

        observer.observe(element);
    }

    public onKeyDownSearch(event: KeyboardEvent): void {
        if (event.key === 'ArrowDown') {
            event.preventDefault();
            this.input()?.nativeElement.blur();
            this.searchInput()?.blurInput();
            this.selectButton()?.nativeElement.focus();
            this.onKeyDown(event);
        }
    }

    public onKeyUp(): void {
        const inputValue = this.isNewUI()
            ? this.searchInput()?.value()
            : this.input()?.nativeElement.value;
        if (typeof inputValue === 'undefined') {
            return;
        }
        this.changeDetectorRef.detectChanges();
        const inputValueLowercase = inputValue.toLowerCase();

        this.onSearch.emit(inputValue);

        if (inputValueLowercase) {
            this.clearPreview();

            this.filteredItems = this.selectables?.filter(item => {
                const textContent = item.host.nativeElement.innerText.toLowerCase();
                const isAlwaysVisible = item instanceof UIOptionComponent && item.alwaysVisible();
                return isAlwaysVisible || textContent.includes(inputValueLowercase);
            });

            this.filteredItems.forEach(item => {
                item.host.nativeElement.style.display = 'flex';
            });

            const itemsNotFound = this.selectables?.filter(item => !this.filteredItems.includes(item));

            itemsNotFound.forEach(item => {
                item.host.nativeElement.style.display = 'none';
            });
        } else {
            this.resetSearch();
        }
    }

    resetSearch(): void {
        this.filteredItems = [];
        this.selectables?.forEach(item => {
            item.host.nativeElement.style.display = 'flex';
        });
    }

    hideItem(value: any): void {
        this.selectables?.forEach(item => {
            if (item.value === value) {
                item.host.nativeElement.style.display = 'none';
            }
        });
    }

    selectAllList(): void {
        if (this.value && this.value.length === this.selectablesLength) {
            this.deselectAll();
        } else {
            this.selectAll();
        }
        this.close();
    }

    getIconByType(value: any, type: string): string | undefined {
        const component = this.getComponentByValue(value);

        if (!component) {
            return undefined;
        }

        if (type.toLowerCase() === 'svgicon') {
            return component ? component.svgIcon : '';
        } else if (type.toLowerCase() === 'icon') {
            return component ? component.icon : '';
        } else if (type.toLowerCase() === 'flag') {
            return component ? component.flag : '';
        } else if (type.toLowerCase() === 'image') {
            return component ? component.image : '';
        }

        return undefined;
    }

    getItemTextByValue(value: any): string {
        const component = this.getComponentByValue(value);
        if (component) {
            if (this.isNewUI()) {
                if (component instanceof UIOptionComponent) {
                    const chipText = component.chipText();
                    if (chipText) {
                        return chipText;
                    }
                }
                const innerText = (component.host.nativeElement as HTMLElement).innerText;
                if (!innerText) {
                    if (typeof component.value === 'string') {
                        return component.value;
                    } else if (typeof component.value.name === 'string') {
                        return component.value.name;
                    } else if (typeof component.value.value === 'string') {
                        return component.value.value;
                    }
                }
                // trim " check ", added by svg icon
                return innerText.startsWith(' check ') ? innerText.substring(7) : innerText;
            }
            return component.host.nativeElement.innerText;
        }

        return value.name || value.value || value;
    }

    isItemBordered(value: string): boolean {
        const component = this.getComponentByValue(value);

        if (!component) {
            return false;
        }

        return component.withBorder;
    }

    onPopoverClose(): void {
        this.clearPreview();
        this.popoverOpen = false;
        this.changeDetectorRef.detectChanges();
        if (this.searchable) {
            this.resetSearch();
        }
    }

    clearPreview(): void {
        const preview = this.selectables.find(item => item.previewed);
        if (preview) {
            this.previewStop.emit();
            preview.setPreview(false);
        }
    }

    private computeSelectedIcon(): UINUIIcon | undefined {
        const value = this.valueSignal();
        const selectables = this.selectablesSignal();
        if (Array.isArray(value)) {
            return undefined;
        }
        const component = value?.id
            ? selectables.find(item => item.getValue().id === value.id)
            : selectables.find(item => item.getValue() === value);

        return component instanceof UIOptionComponent ? component.nuiIcon() : undefined;
    }

    private computeThumbnails(): { flag: UINUIFlag | undefined; value: string }[] {
        const valueSignal = this.valueSignal();
        const selectables = this.selectablesSignal();

        if (!this.isNewUI() || !this.showThumbnails()) {
            return [];
        }
        return [...(Array.isArray(valueSignal) ? valueSignal : [valueSignal])].map(value => {
            const component = value?.id
                ? selectables.find(item => item.getValue().id === value.id)
                : selectables.find(item => item.getValue() === value);

            const nuiFlag = component instanceof UIOptionComponent ? component.nuiFlag() : undefined;
            if (nuiFlag) {
                return {
                    value,
                    flag: nuiFlag
                };
            }
            return {
                value,
                ...value,
                flag: isNuiFlag(component?.flag) ? component?.flag : undefined
            };
        });
    }

    private computeHasSelectedValue(): boolean {
        const value = this.valueSignal();

        if (Array.isArray(value)) {
            return !!value.length;
        }

        return value !== undefined && value !== null && value !== '';
    }
}
