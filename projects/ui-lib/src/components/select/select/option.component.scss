$border: solid 0.1rem #ededed;

:where(:root:not([data-uinew])) :host {
    height: var(--height);
    line-height: var(--height);
    display: flex;
    cursor: pointer;
    padding: 0 1rem;
    user-select: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: var(--ui-font-size);
    align-items: center;

    &.disabled {
        color: var(--ui-color-text-second);
    }

    &:hover {
        background: var(--ui-color-background);
    }

    &:last-child {
        border-bottom: none;
        border-radius: 0 0 0.2rem 0.2rem;
    }

    &.previewed {
        box-shadow: 0 0 0 2px var(--ui-color-border-highlight) inset;
    }

    &.selected {
        background: var(--ui-color-selected-background);
        color: var(--ui-color-primary);

        .icon {
            color: var(--ui-color-primary);
        }
    }

    .icon {
        color: var(--ui-color-text-second);
        font-size: 1.1667em;
        margin-right: 0.8rem;
        flex-shrink: 0;
    }

    .checkbox {
        margin-left: var(--checkbox-margin);
        display: block;
        position: absolute;
        margin-top: -5px;
        color: var(--ui-color-text);
        font-size: 12px;
    }

    .flag {
        margin-right: 8px;
        flex-shrink: 0;
    }

    .image {
        flex-shrink: 0;
        margin-right: 8px;
    }

    .container {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-grow: 1;
    }

    .checkbox-container {
        margin-left: var(--checkbox-container-margin);
    }
}
