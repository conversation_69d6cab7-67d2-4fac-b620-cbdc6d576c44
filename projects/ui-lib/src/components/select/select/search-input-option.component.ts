import { Component, computed, ElementRef, input, output, viewChild } from '@angular/core';
import { DATASET_SIZE } from '../../../services';
import { UISVGIconComponent } from '../../icon';

/**
 * Search input option for selects.
 * Internal to BF-UI.
 * Meant to be used by NUI only.
 */
@Component({
    imports: [UISVGIconComponent],
    selector: 'ui-search-input-option',
    template: `
        <ui-svg-icon
            class="icon"
            icon="none"
            [size]="iconSize()"
            [nuiIcon]="'search'" />
        <input
            #input
            id="interaction-search-input"
            class="search"
            type="text"
            autocomplete="off"
            (focus)="clearPreview.emit()"
            [placeholder]="searchPlaceholder()"
            (keydown)="onKeyDownSearch.emit($event)"
            (keyup)="onKeyUp.emit()" />
    `,
    styles: `
        :host {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 100%;

            --icon-space-left: 12px;
            padding-left: var(--icon-space-left);

            &[data-${DATASET_SIZE}='xs'] {
                --icon-space-left: 8px;
            }
            &[data-${DATASET_SIZE}='sm'] {
                --icon-space-left: 8px;
            }

            input {
                width: 100%;
                display: flex;
                height: var(--nui-forms-height, 40px);
                padding: 0px var(--nui-menu-item-space-padding-horizontal, 12px);
                padding-left: 8px;
                align-items: center;
                border-radius: var(--nui-forms-radius, 4px);
                background: var(--nui-menu-search-fill-primary-default, #fff);
                border: none;
                outline: none;

                &::placeholder {
                    color: var(--nui-menu-search-text-primary-default, #62636c);

                    font-family: var(--nui-label-regular-font-family, 'Roboto Flex');
                    font-size: var(--nui-label-regular-font-size, 14px);
                    font-style: normal;
                    font-weight: var(--nui-label-regular-font-weight, 400);
                    line-height: var(--nui-label-regular-line-height, 20px);
                    letter-spacing: var(--nui-label-regular-letter-spacing, 0.18px);
                }
            }

            ui-svg-icon {
                color: var(--nui-menu-search-icon-primary-default, #62636c);
            }
        }
    `,
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UISearchInputOptionComponent {
    searchPlaceholder = input<string>('');
    size = input<'xs' | 'sm' | 'md' | 'lg'>('md');

    clearPreview = output();
    onKeyDownSearch = output<KeyboardEvent>();
    onKeyUp = output();

    private readonly input = viewChild<ElementRef>('input');
    protected iconSize = computed<'sm' | 'md'>(() => {
        const size = this.size();
        switch (size) {
            case 'xs':
                return 'sm';
            case 'lg':
                return 'md';
            default:
                return size;
        }
    });
    focusInput(): void {
        this.input()?.nativeElement?.focus();
    }

    blurInput(): void {
        this.input()?.nativeElement?.blur();
    }

    value(): string | undefined {
        return this.input()?.nativeElement.value;
    }
}
