:where(:root[data-uinew]) :host {
    --width: 260px;
    --max-width: 550px;

    width: var(--width);
    max-width: var(--max-width);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    .label-wrapper {
        padding: 0 0 var(--nui-forms-label-space-padding-bottom, 4px) var(--nui-forms-label-space-padding-left, 2px);
    }

    .help-text-wrapper {
        padding: var(--nui-forms-help-space-padding-top, 4px) 0 0 var(--nui-forms-help-space-padding-left, 4px);
    }

    button {
        display: flex;
        width: inherit;
        min-width: inherit;
        max-width: inherit;
        height: var(--nui-forms-height, 40px);
        padding: 0 var(--nui-forms-space-padding-horizontal, 12px);
        align-items: center;
        gap: var(--nui-forms-space-gap, 8px);
        align-self: stretch;
        border-radius: var(--nui-forms-radius, 4px);

        .thumbnails-container,
        .chips-container {
            display: flex;
            align-items: center;
            gap: var(--nui-space-100, 4px);
            flex: 1 0 0;

            ui-label {
                white-space: nowrap;
            }
        }

        .chips-container {
            width: calc(
                100% - var(--nui-forms-space-padding-horizontal) * 2 -
                    var(--nui-icon-width) - var(--nui-label-space-gap)
            );
            overflow-x: scroll;
            scrollbar-width: none;
        }

        .thumbnails-container {
            width: calc(
                100% - var(--nui-forms-space-padding-horizontal) * 2 -
                    var(--nui-icon-width) - var(--nui-label-space-gap)
            );

            .single-selection {
                width: 100%;
            }
        }

        .custom-label-container,
        ui-label {
            display: flex;
            align-items: center;
            gap: var(--nui-label-space-gap, 8px);
            flex: 1 0 0;
        }
    }

    &.open {
        button {
            border-radius: var(--nui-forms-radius, 4px) var(--nui-forms-radius, 4px) 0 0;
        }
    }

    &:not(.disabled) {
        button {
            cursor: pointer;
        }
    }

    &.primary {
        .arrow {
            color: var(--nui-forms-icon-primary-enabled);
        }

        .label {
            --color: var(--nui-forms-text-label-primary-enabled);
        }

        .help-text {
            --color: var(--nui-forms-text-help-primary-enabled);
        }

        button {
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-primary-enabled);
            background: var(--nui-forms-fill-primary-enabled);

            .custom-label-container,
            ui-label {
                --color: var(--nui-forms-text-input-primary-filled);

                &.placeholder {
                    --color: var(--nui-forms-text-input-primary-enabled);
                }
            }
        }

        &.filled:not(.disabled) {
            &, &:hover {
                .arrow {
                    color: var(--nui-forms-icon-primary-filled);
                }
            }

            .label {
                --color: var(--nui-forms-text-label-primary-filled);
            }

            .help-text {
                --color: var(--nui-forms-text-help-primary-filled);
            }

            button {
                border-color: var(--nui-forms-border-primary-filled);
                background: var(--nui-forms-fill-primary-filled);
            }
        }

        &:hover:not(.disabled, .open) {
            .arrow {
                color: var(--nui-forms-icon-primary-hover);
            }

            .label {
                --color: var(--nui-forms-text-label-primary-hover);
            }

            .help-text {
                --color: var(--nui-forms-text-help-primary-hover);
            }

            button {
                border-color: var(--nui-forms-border-primary-hover);
                background: var(--nui-forms-fill-primary-hover);
            }
        }

        &:has(button.invalid):not(.disabled) {
            .arrow {
                color: var(--nui-forms-icon-primary-enabled);
            }

            .label {
                --color: var(--nui-forms-text-label-primary-error);
            }

            .help-text {
                --color: var(--nui-forms-text-help-primary-error);
            }

            button {
                border-color: var(--nui-forms-border-primary-error);
                background: var(--nui-forms-fill-primary-error);
            }

            &:hover {
                .arrow {
                    color: var(--nui-forms-icon-primary-hover);
                }
            }

            &.filled {
                .arrow {
                    color: var(--nui-forms-icon-primary-error);
                }
            }

            &.open {
                .arrow {
                    color: var(--nui-forms-icon-primary-error);
                }

                button {
                    border-color: var(--nui-forms-border-primary-error);
                    background: var(--nui-forms-fill-primary-error);
                }
            }
        }

        &.disabled {
            .arrow {
                color: var(--nui-forms-icon-primary-disabled);
            }

            .label {
                --color: var(--nui-forms-text-label-primary-disabled);
            }

            .help-text {
                --color: var(--nui-forms-text-help-primary-disabled);
            }

            button {
                border-color: var(--nui-forms-border-primary-disabled);
                background: var(--nui-forms-fill-primary-disabled);

                .custom-label-container,
                ui-label {
                    --color: var(--nui-forms-text-input-primary-disabled);

                    &.placeholder {
                        --color: var(--nui-forms-text-input-primary-disabled);
                    }
                }
            }
        }

        &.open,
        &.open.filled:not(.disabled, .invalid) {
            .arrow {
                color: var(--nui-forms-icon-primary-selected, #1E1F24);
            }

            button {
                border-color: var(--nui-forms-border-primary-selected);
                background: var(--nui-forms-fill-primary-selected);
            }
        }
    }

    &.secondary {
        .arrow {
            color: var(--nui-forms-icon-secondary-enabled);
        }

        button {
            gap: var(--nui-forms-space-secondary-gap, 4px);
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-fill-secondary-enabled);
            background: var(--nui-forms-fill-secondary-enabled);

            .custom-label-container,
            ui-label {
                --color: var(--nui-forms-text-input-secondary-filled);

                &.placeholder {
                    --color: var(--nui-forms-text-input-secondary-enabled);
                }
            }
        }

        &.filled:not(.disabled) {
            &, &:hover {
                .arrow {
                    color: var(--nui-forms-icon-secondary-filled);
                }
            }

            button {
                border: var(--nui-border-width-small, 1px) solid var(--nui-forms-fill-secondary-filled);
                background: var(--nui-forms-fill-secondary-filled);
            }
        }

        &:hover:not(.disabled, .open) {
            .arrow {
                color: var(--nui-forms-icon-secondary-hover);
            }

            button {
                border-color: var(--nui-forms-border-secondary-hover);
                background: var(--nui-forms-fill-secondary-hover);
            }
        }

        &.disabled {
            .arrow {
                color: var(--nui-forms-icon-secondary-disabled);
            }

            button {
                border-color: var(--nui-forms-fill-secondary-disabled);
                background: var(--nui-forms-fill-secondary-disabled);

                .custom-label-container,
                ui-label {
                    --color: var(--nui-forms-text-input-secondary-disabled);

                    &.placeholder {
                        --color: var(--nui-forms-text-input-secondary-disabled);
                    }
                }
            }
        }

        &.open,
        &.open.filled:not(.disabled, .invalid) {
            .arrow {
                color: var(--nui-forms-icon-secondary-selected);
            }

            button {
                border-color: var(--nui-forms-border-secondary-selected);
                background: var(--nui-forms-fill-secondary-selected);
            }
        }
    }
}
