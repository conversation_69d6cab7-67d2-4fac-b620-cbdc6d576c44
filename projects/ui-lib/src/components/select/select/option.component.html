@if (isNewUI()) {
    <ui-label
        #label
        [truncate]="true"
        [size]="size()"
        [weight]="weight()"
        [leadingIcon]="computedIcon()"
        [emptyLeadingIcon]="showMultiselectIcon() && multiSelect && !selected"
        [leadingFlag]="nuiFlag()"
        [uiTooltip]="tooltipText()"
        [uiTooltipDisabled]="!tooltipText()"
        [uiTooltipWidth]="tooltipWidth()">
        <ng-container *ngTemplateOutlet="content" />
    </ui-label>
} @else {
    @if (multiSelect && !tokenField) {
        <div class="checkbox-container">
            @if (multiSelect && !tokenField && selected) {
                <ui-svg-icon
                    icon="checkmark-large"
                    class="checkbox">
                </ui-svg-icon>
            }
        </div>
    }

    @if (icon && !svgIcon) {
        <ui-icon
            class="icon"
            [icon]="icon"></ui-icon>
    }
    @if (svgIcon) {
        <ui-svg-icon
            class="icon"
            [icon]="svgIcon"></ui-svg-icon>
    }

    @if (flag) {
        <ui-flag
            class="flag"
            [size]="tokenField ? 'tiny' : 'small'"
            [culture]="flag"></ui-flag>
    }
    @if (image !== undefined) {
        <ui-image
            class="image"
            [image]="image"
            [bordered]="withBorder"></ui-image>
    }

    <div class="container">
        <ng-container *ngTemplateOutlet="content"></ng-container>
    </div>
}

<ng-template #content>
    <ng-content></ng-content>
</ng-template>
