import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
    Component,
    computed,
    effect,
    ElementRef,
    inject,
    input,
    model,
    OnDestroy,
    OnInit,
    output,
    untracked,
    viewChild
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { catchError, firstValueFrom, Observable, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { CREATIVE_PREVIEW_HOST, IDynamicPropertyData, PreviewType } from '../../types/creative-preview';

class HttpError extends Error {
    constructor(
        message: string,
        public originalError: unknown
    ) {
        super(message);
        this.name = 'HttpError';
    }
}

type Command = { action: 'stop' | 'play' | 'pause' };
type SeekCommand = { action: 'seek'; time: number | 'preload-frame' };
type MuteCommand = { action: 'mute'; isMuted: boolean };
type DynamicPropertiesCommand = {
    action: 'renderDynamicProperties';
    dynamicProperties: IDynamicPropertyData[];
};
type AdApiCommand = Command | SeekCommand | MuteCommand | DynamicPropertiesCommand;

type PreviewUrlOptions = {
    creativeset: string;
    creative: string;
    type: string;
    responsive: boolean;
    responsiveMode: 'contain' | 'cover';
    accessToken: string;
    snapshot?: string;
    autoplay?: boolean;
    env?: 'feed';
};

/**
 * Provides a creative preview.
 *
 * This component calls the CreativePreviewService.
 * Therefore, a global http interceptor needs to be configured to attach the bearer token automatically to requests.
 *
 * Configure the preview host in the containing module `CREATIVE_PREVIEW_HOST`
 *
 *
 * ```ts
 *   import { CREATIVE_PREVIEW_HOST } from '@bannerflow/ui';
 *
 *   {
 *       provide: CREATIVE_PREVIEW_HOST,
 *       useValue: 'http://api.bannerflow.com/preview'
 *   }
 * ```
 */
@Component({
    selector: 'ui-creative-preview',
    standalone: true,
    templateUrl: './creative-preview.component.html',
    styleUrl: './creative-preview.component.scss'
})
export class UICreativePreviewComponent implements OnInit, OnDestroy {
    private previewFrame = viewChild<ElementRef<HTMLIFrameElement>>('previewFrame');
    creativeset = input.required<string>();
    creative = input.required<string>();
    type = input<PreviewType>('animated');
    responsive = input<boolean>(true);
    responsiveMode = input<'contain' | 'cover'>('contain');
    snapshot = input<string>('');
    env = input<'feed'>();
    autoplay = input<boolean>(true);
    token = model<string | undefined>();
    adRendered = output<void>();

    previewUrl = computed(() => {
        const creativeset = untracked(() => this.creativeset());
        const creative = untracked(() => this.creative());
        const type = this.type();
        const responsive = this.responsive();
        const responsiveMode = this.responsiveMode();
        const accessToken = this.token();
        const snapshot = this.snapshot();
        const autoplay = this.autoplay();
        const env = this.env();

        if (!creativeset || !creative) {
            throw new Error('Creativeset and creative id must be set');
        }

        if (!accessToken) {
            return;
        }

        return this.sanitizer.bypassSecurityTrustResourceUrl(
            this.getCreativePreviewUrl({
                creativeset,
                creative,
                type,
                responsive,
                responsiveMode,
                accessToken,
                snapshot,
                autoplay,
                env
            })
        );
    });

    private readonly previewHost = inject(CREATIVE_PREVIEW_HOST);
    private readonly previewOrigin = new URL(this.previewHost).origin;
    private readonly http = inject(HttpClient);
    private readonly sanitizer = inject(DomSanitizer);

    constructor() {
        effect(
            async () => {
                const creativeset = this.creativeset();
                const creative = this.creative();
                const accessToken = await firstValueFrom(this.getAccessToken(creativeset, creative));
                if (accessToken) {
                    this.token.set(accessToken);
                }
            },
            { allowSignalWrites: true }
        );
    }

    ngOnInit() {
        window.addEventListener('message', this.onIframeMessage);
    }

    ngOnDestroy() {
        window.removeEventListener('message', this.onIframeMessage);
    }

    play(): void {
        this.sendCommandToIframe({ action: 'play' });
    }

    pause(): void {
        this.sendCommandToIframe({ action: 'pause' });
    }

    stop(): void {
        this.sendCommandToIframe({ action: 'stop' });
    }

    seek(seconds: number | 'preload-frame'): void {
        this.sendCommandToIframe({ action: 'seek', time: seconds });
    }

    mute(isMuted: boolean): void {
        this.sendCommandToIframe({ action: 'mute', isMuted });
    }

    renderDynamicProperties(dynamicProperties: IDynamicPropertyData[]): void {
        this.sendCommandToIframe({ action: 'renderDynamicProperties', dynamicProperties });
    }

    private getCreativePreviewUrl({
        creativeset,
        creative,
        accessToken,
        responsive,
        responsiveMode,
        type,
        autoplay,
        snapshot,
        env
    }: PreviewUrlOptions) {
        const url = new URL(`${this.previewHost}/${creativeset}/${creative}/ui`);
        const searchParams = url.searchParams;

        searchParams.set('preview-type', type);
        searchParams.set('responsive', String(+responsive));
        searchParams.set('responsive-mode', responsiveMode);
        searchParams.set('access-token', accessToken);

        if (autoplay === false) {
            searchParams.set('autoplay', 'off');
            searchParams.set('seek', 'preload-frame');
        }

        if (snapshot) {
            searchParams.set('snapshot', snapshot);
        }

        if (env) {
            searchParams.set('env', env);
        }

        return url.toString();
    }

    private getAccessToken(creativeset: string, creative: string): Observable<string | null> {
        const url = new URL(`${this.previewHost}/preview-url`);
        url.searchParams.set('creativeset', creativeset);
        url.searchParams.set('creative', creative);

        return this.http.get<{ previewUrl: string }>(url.toString()).pipe(
            map(({ previewUrl }) => {
                const tmpUrl = new URL(previewUrl);
                return tmpUrl.searchParams.get('access-token');
            }),
            catchError((e: unknown) => {
                const status = (e as HttpErrorResponse)?.status;
                const reason = (e as HttpErrorResponse)?.statusText;
                return throwError(
                    () => new HttpError(`Failed to get access token - ${status}: ${reason}`, e)
                );
            })
        );
    }

    private sendCommandToIframe(cmd: AdApiCommand): void {
        const iframeWindow = this.previewFrame()?.nativeElement.contentWindow;
        if (!iframeWindow) {
            return;
        }

        const message = { type: 'cmd', cmd };

        iframeWindow.postMessage(message, this.previewHost);
    }

    private onIframeMessage = (message: MessageEvent): void => {
        // Verify origin for security and protection, as other browser extensions also use this communication method
        if (message.origin !== this.previewOrigin) {
            return;
        }

        const { event, data } = message.data;

        switch (event) {
            case 'render':
                this.adRendered.emit();
                break;
            case 'error':
                console.error(data);
                break;
            default:
                break;
        }
    };
}
