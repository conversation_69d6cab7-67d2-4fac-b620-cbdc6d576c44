import { Meta, moduleMetadata, type StoryObj } from '@storybook/angular';
import { UICreativePreviewComponent } from './creative-preview.component';
import { HttpClientModule } from '@angular/common/http';
import { CREATIVE_PREVIEW_HOST } from '../../types/creative-preview';

const meta: Meta<UICreativePreviewComponent> = {
    component: UICreativePreviewComponent,
    title: 'Components/CreativePreview',
    decorators: [
        moduleMetadata({
            imports: [HttpClientModule],
            declarations: [],
            providers: [
                { provide: CREATIVE_PREVIEW_HOST, useValue: 'http://api.bannerflow.local/preview' }
            ]
        })
    ],
    argTypes: {
        type: { control: 'radio', options: ['animated', 'image'] },
        responsive: { control: 'boolean' },
        responsiveMode: { control: 'radio', options: ['contain', 'cover'] }
    }
};
export default meta;

type Story = StoryObj<UICreativePreviewComponent>;

export const Base: Story = {
    args: {
        creativeset: '1',
        creative: '1',
        type: 'animated',
        responsive: true,
        responsiveMode: 'contain'
    }
};
