import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UILogoComponent } from './logo.component';

const meta: Meta<UILogoComponent> = {
    title: 'Components/Logo',
    component: UILogoComponent,
    decorators: [moduleMetadata({})]
};
export default meta;

type Story = StoryObj<UILogoComponent>;

export const Default: Story = {
    args: {
        small: false
    },
    render: args => ({
        ...args,
        props: {
            ...args
        },
        template: `
<div class="wrapper" [style.height]="'20px'">
    <ui-logo [small]="small"/>
</div>
`
    })
};
