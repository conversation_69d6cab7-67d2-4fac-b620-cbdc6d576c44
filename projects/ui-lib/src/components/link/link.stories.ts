import type { <PERSON>a, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UILinkComponent } from './link.component';
import { UILinkDirective } from './link.directive';

const meta: Meta<UILinkComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Link',
    component: UILinkComponent,
    decorators: [
        moduleMetadata({
            imports: [UILinkComponent, UILinkDirective]
        })
    ],
    argTypes: {
        size: {
            control: { type: 'radio' },
            options: ['sm', 'md'],
            description: 'The size of the link',
            defaultValue: 'md'
        },
        href: {
            control: { type: 'text' },
            description: 'The URL that the hyperlink points to',
            defaultValue: 'https://bannerflow.com'
        },
        target: {
            control: { type: 'radio' },
            options: ['_self', '_blank', '_parent', '_top'],
            description: 'Specifies where to open the linked document',
            defaultValue: '_blank'
        }
    }
};

export default meta;
type Story = StoryObj<UILinkComponent>;

export const Default: Story = {
    args: {
        size: 'md',
        href: 'https://bannerflow.com',
        target: '_blank'
    },
    render: args => ({
        props: args,
        template: `<ui-link [href]="href" [target]="target" [size]="size">Link using <code>ui-link</code> component</ui-link>`
    })
};

export const Small: Story = {
    ...Default,
    args: {
        ...Default.args,
        size: 'sm'
    }
};

export const Directive: Story = {
    render: args => ({
        props: args,
        template: `<a uiLink [href]="href" [target]="target">Link using uiLink directive</a>`
    })
};

export const Class: Story = {
    render: args => ({
        props: args,
        template: `<a class="ui-link" [href]="href" [target]="target">Link using .ui-link class</a>`
    })
};
