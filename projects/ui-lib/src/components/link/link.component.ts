import { Component, input } from '@angular/core';
import { DATASET_SIZE } from '../../services/uinew-theme.service';

export type LinkSize = 'sm' | 'md';

/**
 * Link component for navigation elements.
 *
 * @description
 * Styles are defined in new-ui.scss.
 * The .ui-link class handles component styling.
 * Class is kept separate to enable reuse via directive or direct class application.
 */
@Component({
    selector: 'ui-link',
    templateUrl: './link.component.html',
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UILinkComponent {
    href = input.required<string>();
    target = input<string>('_self');
    size = input<LinkSize>('md');
}
