import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UILabelComponent } from './label.component';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';

interface UILabelStoryProps {
    size?: 'xs' | 'sm' | 'md';
    type?: 'default' | 'brand' | 'secondary' | 'disabled' | 'inverted' | 'destructive';
    weight?: 'regular' | 'bold';
    leadingIcon?: string;
    trailingIcon?: string;
    content?: string;
}
type Story = StoryObj<UILabelStoryProps>;

const meta: Meta<UILabelComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Label',
    component: UILabelComponent,
    decorators: [
        moduleMetadata({
            imports: [UILabelComponent, CommonModule, FormsModule, BrowserModule]
        })
    ],
    argTypes: {
        size: {
            options: ['xs', 'sm', 'md'],
            control: { type: 'radio' },
            description: 'Size size of the label'
        },
        type: {
            options: ['default', 'brand', 'secondary', 'disabled', 'inverted', 'destructive'],
            control: { type: 'select' },
            description: 'Visual style of the label'
        },
        weight: {
            options: ['regular', 'bold'],
            control: { type: 'radio' },
            description: 'Font weight of the label'
        },
        leadingIcon: {
            options: [undefined, 'check'],
            control: { type: 'select' },
            description: 'Icon displayed before the label text'
        },
        trailingIcon: {
            options: [undefined, 'check'],
            control: { type: 'select' },
            description: 'Icon displayed after the label text'
        }
    }
};

export default meta;

const DefaultRender = (args: UILabelStoryProps) => ({
    props: args,
    template: `<ui-label
        [size]="size"
        [type]="type"
        [weight]="weight"
        [leadingIcon]="leadingIcon"
        [trailingIcon]="trailingIcon">
        {{content}}
    </ui-label>`
});

export const Default: Story = {
    args: {
        content: 'Default Label'
    },
    render: DefaultRender
};

export const ExtraSmall: Story = {
    args: {
        size: 'xs',
        content: 'Extra Small'
    },
    render: DefaultRender
};

export const Small: Story = {
    args: {
        size: 'sm',
        content: 'Small'
    },
    render: DefaultRender
};

export const Medium: Story = {
    args: {
        size: 'md',
        content: 'Medium'
    },
    render: DefaultRender
};

export const Bold: Story = {
    args: {
        content: 'Bold',
        weight: 'bold'
    },
    render: DefaultRender
};

export const WithLeadingIcon: Story = {
    args: {
        leadingIcon: 'check',
        content: 'With Leading Icon'
    },
    render: DefaultRender
};

export const WithTrailingIcon: Story = {
    args: {
        trailingIcon: 'check',
        content: 'With Trailing Icon'
    },
    render: DefaultRender
};

export const WithBothIcons: Story = {
    args: {
        leadingIcon: 'check',
        trailingIcon: 'check',
        content: 'With Both Icons'
    },
    render: DefaultRender
};

export const LongText: Story = {
    args: {
        content:
            'This is a very long label text that demonstrates how the label component handles longer content that might wrap to multiple lines depending on container width'
    },
    render: DefaultRender
};
