import { Component, input, output } from '@angular/core';
import { DATASET_SIZE } from '../../services/uinew-theme.service';
import { UINUIIcon } from '../../types';
import { UIFlagComponent } from '../icon';
import { UINUIFlag } from '../icon/flag/flag.nui.types';
import { UISVGIconComponent } from '../icon/svg-icon/svg-icon.component';

export type UILabelType = 'primary' | 'brand' | 'secondary' | 'disabled' | 'inverted' | 'destructive';
export type UILabelWeight = 'bold' | 'regular';

/**
 * Label text component
 *
 * NOTE: This is meant to be used only when NUI is enabled
 *
 * Nui Style params:
 * `--color`: sets the text color property
 * `--line-height`: sets the line-height property
 */
@Component({
    imports: [UIFlagComponent, UISVGIconComponent],
    selector: 'ui-label',
    template: `
        @let _leadingIcon = leadingIcon();
        @let _trailingIcon = trailingIcon();
        @let _leadingFlag = leadingFlag();
        @if (_leadingFlag) {
            <ui-flag
                [nuiSize]="size()"
                [nuiFlag]="_leadingFlag" />
        } @else if (_leadingIcon) {
            <ui-svg-icon
                (click)="leadingIconClick.emit($event)"
                [size]="size()"
                icon="none"
                [nuiIcon]="_leadingIcon" />
        } @else if (emptyLeadingIcon()) {
            <div class="empty-icon"></div>
        }

        <ng-content />
        @if (_trailingIcon) {
            <ui-svg-icon
                (click)="trailingIconClick.emit($event)"
                [size]="size()"
                icon="none"
                [nuiIcon]="_trailingIcon" />
        }
    `,
    styles: [
        `
            :host {
                display: flex;
                align-items: center;
                gap: var(--nui-label-space-gap);
                color: var(--color, var(--nui-label-text-neutral));
                --line-height: var(--nui-label-regular-line-height);

                &,
                &.label {
                    font-size: var(--nui-label-regular-font-size);
                    line-height: var(--line-height);
                }

                ui-flag {
                    display: inline-flex;
                    width: var(--nui-icon-width, 20px);
                    height: var(--nui-icon-height, 20px);
                    padding: 2px;
                    justify-content: center;
                    align-items: center;
                }

                /* Types */
                &.primary {
                    --color: var(--nui-label-text-neutral);
                }
                &.brand {
                    --color: var(--nui-label-text-brand);
                }
                &.secondary {
                    --color: var(--nui-label-text-secondary);
                }
                &.disabled {
                    --color: var(--nui-label-text-disabled);
                }
                &.inverted {
                    --color: var(--nui-label-text-inverted);
                }
                &.destructive {
                    --color: var(--nui-label-text-destructive);
                }

                /* Font weight */
                &.regular {
                    font-weight: var(--nui-label-regular-font-weight);
                }
                &.bold {
                    font-weight: var(--nui-label-bold-font-weight);
                }

                .empty-icon {
                    width: var(--nui-icon-width, 1em);
                }

                &.truncate {
                    text-overflow: ellipsis;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    display: block;
                    vertical-align: middle;

                    ui-flag {
                        vertical-align: bottom;
                    }

                    .empty-icon {
                        display: inline;
                        padding-right: calc(var(--nui-label-space-gap) / 2);
                    }

                    .empty-icon,
                    ui-svg-icon {
                        vertical-align: middle;
                        margin-right: var(--nui-label-space-gap);
                    }

                    &:has(.empty-icon) {
                        padding-left: var(--nui-icon-width, 1em);
                    }
                }
            }
        `
    ],
    host: {
        '[class]': 'type()',
        '[class.regular]': 'weight() === "regular"',
        '[class.bold]': 'weight() === "bold"',
        '[class.truncate]': 'truncate()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UILabelComponent {
    /**
     * Size of the label.
     */
    size = input<'xs' | 'sm' | 'md'>('md');

    /**
     * Type of the label.
     */
    type = input<UILabelType>();

    /**
     * Weight of the label.
     */
    weight = input<UILabelWeight>('regular');

    /**
     * Leading icon for the label.
     */
    leadingIcon = input<UINUIIcon>();

    /**
     * Leading flag for the label.
     */
    leadingFlag = input<UINUIFlag>();

    /**
     * Trailing icon for the label.
     */
    trailingIcon = input<UINUIIcon>();

    /**
     * Show an empty leading icon for the label.
     */
    emptyLeadingIcon = input<boolean>(false);

    /**
     * Truncate
     */
    truncate = input<boolean>(false);

    leadingIconClick = output<MouseEvent>();
    trailingIconClick = output<MouseEvent>();
}
