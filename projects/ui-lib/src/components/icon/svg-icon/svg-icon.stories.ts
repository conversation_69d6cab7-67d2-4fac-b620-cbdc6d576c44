import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIDomAttachService } from '../../../services';
import { icons } from './icons';
import { UISVGIconComponent } from './svg-icon.component';
import { UISvgIconService } from './svg-icon.service';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UISVGIconComponent> = {
    title: 'Components/Icons/UISVGIcon',
    component: UISVGIconComponent,
    decorators: [
        moduleMetadata({
            imports: [UISVGIconComponent],
            providers: [UISvgIconService, UIDomAttachService]
        })
    ]
};

export default meta;
type Story = StoryObj<UISVGIconComponent>;

export const AllIcons: Story = {
    render: () => ({
        template: `
            <div style="display: flex; flex-wrap: wrap; gap: 30px; padding: 20px;">
                <div *ngFor="let icon of icons" style="text-align: center; width: 120px;">
                    <ui-svg-icon [icon]="icon" style="display: block; margin: 0 auto; transform: scale(1.5);"/>
                    <div style="margin-top: 8px; font-size: 12px;">{{ icon }}</div>
                </div>
            </div>
        `,
        props: {
            icons: icons
        }
    })
};

export const NuiIcon: Story = {
    ...NUI_STORY_SETTINGS,
    render: () => ({
        template: `
            <ui-svg-icon [icon]="'kebab'" [nuiIcon]="'settings'"/>
        `,
        props: {
            icons: icons
        }
    })
};

export const Kebab: Story = {
    args: {
        icon: 'kebab'
    }
};

export const Edit: Story = {
    args: {
        icon: 'edit',
        fill: '#1b75dd'
    }
};
