@let _currentIcon = currentIcon() | lowercase;

@if (isCustomNuiIcon()) {
    <svg
        data-testid="svg-icon"
        attr.class="icon-{{ _currentIcon }} nui-icon {{ size() }}">
        <use attr.xlink:href="#icon-nui-{{ _currentIcon }}"></use>
    </svg>
} @else if (isMaterialIcon()) {
    <span
        class="material-symbols-rounded {{ size() }}"
        [class.filled]="nuiIconFilled()">
        {{ _currentIcon }}
    </span>
} @else if (_currentIcon !== 'none') {
    <svg
        data-testid="svg-icon"
        attr.class="icon icon-{{ _currentIcon }}">
        <use attr.xlink:href="#icon-{{ set }}-{{ _currentIcon }}"></use>
    </svg>
}
