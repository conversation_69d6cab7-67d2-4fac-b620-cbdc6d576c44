/** Generated by fetch-icons.js - Do not edit manually! */
export const icons = [
    'action-disabled',
    'add',
    'ad-default',
    'ads-create',
    'ads-group',
    'ads-s',
    'ai_stars',
    'alert',
    'align-bottom',
    'align-canvas-bottom',
    'align-canvas-center',
    'align-canvas-left',
    'align-canvas-middle',
    'align-canvas-right',
    'align-canvas-top',
    'align-center',
    'align-left',
    'align-middle',
    'align-right',
    'align-top',
    'analytics-l',
    'analytics-s',
    'animation',
    'announcement',
    'arrange-top',
    'arrange-up',
    'arrow-alt-left',
    'arrow-alt-up',
    'arrow-down',
    'arrow-left',
    'arrow-right',
    'arrow-up',
    'assign-cs',
    'asterisk',
    'audience-network',
    'banner-set',
    'banner-sets',
    'big-preview',
    'bookmark-outline',
    'border-joint',
    'border-separate',
    'button',
    'camera',
    'campaign',
    'campaign-add-to',
    'campaign-forward-to',
    'campaign-go-to',
    'campaigns-l',
    'campaigns-l-create-new',
    'campaigns-s',
    'campaigns-s-create-new',
    'campaign-tile',
    'campaign-upload',
    'campaign-w-frame',
    'cancel-24',
    'carousel',
    'checkbox-checkmark-default',
    'checkbox-empty',
    'checkbox-false',
    'checkbox-true',
    'checkmark-large',
    'choice',
    'clear',
    'click',
    'clicks',
    'close',
    'cloud',
    'cloud-large',
    'cloud-l-negative',
    'cloud-no-arrow-s-default',
    'cloud-s',
    'cloud-warning-s-error',
    'cloud-warning-s-error1',
    'cm-published',
    'cm-published-changed',
    'code',
    'code-large',
    'collapse',
    'color-picker',
    'columns-grid',
    'comment-filled',
    'comments',
    'comments-add',
    'comments-checkmark',
    'comments-user',
    'composition',
    'composition-new',
    'connect',
    'copy',
    'copy-s',
    'corner-b-l',
    'corner-b-r',
    'corner-separate',
    'corner-t-l',
    'corner-t-r',
    'creative_300x60',
    'creative_600x900',
    'creative_1080x1080',
    'creative_1080x1350',
    'creative_1080x1920',
    'creative_1200x628',
    'creative_1920x1080',
    'creative-set',
    'creative-set-new-s',
    'cross-small',
    'crown',
    'cs-choices',
    'ctr',
    'customized',
    'de-action',
    'decision-tree',
    'degrees',
    'delete',
    'delete-lar',
    'de-reserved-action',
    'direction_ltr',
    'direction_rtl',
    'direction-down',
    'direction-left',
    'direction-right',
    'direction-up',
    'disabled',
    'discard-changes',
    'display-campaign',
    'distribute-canvas-horisontally',
    'distribute-canvas-vertically',
    'distribute-horisontally',
    'distribute-vertically',
    'download',
    'drag-drop',
    'dropdown-tiny-triangle',
    'duplicate-s',
    'dynamic-feed',
    'edit',
    'error',
    'error-l-negative',
    'error-l-negative1',
    'exclamation_mark',
    'expand',
    'export',
    'facebook',
    'fallback',
    'fallback-cs',
    'fallback-default',
    'fallback-missing',
    'fallback-warning',
    'feed',
    'feeds',
    'files',
    'file-size',
    'filter',
    'filter-2',
    'filter-blur',
    'filter-clear',
    'filter-contrast',
    'filter-grayscale',
    'filter-invert',
    'filter-saturate',
    'filter-sepia',
    'flip-h',
    'flip-v',
    'folder',
    'folder-add',
    'folder-move',
    'folder-multiple',
    'folder-open',
    'folder-shared',
    'font-file',
    'font-folder',
    'gif-frame',
    'gif-frame-small',
    'global-app-bundle',
    'go-to-url',
    'go-to-url-large',
    'hamburger',
    'heart-outline',
    'html',
    'html-crossed',
    'image',
    'image-add',
    'image-frames',
    'image-frames1',
    'image-upload',
    'impressions',
    'indeterminate-checkmark',
    'indeterminate-checkmark-l',
    'infinity',
    'info-l',
    'info-large',
    'instagram',
    'kebab',
    'keyboard',
    'like-filled',
    'like-outline',
    'lines',
    'link',
    'linkedin',
    'linkedin_info',
    'linkedin_comment',
    'linkedin_like',
    'linkedin_repost',
    'linkedin_send',
    'link-l',
    'link-s',
    'list',
    'list1',
    'live-icon',
    'localized',
    'lock-closed',
    'lock-open',
    'log',
    'loop-forever',
    'loop-x-times',
    'mail',
    'mask',
    'menu-checked',
    'messenger',
    'meta',
    'migrated',
    'minus-small',
    'overlap',
    'override',
    'padding_bottom',
    'padding_left',
    'padding_right',
    'padding_separate',
    'padding_top',
    'padding-bottom',
    'padding-left',
    'padding-right',
    'padding-top',
    'pangle',
    'past',
    'pin',
    'pinterest',
    'pixel',
    'playback-end-small',
    'playback-jump-sec-back',
    'playback-jump-sec-forward',
    'playback-pause',
    'playback-pause-small',
    'playback-play',
    'playback-play-small',
    'playback-rec',
    'playback-start',
    'playback-start-small',
    'playback-step',
    'playback-step-left-small',
    'playback-step-right-small',
    'plus',
    'plus-small',
    'plus-tiny',
    'preview_ads',
    'progress',
    'question-mark',
    'question-mark-s',
    'rearrange',
    'repeat',
    'reply',
    'restart-warning',
    'root-folder',
    'rotate-left',
    'rotate-right',
    'rotate-x',
    'rotate-y',
    'rule-ab-split',
    'rule-rotation',
    'rule-schedule',
    'schedule',
    'search',
    'selection-tool',
    'send',
    'send_fb',
    'settings',
    'settings-filter',
    'shape-arrow',
    'shape-oval',
    'shape-rectangle',
    'shape-transition',
    'shape-transition-clear',
    'shape-triangle',
    'share',
    'share-alt',
    'share-blue',
    'shopping-cart',
    'size',
    'skew',
    'skew-x',
    'skew-y',
    'smartphone-14',
    'smiley',
    'snapchat',
    'social-campaign',
    'social-fx',
    'sort',
    'status-dot',
    'strikethrough',
    'styles-lorum-ipsum-view',
    'styles-minimal-view',
    'swap',
    'sync',
    'sync-l',
    'sync-large',
    'sync-small',
    'text',
    'text-align-h-center',
    'text-align-h-justify',
    'text-align-h-left',
    'text-align-h-right',
    'text-align-v-bottom',
    'text-align-v-full-hight',
    'text-align-v-middle',
    'text-align-v-top',
    'text-style-apply-formatting',
    'text-styles-character-spacing',
    'text-styles-clear-formatting',
    'text-styles-line-spacing',
    'text-styles-paragraph-spacing',
    'text-styles-subscript',
    'text-styles-superscript',
    'thumbnails',
    'thumbnails-with-preview',
    'tiktok',
    'tiktok_heart',
    'tiktok_more',
    'tiktok_saved',
    'tiktok_share',
    'time',
    'translation',
    'tt-instant-page',
    'tt-website',
    'twitter',
    'underline',
    'undo-arrow',
    'unlink',
    'uplift-report',
    'upload-file',
    'upload-media',
    'uppercase',
    'user',
    'users',
    'version',
    'video',
    'video-heavy',
    'view-collapse',
    'view-expand',
    'visibility-hidden',
    'visibility-visible',
    'volume-on',
    'warning',
    'warning-large',
    'warning-l-negative',
    'warning-l-outlines',
    'weight',
    'widget',
    'widget1',
    'widget-new',
    'youtube',
    'zoom',
    'zoom-in',
    'zoom-out',
    'action',
    'other-sizes-arrow-upload',
    'notification',
    'notification-filled',
    'question-mark-outlined',
    'settings-outlined',
    'volume_on',
    'volume_off',
    'qr-code',
    'cs_history',
    'twitter-colored',
    'snapchat-colored',
    'pinterest-colored',
    'meta-colored',
    'tiktok-colored',
    'linkedin-colored',
    'googlefonts-colored',
    'linkedin-reactions-colored',
    'cursor-resize',
    'cursor-rectangle',
    'cursor-button',
    'cursor-rotate',
    'cursor-text',
    'cursor-zoom-in',
    'cursor-oval',
    'cursor-zoom-out',
    'cursor-polygon',
    'cursor-selection-locked',
    'cursor-star',
    'cursor-selection',
    'cursor-heart',
    'cursor-arrow',
    'cursor-other',
    'cursor-move-directions'
] as const;

export type Icon = (typeof icons)[number];
