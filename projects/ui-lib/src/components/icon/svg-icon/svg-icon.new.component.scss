:where(:root[data-uinew]) :host {    
    position: relative;
    line-height: 1em;
    display: inline-flex;
    align-items: center;
    color: var(--color, var(--nui-icon-primary, #1E1F24));
    font-size: var(--nui-icon-font-icon-size);

    .material-symbols-rounded {
        font-variation-settings:
            'FILL' 0,
            'wght' 300,
            'GRAD' -25,
            'opsz' 24;
        font-size: var(--nui-icon-font-icon-size);

        &.filled {
            font-variation-settings:
                'FILL' 1,
                'wght' 300,
                'GRAD' -25,
                'opsz' 24;
        }

        &.xs,
        &.sm {
            font-variation-settings:
                'FILL' 0,
                'wght' 300,
                'GRAD' -25,
                'opsz' 20;

            &.filled {
                font-variation-settings:
                    'FILL' 1,
                    'wght' 300,
                    'GRAD' -25,
                    'opsz' 20;
            }
        }
    }

    .icon,
    .nui-icon {
        display: inline-block;
        width: var(--nui-icon-font-icon-size);
        height: var(--nui-icon-font-icon-size);
        stroke-width: 1px;
    }
}
