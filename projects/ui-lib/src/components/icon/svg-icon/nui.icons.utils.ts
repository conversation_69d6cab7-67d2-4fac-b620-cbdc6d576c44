import { MaterialSymbol } from 'material-symbols';
import { UIIcon, UINUIIcon } from '../../../types';
import { icons as newIcons, Icon as NUIIconType } from './nui.icons';

const map: Partial<Record<UIIcon, UINUIIcon | MaterialSymbol>> = {
    'arrow-right': 'chevron_right',
    'checkbox-checkmark-default': 'check',
    'checkbox-false': 'check_box_outline_blank',
    'checkbox-true': 'check',
    'folder-move': 'drive_file_move',
    'folder-multiple': 'folder_copy',
    'folder-shared': 'folder_shared',
    'font-file': 'font-file',
    'go-to-url': 'open_in_new',
    'go-to-url-large': 'open_in_new',
    'googlefonts-colored': 'brand-googlefonts-colored',
    'indeterminate-checkmark': 'check_indeterminate_small',
    'indeterminate-checkmark-l': 'check_indeterminate_small',
    kebab: 'more_vert',
    'upload-file': 'upload',
    add: 'add',
    close: 'close',
    delete: 'delete',
    download: 'download',
    edit: 'edit',
    error: 'error',
    folder: 'folder',
    search: 'search',
    settings: 'settings'
};

export function mapOldIcon(icon: UIIcon | 'none'): UINUIIcon | 'none' | undefined {
    if (icon === 'none') {
        return icon;
    }
    return map[icon];
}

export function isCustomIcon(icon: string): icon is NUIIconType {
    return ([...newIcons] as string[]).includes(icon);
}
