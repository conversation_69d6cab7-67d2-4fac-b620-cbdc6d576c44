import { Injectable, OnDestroy, inject } from '@angular/core';
import { IUIAttachedDOMComponent } from '../../../types';
import { UIDomAttachService } from './../../../services/dom-attach.service';
import { NUISvgIconsComponent } from './nui.svg-icons.component';
import { UISvgIconsComponent } from './svg-icons.component';

@Injectable({ providedIn: 'root' })
export class UISvgIconService implements OnDestroy {
    private domAttach = inject(UIDomAttachService);

    private attachedSvg?: IUIAttachedDOMComponent<UISvgIconsComponent>;
    private nuiAttachedSvg?: IUIAttachedDOMComponent<NUISvgIconsComponent>;

    constructor() {
        this.add();
    }

    add(): void {
        this.attachedSvg = this.domAttach.addComponent(UISvgIconsComponent, document.body);
    }

    remove(): void {
        this.attachedSvg?.domPortalHost.detach();
    }

    addNUI(): void {
        this.nuiAttachedSvg ??= this.domAttach.addComponent(NUISvgIconsComponent, document.body);
    }

    removeNUI(): void {
        this.nuiAttachedSvg?.domPortalHost.detach();
    }

    ngOnDestroy(): void {
        this.remove();
    }
}
