import { LowerCasePipe } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    Input,
    OnChanges,
    computed,
    effect,
    inject,
    input
} from '@angular/core';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UIIcon, UINUIIcon } from '../../../types';
import { isCustomIcon, mapOldIcon } from './nui.icons.utils';
import { UISvgIconService } from './svg-icon.service';

@Component({
    imports: [LowerCasePipe],
    selector: 'ui-svg-icon',
    templateUrl: './svg-icon.component.html',
    styleUrls: ['./svg-icon.component.scss', './svg-icon.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'icon',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UISVGIconComponent implements OnChanges {
    private elementRef = inject(ElementRef);
    private uiNewThemeService = inject(UINewThemeService);

    /**
     * Name of icon to be display. Case insensitive.
     */
    icon = input.required<UIIcon | 'none'>();

    /**
     * Name of the new icon to be display. Only when NUI is enabled
     */
    nuiIcon = input<UINUIIcon>();

    /**
     * NUI size
     */
    size = input<'xs' | 'sm' | 'md'>('md');

    /**
     * NUI Material Symbol fill
     */
    nuiIconFilled = input(false);

    /**
     * Fill color of the icon. Pass an array if you want to apply multiple.
     */
    @Input() fill: string | string[];

    /**
     * From which icon set the icon should load.
     */
    @Input() set: 'ui' | 'studio' | 'misc' = 'ui';

    // true when the icon is a nui icon and should be rendered from material-symbols
    isMaterialIcon = computed(() => this.computeIsMaterialIcon());

    // true when the icon is a nui icon and should be rendered from the svgs definitions
    isCustomNuiIcon = computed(() => this.computeIsCustomNuiIcon());

    // Mapped icons depending on if nui is enabled or not
    currentIcon = computed(() => this.computeCurrentIcon());

    constructor() {
        const svgIconService = inject(UISvgIconService); // init
        effect(() => {
            if (this.uiNewThemeService.isNewThemeEnabled()) {
                svgIconService.addNUI();
            } else {
                svgIconService.removeNUI();
            }
        });
    }

    /**
     * An input has been changed
     */
    ngOnChanges(): void {
        this.setFill();
    }

    private computeCurrentIcon(): string {
        if (!this.uiNewThemeService.isNewThemeEnabled()) {
            return this.icon();
        }
        const nuiIcon = this.nuiIcon();
        if (nuiIcon) {
            return nuiIcon;
        }
        const icon = this.icon();
        return mapOldIcon(icon) ?? icon;
    }

    private computeIsMaterialIcon(): boolean {
        if (!this.uiNewThemeService.isNewThemeEnabled()) {
            return false;
        }
        const nuiIcon = this.nuiIcon();
        if (nuiIcon) {
            return !isCustomIcon(nuiIcon);
        }
        const icon = this.icon();
        const mappedOldIcon = mapOldIcon(icon);
        return !!mappedOldIcon && !isCustomIcon(mappedOldIcon);
    }

    private computeIsCustomNuiIcon(): boolean {
        if (!this.uiNewThemeService.isNewThemeEnabled()) {
            return false;
        }
        const nuiIcon = this.nuiIcon();
        if (nuiIcon) {
            return isCustomIcon(nuiIcon);
        }
        const icon = this.icon();
        return isCustomIcon(mapOldIcon(icon) ?? '');
    }

    /**
     * Change fill color.
     */
    private setFill(): void {
        this.clearFill();
        const element = this.elementRef.nativeElement;
        if (this.fill && element) {
            const fill = Array.isArray(this.fill) ? this.fill : [this.fill];

            fill.forEach((color, index) => {
                element.style.setProperty(`--color${index + 1}`, color);
            });
        }
    }

    /**
     * Remove all fill colors
     */
    private clearFill(): void {
        const element = this.elementRef.nativeElement as HTMLDivElement;
        if (element) {
            let index = 1;
            while (element.style.removeProperty(`--color${index}`)) {
                index++;
            }
        }
    }
}
