import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UISVGIconComponent } from './svg-icon.component';
import { icons } from './nui.icons';
import { materialIcons } from './nui-material.icons';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/SvgIcons',
    component: UISVGIconComponent,
    decorators: [
        moduleMetadata({
            imports: [UISVGIconComponent, UIComponentSizeDirective]
        })
    ],
    parameters: {
        controls: {
            include: ['size', 'nuiIcon', 'nuiIconFilled']
        }
    },
    args: {
        nuiIcon: 'label',
        size: 'md',
        icon: 'none',
        nuiIconFilled: false
    },
    argTypes: {
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        nuiIcon: { control: 'select', options: [...icons, ...materialIcons] },
        nuiIconFilled: { control: 'boolean' }
    }
};

export default meta;

type Story = StoryObj<UISVGIconComponent>;

export const Playground: Story = {};
export const AllIconsNui: Story = {
    ...NUI_STORY_SETTINGS,
    parameters: {
        controls: { disable: true },
        // Disables Chromatic's snapshotting on a component level
        chromatic: { disableSnapshot: true }
    },
    render: () => ({
        template: `
            <h3 [uiSize]="'sm'">Custom icons</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 30px; padding: 20px;">
                @for(icon of icons; track $index){
                    <div style="text-align: center; width: 120px;">
                        <ui-svg-icon [icon]="'none'" [size]="size" [nuiIconFilled]="nuiIconFilled" [nuiIcon]="icon" style="display: block; margin: 0 auto;"/>
                        <div style="margin-top: 8px; font-size: 12px;">{{ icon }}</div>
                    </div>
                }
            </div>
            <h3 [uiSize]="'sm'">Material symbols</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 30px; padding: 20px;">
                @for(icon of materialIcons; track $index){
                    <div style="text-align: center; width: 120px;">
                        <ui-svg-icon [icon]="'none'" [size]="size" [nuiIconFilled]="nuiIconFilled" [nuiIcon]="icon" style="display: block; margin: 0 auto;"/>
                        <div style="margin-top: 8px; font-size: 12px;">{{ icon }}</div>
                    </div>
                }
            </div>
        `,
        props: {
            icons,
            materialIcons
        }
    })
};
