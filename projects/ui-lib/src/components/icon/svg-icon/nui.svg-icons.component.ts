import { Component, ChangeDetectionStrategy } from '@angular/core';

@Component({
    imports: [],
    selector: 'ui-new-svg-icons',
    templateUrl: 'nui.svg-icons.component.html',
    styleUrls: ['./svg-icons.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})

/**
 * Only used to inject the svg into body when the UISvgIconService initializes (once)
 */
export class NUISvgIconsComponent {}
