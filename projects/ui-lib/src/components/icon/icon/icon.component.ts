import { LowerCasePipe } from '@angular/common';
import { Component, ChangeDetectionStrategy, Input } from '@angular/core';

@Component({
    imports: [LowerCasePipe],
    selector: 'ui-icon',
    templateUrl: './icon.component.html',
    styleUrls: ['../../../assets/icons/bannerflow-material.css', './icon.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UIIconComponent {
    @Input() public icon: string | undefined;
}
