import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent } from './icon.component';

const meta: Meta<UIIconComponent> = {
    title: 'Components/Icons/UIIcon',
    component: UIIconComponent,
    decorators: [
        moduleMetadata({
            imports: [UIIconComponent]
        })
    ]
};
export default meta;
type Story = StoryObj<UIIconComponent>;

export const Default: Story = {
    args: {
        icon: 'loop'
    }
};
