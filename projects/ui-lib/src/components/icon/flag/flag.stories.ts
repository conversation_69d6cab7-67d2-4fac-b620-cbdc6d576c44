import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIFlagComponent } from './flag.component';

const meta: Meta<UIFlagComponent> = {
    title: 'Components/Icons/UIFlag',
    component: UIFlagComponent,
    decorators: [
        moduleMetadata({
            imports: [UIFlagComponent]
        })
    ]
};
export default meta;
type Story = StoryObj<UIFlagComponent>;

export const Flag: Story = {
    args: {
        culture: 'se'
    }
};
export const Small: Story = {
    args: {
        culture: 'se',
        size: 'small'
    }
};
export const Tiny: Story = {
    args: {
        culture: 'se',
        size: 'tiny'
    }
};
