import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIFlagComponent } from './flag.component';
import { NUI_FLAGS } from './flag.nui.types';

const meta: Meta<UIFlagComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Icons/UIFlag',
    component: UIFlagComponent,
    decorators: [moduleMetadata({ imports: [UIFlagComponent] })],
    parameters: {
        controls: {
            include: ['nuiSize', 'nuiFlag']
        }
    },
    argTypes: {
        nuiSize: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        nuiFlag: { control: 'select', options: [...NUI_FLAGS] }
    },
    args: {
        nuiFlag: 'se',
        nuiSize: 'md'
    }
};
export default meta;
type Story = StoryObj<UIFlagComponent>;

export const NUIFlag: Story = {};
