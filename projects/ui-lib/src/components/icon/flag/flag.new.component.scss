:where(:root[data-uinew]) :host {
    --nui-flag-padding: 2.4px;
    --nui-flag-inner-size: calc(var(--nui-icon-width) - (var(--nui-flag-padding) * 2));

    display: flex;
    width: var(--nui-icon-width);
    height: var(--nui-icon-height);
    padding: var(--nui-flag-padding);

    &::before {
        content: '';
        width: var(--nui-flag-inner-size);
        height: var(--nui-flag-inner-size);
        background-image: inherit;
        background-size: cover;
        background-position: 50% 50%;
    }
}
