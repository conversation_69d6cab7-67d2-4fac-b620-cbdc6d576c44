import { Component, Input, OnChanges, inject, input } from '@angular/core';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UINUIFlag } from './flag.nui.types';

/**
 * Flag files are hosted by Azure Blob Storage
 *
 * They can be found under the 'bannerflow' account, the container is called
 * 'shared'. And the folder they are under is 'ui/flags' or 'ui/uinew-flags',
 * depending if nui is enabled or not
 */
@Component({
    imports: [],
    selector: 'ui-flag',
    template: '',
    styleUrls: ['./flag.component.scss', './flag.new.component.scss'],
    host: {
        '[class.ui-flag]': 'true',
        '[class.small]': `size === 'small'`,
        '[class.tiny]': `size === 'tiny'`,
        '[style.backgroundImage]': 'flagUrl',
        [`[attr.data-${DATASET_SIZE}]`]: 'nuiSize()'
    }
})
export class UIFlagComponent implements OnChanges {
    private uiNewThemeService = inject(UINewThemeService);
    /**
     * Culture receives ISO 3166-1 alpha-2 code [Two-letters country codes]
     * Culture code can also be in the format "en-us" and it will be treated as "us"
     *
     * @summary:
     * Icon of the flag.
     */
    @Input() culture: string | undefined;

    /**
     * Size can be either 'tiny' or 'small' or empty
     *
     * @summary:
     * Size of the icon.
     */

    @Input() size?: 'small' | 'tiny';

    /**
     * NUI flag icon.
     * Ignored if NUI is disabled
     */
    nuiFlag = input<UINUIFlag>();

    /**
     * NUI Size
     */
    nuiSize = input<'xs' | 'sm' | 'md'>('md');

    public icon: string;
    private isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    get flagUrl(): string {
        let path = 'shared/ui/flags/1x1/';
        let icon = this.icon;

        if (this.isNewUI()) {
            path = 'shared/ui/uinew-flags/';
            icon = this.nuiFlag() ?? this.icon;
        }
        return `url(https://cdn.bannerflow.com/${path}${icon}.svg)`;
    }

    ngOnChanges(): void {
        this.setIcon();
    }

    // Support input of different formats (use the last numbers) ex. "en-us" => "us"
    private setIcon(): void {
        const cultureStr = this.culture ? this.culture : '';
        const match: RegExpMatchArray | null = cultureStr.match(/[a-zA-Z]{2,2}$/g);

        if (match) {
            this.icon = match[0];
        } else {
            this.icon = cultureStr;
        }
    }
}
