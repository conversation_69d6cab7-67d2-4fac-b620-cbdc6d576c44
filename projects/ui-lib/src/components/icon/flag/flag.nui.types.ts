export const NUI_FLAGS = [
    'ac',
    'ad',
    'ae',
    'af-emirate',
    'af',
    'ag',
    'ai',
    'al',
    'am',
    'an',
    'ao',
    'aq-true_south',
    'aq',
    'ar',
    'as',
    'at',
    'au-aboriginal',
    'au-act',
    'au-nsw',
    'au-nt',
    'au-qld',
    'au-sa',
    'au-tas',
    'au-torres_strait_islands',
    'au-vic',
    'au-wa',
    'au',
    'aw',
    'ax',
    'az',
    'ba',
    'bb',
    'bd',
    'be',
    'bf',
    'bg',
    'bh',
    'bi',
    'bj',
    'bl',
    'bm',
    'bn',
    'bo',
    'bq-bo',
    'bq-sa',
    'bq-se',
    'bq',
    'br',
    'bs',
    'bt',
    'bv',
    'bw',
    'by',
    'bz',
    'ca-bc',
    'ca-qc',
    'ca',
    'cc',
    'cd',
    'cf',
    'cg',
    'ch-gr',
    'ch',
    'ci',
    'ck',
    'cl',
    'cm',
    'cn-xj',
    'cn',
    'co',
    'cp',
    'cq',
    'cr',
    'cu',
    'cv',
    'cw',
    'cx',
    'cy',
    'cz',
    'de',
    'dg',
    'dj',
    'dk',
    'dm',
    'do',
    'dz',
    'ea',
    'east_african_federation',
    'easter_island',
    'ec-w',
    'ec',
    'ee',
    'eg',
    'eh',
    'er',
    'es-ar',
    'es-ce',
    'es-cn',
    'es-ct',
    'es-ga',
    'es-ib',
    'es-ml',
    'es-pv',
    'es-variant',
    'es',
    'et-af',
    'et-am',
    'et-be',
    'et-ga',
    'et-ha',
    'et-or',
    'et-si',
    'et-sn',
    'et-so',
    'et-sw',
    'et-ti',
    'et',
    'eu',
    'european_union',
    'ewe',
    'fi',
    'fj',
    'fk',
    'fm',
    'fo',
    'fr-20r',
    'fr-bre',
    'fr-cp',
    'fr',
    'fx',
    'ga',
    'gb-con',
    'gb-eng',
    'gb-nir',
    'gb-ork',
    'gb-sct',
    'gb-wls',
    'gb',
    'gd',
    'ge-ab',
    'ge',
    'gf',
    'gg',
    'gh',
    'gi',
    'gl',
    'gm',
    'gn',
    'gp',
    'gq',
    'gr',
    'gs',
    'gt',
    'gu',
    'guarani',
    'gw',
    'gy',
    'hausa',
    'hk',
    'hm',
    'hmong',
    'hn',
    'hr',
    'ht',
    'hu',
    'ic',
    'id-jb',
    'id-jt',
    'id',
    'ie',
    'il',
    'im',
    'in-as',
    'in-gj',
    'in-ka',
    'in-mn',
    'in-mz',
    'in-or',
    'in-tg',
    'in-tn',
    'in',
    'io',
    'iq-kr',
    'iq',
    'ir',
    'is',
    'it-21',
    'it-23',
    'it-25',
    'it-32',
    'it-34',
    'it-36',
    'it-42',
    'it-45',
    'it-52',
    'it-55',
    'it-57',
    'it-62',
    'it-65',
    'it-67',
    'it-72',
    'it-75',
    'it-77',
    'it-78',
    'it-82',
    'it-88',
    'it',
    'je',
    'jm',
    'jo',
    'jp',
    'kanuri',
    'ke',
    'kg',
    'kh',
    'ki',
    'kikuyu',
    'km',
    'kn',
    'kongo',
    'kp',
    'kr',
    'kw',
    'ky',
    'kz',
    'la',
    'lb',
    'lc',
    'li',
    'lk',
    'lr',
    'ls',
    'lt',
    'lu',
    'lv',
    'ly',
    'ma',
    'malayali',
    'maori',
    'mc',
    'md',
    'me',
    'mf',
    'mg',
    'mh',
    'mk',
    'ml',
    'mm',
    'mn',
    'mo',
    'mp',
    'mq-old',
    'mq',
    'mr',
    'ms',
    'mt',
    'mu',
    'mv',
    'mw',
    'mx',
    'my',
    'mz',
    'na',
    'nc',
    'ne',
    'nf',
    'ng',
    'ni',
    'nl-fr',
    'nl',
    'no',
    'northern_cyprus',
    'np',
    'nr',
    'nu',
    'nz',
    'occitania',
    'om',
    'otomi',
    'pa',
    'pe',
    'pf',
    'pg',
    'ph',
    'pk-jk',
    'pk-sd',
    'pk',
    'pl',
    'pm',
    'pn',
    'pr',
    'ps',
    'pt-20',
    'pt-30',
    'pt',
    'pw',
    'py',
    'qa',
    'quechua',
    're',
    'ro',
    'rs',
    'ru-ba',
    'ru-ce',
    'ru-cu',
    'ru-da',
    'ru-dpr',
    'ru-ko',
    'ru-lpr',
    'ru-ta',
    'ru-ud',
    'ru',
    'rw',
    'sa',
    'sami',
    'sb',
    'sc',
    'sd',
    'se',
    'sg',
    'sh-ac',
    'sh-hl',
    'sh-ta',
    'sh',
    'si',
    'sj',
    'sk',
    'sl',
    'sm',
    'sn',
    'so',
    'somaliland',
    'south_ossetia',
    'soviet_union',
    'sr',
    'ss',
    'st',
    'su',
    'sv',
    'sx',
    'sy',
    'sz',
    'ta',
    'tc',
    'td',
    'tf',
    'tg',
    'th',
    'tibet',
    'tj',
    'tk',
    'tl',
    'tm',
    'tn',
    'to',
    'tr',
    'transnistria',
    'tt',
    'tv',
    'tw',
    'tz',
    'ua',
    'ug',
    'uk',
    'um',
    'un',
    'us-ak',
    'us-al',
    'us-ar',
    'us-az',
    'us-betsy_ross',
    'us-ca',
    'us-co',
    'us-confederate_battle',
    'us-dc',
    'us-fl',
    'us-ga',
    'us-hi',
    'us-in',
    'us-md',
    'us-mo',
    'us-ms',
    'us-nc',
    'us-nm',
    'us-or',
    'us-ri',
    'us-sc',
    'us-tn',
    'us-tx',
    'us-wa',
    'us-wi',
    'us-wy',
    'us',
    'uy',
    'uz',
    'va',
    'vc',
    've',
    'vg',
    'vi',
    'vn',
    'vu',
    'wf',
    'wiphala',
    'ws',
    'xk',
    'xx',
    'ye',
    'yorubaland',
    'yt',
    'yu',
    'za',
    'zm',
    'zw'
] as const;

export type NUIFlags = typeof NUI_FLAGS;

export type UINUIFlag = NUIFlags[number];

export function isNuiFlag(name: string | undefined): name is UINUIFlag {
    return !!name && NUI_FLAGS.includes(name as UINUIFlag);
}
