:host {
    font-size: inherit;
    line-height: inherit;
    display: inline-block;

    i {
        font-size: inherit;
        line-height: inherit;
    }
}
.image {
    font-size: var(--font-size);
    width: 2rem;
    height: 2rem;
    border-radius: 100%;
    display: inline-block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-color: #ccc;
    vertical-align: -0.6rem;
}
.bordered-circle {
    background-color: #fff;
    border: 1px solid #d6d6d6;
    height: 24px;
    border-radius: 50%;
    width: 24px;
    display: flex;
    .image {
        background-color: #fff;
        width: 16px;
        height: 16px;
        margin-left: 0.3rem;
        margin-top: 0.3rem;
        margin-right: 0rem;
    }
}
