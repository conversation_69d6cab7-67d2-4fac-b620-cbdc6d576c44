import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { UIImageComponent } from './image.component';

const meta: Meta<UIImageComponent> = {
    title: 'Components/Icons/UIImage',
    component: UIImageComponent,
    decorators: [
        moduleMetadata({
            imports: [UIImageComponent]
        })
    ]
};
export default meta;
type Story = StoryObj<UIImageComponent>;

export const Normal: Story = {
    args: {
        bordered: false,
        image: 'https://bannerflowsandbox.blob.core.windows.net:443/bf-content/logo/brand/bannerflow-enterprise/5a6f35c2952dd74cecfe35d8/5a02f7a98866581878e53695.png'
    }
};

export const Bordered: Story = {
    args: {
        bordered: true,
        image: 'https://bannerflowsandbox.blob.core.windows.net:443/bf-content/logo/brand/bannerflow-enterprise/5a6f35c2952dd74cecfe35d8/5a02f7a98866581878e53695.png'
    }
};
