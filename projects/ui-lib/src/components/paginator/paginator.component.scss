:where(:root:not([data-uinew])) :host {
    .paginator-wrapper {
        display: flex;
        color: var(--ui-color-text);

        .show-page {
            display: flex;
            align-items: center;
            flex: 0.5;

            .select {
                margin: 0 5px;

                --font-weight: 700;
                --min-width: 6rem;
                --arrow-color: var(--ui-color-text-second);
                --button-height: 2.6rem;
            }
        }

        .page-selection {
            display: flex;
            justify-content: center;
            align-items: center;

            .page-counter {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .page-input {
                --input-font-weight: 700;

                margin: 0 5px 0 15px;
            }

            span {
                margin: 2px 15px 0 0;
            }
        }
    }
}

