import { BrowserAnimationsModule, NoopAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { expect, screen, userEvent, within } from '@storybook/test';
import { UIButtonComponent } from '../buttons';
import { UISVGIconComponent } from '../icon';
import { UINumberInputComponent, UISelectableListComponent } from '../inputs';
import {
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective
} from '../popovers';
import { UIOptionComponent, UISelectComponent } from '../select';
import { UIPaginatorComponent } from './paginator.component';

const meta: Meta<UIPaginatorComponent> = {
    title: 'Components/Paginator/Paginator',
    component: UIPaginatorComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIPaginatorComponent,
                UISVGIconComponent,
                UISelectComponent,
                UIOptionComponent,
                UIPopoverComponent,
                UISelectableListComponent,
                UIButtonComponent,
                UIPopoverTargetDirective,
                UIPopoverTemplateDirective,
                UIPopoverDirective,
                UINumberInputComponent,
                BrowserAnimationsModule,
                NoopAnimationsModule
            ]
        })
    ]
};
export default meta;
type Story = StoryObj<UIPaginatorComponent>;

export const Paginate: Story = {
    args: {
        totalItems: 200,
        pageSizeOptions: [10, 50, 100],
        activePageSizeOption: 10
    },
    play: async ({ canvasElement }) => {
        const canvas = within(canvasElement);

        const nextPage = await canvas.findByTestId('next-page');
        const uiInput = await canvas.findByTestId('input-page');
        const inputPage = uiInput.querySelector('.input') as HTMLInputElement;

        await userEvent.clear(inputPage);
        await userEvent.type(inputPage, '1');
        await userEvent.click(nextPage);

        expect(inputPage).toHaveValue('2');

        const prevPage = await canvas.findByTestId('prev-page');
        await userEvent.clear(inputPage);
        await userEvent.type(inputPage, '2');
        await userEvent.click(prevPage);

        expect(inputPage).toHaveValue('1');

        const selectDropDown = await canvas.findByTestId('select-toggle');
        const dropDownButton = await within(selectDropDown).findByText('10');
        await userEvent.click(dropDownButton);

        const option = await screen.findByText('100');
        expect(option).toBeInTheDocument();
        await userEvent.click(option);

        const lastPage = await canvas.findByTestId('last-page');
        const lastPageValue = '/ 2';

        expect(lastPage).toHaveTextContent(lastPageValue);
    }
};
