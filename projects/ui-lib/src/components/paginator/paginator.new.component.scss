:where(:root[data-uinew]) :host {
    --paginator-width: 100%;

    .paginator-wrapper {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        align-items: center;
        width: var(--paginator-width);
        padding: var(--nui-paginator-space-padding-vertical) var(--nui-paginator-space-padding-horizontal);
        color: var(--nui-paginator-text-primary);
        font-family: var(--nui-font-family-primary);
        font-size: var(--nui-font-size-275);
        font-weight: var(--nui-font-font-weight-10000);
        line-height: var(--nui-font-line-height-400);
        letter-spacing: var(--nui-font-letter-spacing-020);

        .show-page {
            display: flex;
            align-items: center;
            gap: var(--nui-paginator-space-gap-show, 12px);

            .select {
                --width: 6.6rem;
            }
        }

        .page-selection {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--nui-paginator-space-gap-page, 12px);

            .page-counter {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: var(--nui-space-200);
            }

            .page-input {
                --input-text-align: center;
            }
        }
    }
}
