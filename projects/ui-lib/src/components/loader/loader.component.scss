:where(:root:not([data-uinew])) :host {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 101;
    background-color: var(--background-color);
    border-radius: var(--border-radius);

    &.ui-inline-loader {
        position: unset;
        left: unset;
        top: unset;
        width: unset;
        height: unset;
        display: unset;
        justify-content: unset;
        align-items: unset;
        z-index: unset;
        background-color: unset;
        border-radius: unset;
    }

    .loading-circle {
        display: inline-block;
        border-radius: 100px;
        width: 4px;
        height: 4px;
        margin: 2px;
        opacity: 0.4;
        background-color: var(--circle-color);

        &.first {
            animation: updown 1s infinite;
        }

        &.middle {
            animation: updown 1s infinite;
            animation-delay: 0.2s;
        }

        &.last {
            animation: updown 1s infinite;
            animation-delay: 0.4s;
        }
    }

    @keyframes updown {
        0% {
            background-color: var(--circle-color);
            transform: translateY(0);
            opacity: 0.4;
        }

        16.5% {
            transform: translateY(-3px);
            opacity: 1;
        }

        33% {
            transform: translateY(0);
            opacity: 0.4;
        }
    }
}