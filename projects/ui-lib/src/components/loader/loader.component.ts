import { animate, style, transition, trigger } from '@angular/animations';
import { Component, Input, input } from '@angular/core';

export type UILoaderType = 'primary' | 'inverted';

@Component({
    imports: [],
    selector: 'ui-loader',
    templateUrl: 'loader.component.html',
    styleUrls: ['loader.component.scss', 'loader.new.component.scss'],
    host: {
        '[class]': 'type()',
        '[class.ui-loader]': 'true',
        '[class.ui-inline-loader]': 'inline',
        '[@fadeIn]': 'true',
        '[@fadeOut]': 'true'
    },
    animations: [
        trigger('fadeIn', [
            transition(':enter', [style({ opacity: 0 }), animate('.3s', style({ opacity: 1 }))])
        ]),
        trigger('fadeOut', [
            transition(':leave', [style({ opacity: 1 }), animate('.3s', style({ opacity: 0 }))])
        ])
    ]
})
export class UILoaderComponent {
    @Input() animationsDisabled = false;
    @Input() inline = false;

    /**
     * Loader type for NUI. Controls color scheme.
     * - 'primary': Uses brand colors (blue+grey)
     * - 'inverted': Uses neutral colors (white+grey) for colored backgrounds
     */
    type = input<UILoaderType>('primary');
}
