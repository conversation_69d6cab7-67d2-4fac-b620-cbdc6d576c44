import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UILoaderComponent } from './loader.component';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIComponentSizeDirective } from '../../directives';

const sharedStyles = `
  .demo-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start;
  }
  .demo-container hr,
  .demo-container h6 {
    flex-basis: 100%;
  }
  .demo-card {
    position: relative;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 24px;
    min-width: 200px;
    min-height: 120px;
    background: #ffffff;
    overflow: hidden;
  }
  .demo-card h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
  .demo-card p {
    margin: 0;
    font-size: 13px;
    color: #666;
  }
  .inline-demo {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #ffffff;
  }
  .colored-bg {
    background: #2563eb;
    color: white;
  }
  .colored-bg p {
    color: #e0e7ff;
  }
`;

const meta: Meta<UILoaderComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Loader',
    component: UILoaderComponent,
    parameters: {
        controls: { disable: true }
    },
    decorators: [
        moduleMetadata({
            imports: [
                UILoaderComponent,
                CommonModule,
                FormsModule,
                BrowserAnimationsModule,
                UIComponentSizeDirective
            ]
        })
    ],
    argTypes: {
        inline: {
            control: 'boolean',
            description: 'Whether the loader is inline or acts as an overlay'
        },
        animationsDisabled: {
            control: 'boolean',
            description: 'Disable the bouncing animation'
        },
        type: {
            control: 'select',
            options: ['primary', 'inverted'],
            description: 'Loader type - primary for light backgrounds, inverted for colored backgrounds'
        }
    }
};

export default meta;
type Story = StoryObj<UILoaderComponent>;

export const Playground: Story = {
    parameters: {
        controls: { disable: false }
    },
    args: {
        inline: false,
        animationsDisabled: false,
        type: 'primary'
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                @if (inline) {
                    <div class="inline-demo" [class.colored-bg]="type === 'inverted'">
                        <ui-loader [inline]="inline" [animationsDisabled]="animationsDisabled" [type]="type" />
                        <span>Playground - Inline Mode</span>
                    </div>
                } @else {
                    <div class="demo-card" [class.colored-bg]="type === 'inverted'">
                        <h4 [uiSize]="'xs'">Playground - Overlay Mode</h4>
                        <p>Adjust the controls to see different loader configurations.</p>
                        <ui-loader [inline]="inline" [animationsDisabled]="animationsDisabled" [type]="type" />
                    </div>
                }
            </div>
        `
    })
};

export const OverlayLoaders: Story = {
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <h6 [uiSize]="'md'">Default</h6>
                <div class="demo-card">
                    <h6 [uiSize]="'md'">Default Overlay Loader</h6>
                    <p>Loading content...</p>
                    <ui-loader />
                </div>

                <div class="demo-card">
                    <h4 [uiSize]="'xs'">Card with Content</h4>
                    <p>This card shows how the loader overlays existing content when loading data or performing actions.</p>
                    <ui-loader />
                </div>

                <hr />
                <h6 [uiSize]="'md'">Animation Disabled</h6>
                <div class="demo-card">
                    <h6 [uiSize]="'xs'">Static Overlay</h6>
                    <p>No bouncing animation, useful for accessibility</p>
                    <ui-loader [animationsDisabled]="true" />
                </div>
            </div>
        `
    })
};

export const InlineLoaders: Story = {
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <h6 [uiSize]="'md'">Default</h6>
                <div class="inline-demo">
                    <ui-loader [inline]="true" />
                    <span>Loading...</span>
                </div>

                <div class="inline-demo">
                    <span>Saving changes</span>
                    <ui-loader [inline]="true" />
                </div>

                <div class="inline-demo">
                    <ui-loader [inline]="true" />
                    <span>Processing request</span>
                    <ui-loader [inline]="true" />
                </div>

                <hr />
                <h6 [uiSize]="'md'">Animation Disabled</h6>
                <div class="inline-demo">
                    <ui-loader [inline]="true" [animationsDisabled]="true" />
                    <span>Static inline loader</span>
                </div>
            </div>
        `
    })
};

export const LoaderTypes: Story = {
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <h6 [uiSize]="'md'">Primary Type</h6>
                <div class="demo-card">
                    <h4 [uiSize]="'xs'">Primary Overlay</h4>
                    <p>For light backgrounds and standard usage</p>
                    <ui-loader />
                </div>

                <div class="inline-demo">
                    <ui-loader [inline]="true" />
                    <span>Primary inline loader</span>
                </div>

                <hr />
                <h6 [uiSize]="'md'">Inverted Type</h6>

                <div class="inline-demo colored-bg">
                    <ui-loader [inline]="true" type="inverted" />
                    <span>Inverted inline loader</span>
                </div>
            </div>
        `
    })
};

export const FullScreenLoader: Story = {
    render: args => ({
        props: args,
        template: `
            <ui-loader />
        `
    })
};
