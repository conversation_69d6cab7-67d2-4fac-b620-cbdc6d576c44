import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UILoaderComponent } from './loader.component';

const meta: Meta<UILoaderComponent> = {
    title: 'Components/Loader/loader',
    component: UILoaderComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UILoaderComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                BrowserAnimationsModule
            ]
        })
    ]
};

export default meta;
type Story = StoryObj<UILoaderComponent>;

export const Loader: Story = {
    args: {
        animationsDisabled: false,
        inline: false
    }
};
