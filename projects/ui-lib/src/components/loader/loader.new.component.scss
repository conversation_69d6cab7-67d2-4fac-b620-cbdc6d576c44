:where(:root[data-uinew]) :host {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 101;
    background-color: var(--background-color, var(--nui-surface-neutral-subtler));
    border-radius: var(--border-radius);

    &.ui-inline-loader {
        position: unset;
        left: unset;
        top: unset;
        width: unset;
        height: unset;
        display: unset;
        justify-content: unset;
        align-items: unset;
        z-index: unset;
        background-color: unset;
        border-radius: unset;
    }

    .loading-circle {
        display: inline-block;
        border-radius: var(--nui-border-radius-full, 100px);
        width: 4px;
        height: 4px;
        margin: 2px;
        background-color: var(--loader-inactive-color, var(--nui-loader-fill-primary-light));

        &.first {
            animation: bounce-dots 1s infinite;
        }

        &.middle {
            animation: bounce-dots 1s infinite;
            animation-delay: 0.2s;
        }

        &.last {
            animation: bounce-dots 1s infinite;
            animation-delay: 0.4s;
        }
    }

    &.primary {
        --loader-inactive-color: var(--nui-loader-fill-primary-light);
        --loader-active-color: var(--nui-loader-fill-primary-strong);
    }

    &.inverted {
        --loader-inactive-color: var(--nui-loader-fill-inverted-light);
        --loader-active-color: var(--nui-loader-fill-inverted-strong);
    }

    @keyframes bounce-dots {
        0% {
            background-color: var(--loader-active-color, var(--nui-loader-fill-primary-strong));
            transform: translateY(0);
        }

        16.5% {
            transform: translateY(-3px);
        }

        33% {
            transform: translateY(0);
        }
    }
}
