@if (isNewUI()) {
    <button
        [class]="computedType()"
        [class.selected]="computedSelected()"
        [class.icon-button]="hasIcon && !text"
        [class.two-icons-button]="hasTwoIcons && !text"
        [class.loading]="loading"
        [class.disabled]="disabled"
        [disabled]="disabled"
        (click)="onClick($event)">
        @if (loading) {
            <ui-loader
                [inline]="true"
                [type]="loaderType()" />
        }

        <ui-label
            [weight]="'bold'"
            [size]="size()"
            [leadingIcon]="nuiSvgIcon()"
            [trailingIcon]="nuiTrailingIcon()">
            @if (text) {
                {{ text }}
            } @else {
                <ng-container *ngTemplateOutlet="contentTpl" />
            }
        </ui-label>
    </button>
} @else {
    <div
        class="container {{ computedType() }}"
        (click)="onClick($event)">
        @if (loading) {
            <ui-loader />
        }
        <div class="content">
            @if (icon) {
                <ui-icon
                    [icon]="icon"
                    class="icon icon__leading" />
            }
            @if (svgIcon) {
                <ui-svg-icon
                    [icon]="svgIcon"
                    class="icon icon__leading"
                    [size]="size()" />
            }
            @if (text) {
                <span>
                    {{ text }}
                </span>
            } @else {
                <ng-container *ngTemplateOutlet="contentTpl" />
            }

            @if (trailingIcon) {
                <ui-svg-icon
                    [icon]="trailingIcon"
                    class="icon icon__trailing"
                    [size]="size()" />
            }
        </div>
    </div>
}

<ng-template #contentTpl><ng-content /></ng-template>
