import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UIButtonComponent } from './button.component';

const meta: Meta<UIButtonComponent> = {
    title: 'Components/Buttons/Button',
    component: UIButtonComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, FormsModule, UIButtonComponent, UIIconComponent, UISVGIconComponent]
        })
    ],
    argTypes: {
        onClick: { action: 'clicked' }
    }
};

export default meta;
type Story = StoryObj<UIButtonComponent>;

export const Primary: Story = {
    args: {
        text: 'Primary',
        type: 'primary'
    }
};

export const Default: Story = {
    args: {
        text: 'Default'
    }
};

export const Discrete: Story = {
    args: {
        text: 'Discrete',
        type: 'discrete'
    }
};

export const SolidPrimaryWithNestedContent: Story = {
    args: {
        type: 'solid-primary'
    },
    render: args => ({
        props: args,
        template: `<ui-button [type]="type" ui-theme="small">Test me 🦭</ui-button>`
    })
};

export const SolidPrimaryWithUIThemeSmall: Story = {
    args: {
        text: 'Solid Primary',
        type: 'solid-primary'
    },
    render: args => ({
        props: args,
        template: `<ui-button [text]="text" [type]="type" ui-theme="small"></ui-button>`
    })
};

export const SolidPrimaryWithSize: Story = {
    args: {
        text: 'Solid Primary',
        type: 'solid-primary'
    },
    render: args => ({
        props: args,
        template: `<ui-button [text]="text" [type]="type" size="sm"></ui-button>`
    })
};

export const SolidSecondary: Story = {
    args: {
        text: 'Button text',
        type: 'solid-secondary'
    }
};

export const SolidPrimaryDestructive: Story = {
    args: {
        text: 'Button text',
        type: 'solid-primary-destructive'
    }
};

export const SolidSecondaryDestructive: Story = {
    args: {
        text: 'Button text',
        type: 'solid-secondary-destructive'
    }
};

export const SolidDisabled: Story = {
    args: {
        text: 'Button text',
        type: 'solid-primary',
        disabled: true
    }
};

export const Disabled: Story = {
    args: {
        text: 'Button text',
        type: 'primary',
        disabled: true
    }
};

export const NewIconButtonPrimary: Story = {
    args: {
        svgIcon: 'list1',
        type: 'primary'
    }
};

export const IconButton: Story = {
    args: {
        svgIcon: 'list1'
    }
};

export const TextIconButton: Story = {
    args: {
        text: 'text with 😔',
        svgIcon: 'direction-left'
    }
};

export const TwoIcons: Story = {
    args: {
        text: 'Both sides icons 🤯',
        svgIcon: 'arrow-left',
        trailingIcon: 'arrow-right'
    }
};
