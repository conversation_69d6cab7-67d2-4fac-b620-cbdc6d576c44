$button-icon-spacing: 0.6rem;

:where(:root:not([data-uinew])) :host {
    position: relative;
    cursor: pointer;
    text-transform: uppercase;
    display: inline-block;
    text-align: center;
    user-select: none;
    white-space: nowrap;
    background-color: var(--background-color);
    color: var(--text-color);
    font-size: var(--font-size);
    font-weight: var(--font-weight);
    border-radius: 2px;
    vertical-align: middle;

    &.disabled {
        pointer-events: none;
    }

    &.loading {
        cursor: default;
        pointer-events: none;

        .content {
            opacity: 0;
        }
    }

    &.capitalize {
        text-transform: capitalize;
    }

    &.borderless {
        .container {
            border: none !important;
        }
    }

    &.icon-button {
        .container {
            padding: 0;
            width: var(--height);
        }
    }

    &.text-button {
        .container {
            min-width: var(--min-width);
        }
    }

    &.text-icon-button {
        .icon {
            &__leading {
                margin-right: $button-icon-spacing;
            }

            &__trailing {
                margin-left: $button-icon-spacing;
            }
        }

        .text {
            font-weight: var(--font-weight);
            margin-left: 0;
        }
    }

    .icon {
        font-size: var(--icon-font-size);
    }

    .container {
        position: relative;
        height: var(--height);
        border-radius: 2px;
        padding: var(--padding);
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;

        &:active {
            background-color: var(--active-background-color);
            color: var(--active-text-color);
            transform: scale(0.98);
        }

        &.default,
        &.discrete {
            border: 1px solid var(--border-color);

            &:hover {
                border: 1px solid var(--border-color-hover);
            }
        }
    }

    .content {
        transition: opacity 0.3s;
        display: flex;
        min-width: var(--min-width);
        justify-content: center;
        align-items: center;
        pointer-events: none;
    }
}
