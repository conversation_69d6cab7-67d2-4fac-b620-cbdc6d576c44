import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, HostListener, inject, input, Input, signal } from '@angular/core';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UINUIIcon } from '../../../types';
import { NewButtonType, UIButtonType, UISubmitResponse } from '../../../types/button';
import { Icon, UIIconComponent, UISVGIconComponent } from '../../icon';
import { UILabelComponent } from '../../label';
import { UILoaderComponent, UILoaderType } from '../../loader';

const MAX_WAIT_TIME = 500;

/**
 * Buttons
 *
 * Style params:
 * * `--width`: sets the width for the button. Defaults to auto
 */
@Component({
    imports: [
        UILabelComponent,
        UIIconComponent,
        UILoaderComponent,
        UISVGIconComponent,
        NgTemplateOutlet
    ],
    selector: 'ui-button',
    templateUrl: './button.component.html',
    styleUrls: ['./button.component.scss', './button.new.component.scss'],
    host: {
        '[class]': 'computedType()',
        '[class.ui-button]': 'true',
        '[class.icon-button]': 'hasIcon && !text',
        '[class.two-icons-button]': 'hasTwoIcons && !text',
        '[class.text-icon-button]': 'text && hasIcon',
        '[class.text-button]': 'text',
        '[class.disabled]': 'disabled',
        '[class.loading]': 'loading',
        '[class.borderless]': '!border',
        '[class.capitalize]': 'capitalize',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIButtonComponent {
    private uiNewThemeService = inject(UINewThemeService);

    /**
     * Text of the button.
     */
    @Input() text?: string;

    /**
     * @summary:
     * Icon of the button.
     */
    @Input() icon?: string;

    /**
     * @summary:
     * Svg icon of the button.
     */
    @Input() svgIcon?: Icon | 'none';

    nuiSvgIcon = input<UINUIIcon>();

    /**
     * @summary:
     * Trailing icon of the button.
     */
    @Input() trailingIcon?: Icon;

    nuiTrailingIcon = input<UINUIIcon>();

    /**
     * @summary:
     * Icon of the button.
     * @description:
     * The 'done' handler is fired directly efter the submission, regardless of whether the
     * submission fails or succeeds. The passed error or state can be used to render any additional
     * message to the user.
     */
    @Input() submit?: <T>() => Promise<UISubmitResponse<T> | any>;

    /**
     * @summary:
     * Done handler.
     * @description:
     * The 'done' handler is fired directly efter the submission, regardless of whether the
     * submission fails or succeeds. The passed error or state can be used to render any additional
     * message to the user.
     */
    @Input() done?: (error?: any, state?: any) => any;

    /**
     * Type of the button.
     */
    @Input() type?: UIButtonType = 'default';

    /**
     * NUI type of the button.
     */
    nuiType = input<NewButtonType>();

    /**
     * NUI selected state of the button. Only used for plain buttons.
     */
    selected = input<boolean>(false);

    /**
     * Disable button, set to true to prevent clicks.
     */
    @Input() disabled?: boolean;

    /**
     * Show loader
     */
    @Input() loading = false;

    /**
     * Show border, set false to hide border styles
     */
    @Input() border = true;

    /**
     * Capitalize the text of the button.
     */
    @Input() capitalize = false;

    /**
     * Size of the button.
     */
    size = input<'xs' | 'sm' | 'md'>('md');

    computedType = computed(() => this.computeButtonType());
    loaderType = computed(() => this.computeLoaderType());
    computedSelected = computed<boolean | undefined>(() => {
        const type = this.nuiType();
        const selected = this.selected();
        return (
            selected &&
            (type === 'plain-primary' ||
                type === 'plain-secondary' ||
                type === 'plain-primary-destructive')
        );
    });

    isHovered = signal(false);
    isPressed = signal(false);

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    @HostListener('mouseenter')
    handleMouseEnter(): void {
        this.isHovered.set(true);
    }

    @HostListener('mouseleave')
    handleMouseLeave(): void {
        this.isHovered.set(false);
    }

    @HostListener('mousedown')
    handleMouseDown(): void {
        this.isPressed.set(true);
    }

    @HostListener('mouseup')
    handleMouseUp(): void {
        this.isPressed.set(false);
    }

    get hasIcon(): boolean {
        return !!this.icon || !!this.svgIcon || !!this.nuiSvgIcon();
    }

    get hasTwoIcons(): boolean {
        return this.hasIcon && !!this.nuiTrailingIcon();
    }

    async onClick(event?: MouseEvent): Promise<void> {
        if (this.loading) {
            // Check if there's an event in case onClick is called programatically
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            return;
        }

        if (this.submit) {
            this.loading = true;
            const start = Date.now();
            let response: UISubmitResponse<any> = {};
            try {
                const submitResponse = (await this.submit()) || {};
                if (
                    typeof submitResponse.state !== 'undefined' ||
                    typeof submitResponse.error !== 'undefined'
                ) {
                    response = submitResponse;
                }
            } catch (e) {
                response.error = e;
            }
            const elapsed = Date.now() - start;
            const remaining = MAX_WAIT_TIME - elapsed;
            setTimeout(
                () => {
                    this.loading = false;
                    if (this.done) {
                        this.done(response.error, response.state);
                    }
                },
                remaining > 0 ? remaining : 0
            );
        }
    }

    private computeButtonType(): UIButtonType | undefined {
        const isNewUI = this.uiNewThemeService.isNewThemeEnabled();
        if (!isNewUI) {
            return this.type;
        }
        const nuiType = this.nuiType();
        if (nuiType) {
            return nuiType;
        }
        switch (this.type) {
            case 'primary':
                return 'solid-primary';
            case 'default':
                return 'ghost-primary';
            case 'discrete':
                return 'ghost-secondary';
        }
        return this.type;
    }

    private computeLoaderType(): UILoaderType {
        if (this.disabled) {
            return 'primary';
        }

        const buttonType = this.computedType();

        switch (buttonType) {
            case 'solid-primary':
            case 'solid-primary-destructive':
                return 'inverted';
            default:
                return 'primary';
        }
    }
}
