:where(:root[data-uinew]) :host {
    position: relative;
    display: flex;
    text-align: center;

    --icon-button-width: 40px;
    --two-icons-button-width: 80px;
    --width: auto;

    &[data-uisize='sm'] {
        --icon-button-width: 32px;
        --two-icons-button-width: 60px;
    }

    &[data-uisize='xs'] {
        --icon-button-width: 24px;
        --two-icons-button-width: 44px;
    }

    button {
        display: inline-flex;
        height: var(--nui-button-height, 40px);
        padding: 0 var(--nui-button-space-padding-horizontal, 16px);
        justify-content: center;
        align-items: center;
        gap: var(--nui-button-space-gap, 8px);
        flex-shrink: 0;
        outline: none;
        border-radius: var(--nui-button-radius, 8px);
        border: var(--nui-border-width-small) solid transparent;
        cursor: pointer;
        color: var(--color);
        width: var(--width);

        &.icon-button {
            width: var(--icon-button-width);
        }

        &.two-icons-button {
            width: var(--two-icons-button-width);
        }

        ui-loader {
            display: none;
        }

        &.loading {
            ui-loader {
                position: absolute;
                display: block;
            }

            ui-label {
                opacity: 0;
            }
        }

        // ===== SOLID =====
        &.solid-primary {
            --color: var(--nui-text-primary-inverted);

            background-color: var(--nui-button-fill-primary);

            &:hover {
                background-color: var(--nui-button-fill-primary-hover);
            }

            &:active,
            &.loading {
                background-color: var(--nui-button-fill-primary-pressed);
            }
        }

        &.solid-secondary {
            --color: var(--nui-button-text-primary-inverted);

            background-color: var(--nui-button-fill-secondary);

            &:hover {
                background-color: var(--nui-button-fill-secondary-hover);
            }

            &:active {
                background-color: var(--nui-button-fill-secondary-pressed);
            }
        }

        &.solid-primary-destructive {
            --color: var(--nui-button-text-primary-destructive);

            background-color: var(--nui-button-fill-primary-destructive, #d63d54);
            border-color: var(--nui-button-border-secondary-destructive);

            &:hover {
                --color: var(--nui-label-text-destructive);

                background-color: var(--nui-button-fill-primary-destructive-hover);
            }

            &:active {
                --color: var(--nui-button-text-primary-destructive);

                background-color: var(--nui-button-fill-primary-destructive, #d63d54);
            }
        }

        &.solid-secondary-destructive {
            --color: var(--nui-label-text-destructive);

            background-color: var(--nui-button-fill-secondary-destructive);
            border-color: var(--nui-button-border-secondary-destructive);

            &:hover {
                --color: var(--nui-button-text-primary-destructive);

                background-color: var(--nui-button-fill-primary-destructive, #d63d54);
            }

            &:active {
                --color: var(--nui-button-text-primary-destructive);

                background-color: var(--nui-button-fill-primary-destructive, #d63d54);
            }
        }

        &.solid-primary.disabled,
        &.solid-secondary.disabled,
        &.solid-primary-destructive.disabled,
        &.solid-secondary-destructive.disabled {
            --color: var(--nui-label-text-disabled);

            background-color: var(--nui-button-fill-primary-disabled);
            border-color: transparent;
            pointer-events: none;
        }

        // ===== Ghost =====
        &.ghost-primary {
            --color: var(--nui-button-text-brand);

            background-color: var(--nui-button-fill-transparent);

            &:hover {
                --color: var(--nui-text-primary-inverted);

                background-color: var(--nui-button-fill-primary-hover);
            }

            &:active {
                background-color: var(--nui-button-fill-primary-pressed);
            }
        }

        &.ghost-secondary {
            --color: var(--nui-button-text-primary-inverted);

            background-color: var(--nui-button-fill-transparent);

            &:hover {
                background-color: var(--nui-button-fill-secondary-hover);
            }

            &:active {
                background-color: var(--nui-button-fill-secondary-pressed);
            }
        }

        &.ghost-primary-destructive {
            --color: var(--nui-button-text-primary-destructive-inverted);

            background-color: var(--nui-text-primary-inverted);

            &:hover {
                --color: var(--nui-text-primary-inverted);

                background-color: var(--nui-button-fill-primary-destructive, #d63d54);
            }

            &:active {
                --color: var(--nui-button-text-primary-destructive-inverted);

                background-color: var(--nui-button-fill-primary-destructive-hover);
                border-color: var(--nui-button-border-secondary-destructive);
            }
        }

        &.ghost-primary.disabled,
        &.ghost-secondary.disabled,
        &.ghost-primary-destructive.disabled {
            --color: var(--nui-text-disabled);

            background-color: var(--nui-button-fill-transparent);
            border-color: transparent;
            pointer-events: none;
        }

        // ===== PLAIN =====

        &.plain-primary,
        &.plain-secondary,
        &.plain-primary-destructive {
            height: var(--nui-icon-height);
            padding: 0;

            &.icon-button {
                width: auto;
            }
        }

        &.plain-primary {
            background-color: var(--nui-button-fill-transparent);

            --color: var(--nui-text-primary);

            &:hover {
                --color: var(--nui-text-brand);
            }

            &:active {
                --color: var(--nui-text-secondary);
            }

            &.selected {
                --color: var(--nui-button-icon-brand);
            }
        }

        &.plain-secondary {
            --color: var(--nui-button-text-secondary, #62636c);

            background-color: var(--nui-button-fill-transparent);

            &:hover {
                --color: var(--nui-button-text-primary-inverted, #1e1f24);
            }

            &:active {
                --color: var(--nui-button-text-primary-inverted, #1e1f24);
            }

            &.selected {
                --color: var(--nui-button-icon-primary-inverted);
            }
        }

        &.plain-primary-destructive {
            --color: var(--nui-button-text-primary-inverted);

            border-color: transparent;
            background-color: var(--nui-button-fill-transparent);

            &:hover {
                --color: var(--nui-button-text-primary-destructive-inverted);

                background-color: var(--nui-button-fill-transparent);
            }

            &:active {
                --color: var(--nui-button-text-primary-destructive-inverted);

                background-color: var(--nui-button-fill-transparent);
            }

            &.selected {
                --color: var(--nui-button-icon-primary-destructive-inverted);
            }
        }

        &.plain-primary.disabled,
        &.plain-secondary.disabled,
        &.plain-primary-destructive.disabled {
            --color: var(--nui-button-text-disabled);

            background-color: var(--nui-button-fill-transparent);
            border-color: transparent;
            pointer-events: none;
        }

        &:focus-visible {
            outline: var(--nui-border-width-medium) solid var(--nui-border-system-focus);
            box-shadow: inset 0 0 0 1px var(--nui-icon-primary-inverted);
        }

        &.disabled {
            pointer-events: none;
            border-color: transparent;
        }

        &.loading {
            cursor: default;
            pointer-events: none;
        }
    }
}
