import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { materialIcons } from '../../icon/svg-icon/nui-material.icons';
import { icons } from '../../icon/svg-icon/nui.icons';
import { UIButtonComponent } from './button.component';
import { UIComponentSizeDirective } from '../../../directives';

const exampleIcons = [undefined, ...icons, ...materialIcons];

const sharedStyles = `
  .demo-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }
  .demo-container hr,
  .demo-container h2 {
    flex-basis: 100%
  }
`;

const meta: Meta<UIButtonComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Buttons/Button',
    component: UIButtonComponent,
    decorators: [
        moduleMetadata({
            imports: [
                CommonModule,
                FormsModule,
                UIButtonComponent,
                UIIconComponent,
                UISVGIconComponent,
                UIComponentSizeDirective
            ]
        })
    ],
    parameters: {
        controls: {
            include: [
                'text',
                'nuiType',
                'size',
                'disabled',
                'loading',
                'selected',
                'nuiSvgIcon',
                'nuiTrailingIcon'
            ]
        }
    },
    argTypes: {
        nuiType: {
            control: 'select',
            options: [
                'solid-primary',
                'solid-secondary',
                'solid-primary-destructive',
                'solid-secondary-destructive',
                'ghost-primary',
                'ghost-secondary',
                'ghost-primary-destructive',
                'plain-primary',
                'plain-secondary',
                'plain-primary-destructive'
            ]
        },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        disabled: { control: 'boolean' },
        loading: { control: 'boolean' },
        nuiSvgIcon: {
            control: 'select',
            options: exampleIcons
        },
        nuiTrailingIcon: {
            control: 'select',
            options: exampleIcons
        }
    }
};

export default meta;
type Story = StoryObj<UIButtonComponent>;

export const Playground: Story = {
    args: {
        text: 'Button Text',
        nuiType: 'solid-primary',
        size: 'md',
        disabled: false,
        loading: false,
        selected: false,
        nuiSvgIcon: undefined,
        nuiTrailingIcon: undefined
    }
};

export const SolidButtons: Story = {
    parameters: {
        controls: { disable: true }
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <h2 [uiSize]="'md'">Default</h2>
                <ui-button nuiType="solid-primary" text="Primary" />
                <ui-button nuiType="solid-primary" nuiSvgIcon="search" />
                <ui-button nuiType="solid-secondary" text="Secondary" />
                <ui-button nuiType="solid-secondary" nuiSvgIcon="search" />
                <ui-button nuiType="solid-primary-destructive" text="Primary Destructive" />
                <ui-button nuiType="solid-primary-destructive" nuiSvgIcon="search" />
                <ui-button nuiType="solid-secondary-destructive" text="Secondary Destructive" />
                <ui-button nuiType="solid-secondary-destructive" nuiSvgIcon="search" />

                <hr />
                <h2 [uiSize]="'md'">Disabled</h2>
                <ui-button nuiType="solid-primary" text="Primary" [disabled]="true" />
                <ui-button nuiType="solid-primary" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="solid-secondary" text="Secondary" [disabled]="true" />
                <ui-button nuiType="solid-secondary" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="solid-primary-destructive" text="Primary Destructive" [disabled]="true" />
                <ui-button nuiType="solid-primary-destructive" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="solid-secondary-destructive" text="Secondary Destructive" [disabled]="true" />
                <ui-button nuiType="solid-secondary-destructive" nuiSvgIcon="search" [disabled]="true" />

                <hr />
                <h2 [uiSize]="'md'">Loading</h2>
                <ui-button nuiType="solid-primary" text="Primary" [loading]="true" />
                <ui-button nuiType="solid-primary" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="solid-secondary" text="Secondary" [loading]="true" />
                <ui-button nuiType="solid-secondary" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="solid-primary-destructive" text="Primary Destructive" [loading]="true" />
                <ui-button nuiType="solid-primary-destructive" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="solid-secondary-destructive" text="Secondary Destructive" [loading]="true" />
                <ui-button nuiType="solid-secondary-destructive" nuiSvgIcon="search" [loading]="true" />

                <hr />
                <h2 [uiSize]="'md'">Loading + Disabled</h2>
                <ui-button nuiType="solid-primary" text="Primary" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-primary" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-secondary" text="Secondary" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-secondary" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-primary-destructive" text="Primary Destructive" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-primary-destructive" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-secondary-destructive" text="Secondary Destructive" [loading]="true" [disabled]="true" />
                <ui-button nuiType="solid-secondary-destructive" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
            </div>
        `
    })
};

export const GhostButtons: Story = {
    parameters: {
        controls: { disable: true }
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <h2 [uiSize]="'md'">Default</h2>
                <ui-button nuiType="ghost-primary" text="Primary" />
                <ui-button nuiType="ghost-primary" nuiSvgIcon="search" />
                <ui-button nuiType="ghost-secondary" text="Secondary" />
                <ui-button nuiType="ghost-secondary" nuiSvgIcon="search" />
                <ui-button nuiType="ghost-primary-destructive" text="Primary Destructive" />
                <ui-button nuiType="ghost-primary-destructive" nuiSvgIcon="search" />

                <hr />
                <h2 [uiSize]="'md'">Disabled</h2>
                <ui-button nuiType="ghost-primary" text="Primary" [disabled]="true" />
                <ui-button nuiType="ghost-primary" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="ghost-secondary" text="Secondary" [disabled]="true" />
                <ui-button nuiType="ghost-secondary" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="ghost-primary-destructive" text="Primary Destructive" [disabled]="true" />
                <ui-button nuiType="ghost-primary-destructive" nuiSvgIcon="search" [disabled]="true" />

                <hr />
                <h2 [uiSize]="'md'">Loading</h2>
                <ui-button nuiType="ghost-primary" text="Primary" [loading]="true" />
                <ui-button nuiType="ghost-primary" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="ghost-secondary" text="Secondary" [loading]="true" />
                <ui-button nuiType="ghost-secondary" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="ghost-primary-destructive" text="Primary Destructive" [loading]="true" />
                <ui-button nuiType="ghost-primary-destructive" nuiSvgIcon="search" [loading]="true" />

                <hr />
                <h2 [uiSize]="'md'">Loading + Disabled</h2>
                <ui-button nuiType="ghost-primary" text="Primary" [loading]="true" [disabled]="true" />
                <ui-button nuiType="ghost-primary" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="ghost-secondary" text="Secondary" [loading]="true" [disabled]="true" />
                <ui-button nuiType="ghost-secondary" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="ghost-primary-destructive" text="Primary Destructive" [loading]="true" [disabled]="true" />
                <ui-button nuiType="ghost-primary-destructive" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
            </div>
        `
    })
};

export const PlainButtons: Story = {
    parameters: {
        controls: { disable: true }
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <h2 [uiSize]="'md'">Default</h2>
                <ui-button nuiType="plain-primary" text="Primary" />
                <ui-button nuiType="plain-primary" nuiSvgIcon="search" />
                <ui-button nuiType="plain-secondary" text="Secondary" />
                <ui-button nuiType="plain-secondary" nuiSvgIcon="search" />
                <ui-button nuiType="plain-primary-destructive" text="Primary Destructive" />
                <ui-button nuiType="plain-primary-destructive" nuiSvgIcon="search" />

                <hr />
                <h2 [uiSize]="'md'">Disabled</h2>
                <ui-button nuiType="plain-primary" text="Primary" [disabled]="true" />
                <ui-button nuiType="plain-primary" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="plain-secondary" text="Secondary" [disabled]="true" />
                <ui-button nuiType="plain-secondary" nuiSvgIcon="search" [disabled]="true" />
                <ui-button nuiType="plain-primary-destructive" text="Primary Destructive" [disabled]="true" />
                <ui-button nuiType="plain-primary-destructive" nuiSvgIcon="search" [disabled]="true" />

                <hr />
                <h2 [uiSize]="'md'">Selected</h2>
                <ui-button nuiType="plain-primary" text="Primary" [selected]="true" />
                <ui-button nuiType="plain-primary" nuiSvgIcon="search" [selected]="true" />
                <ui-button nuiType="plain-secondary" text="Secondary" [selected]="true" />
                <ui-button nuiType="plain-secondary" nuiSvgIcon="search" [selected]="true" />
                <ui-button nuiType="plain-primary-destructive" text="Primary Destructive" [selected]="true" />
                <ui-button nuiType="plain-primary-destructive" nuiSvgIcon="search" [selected]="true" />

                <hr />
                <h2 [uiSize]="'md'">Loading</h2>
                <ui-button nuiType="plain-primary" text="Primary" [loading]="true" />
                <ui-button nuiType="plain-primary" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="plain-secondary" text="Secondary" [loading]="true" />
                <ui-button nuiType="plain-secondary" nuiSvgIcon="search" [loading]="true" />
                <ui-button nuiType="plain-primary-destructive" text="Primary Destructive" [loading]="true" />
                <ui-button nuiType="plain-primary-destructive" nuiSvgIcon="search" [loading]="true" />

                <hr />
                <h2 [uiSize]="'md'">Loading + Disabled</h2>
                <ui-button nuiType="plain-primary" text="Primary" [loading]="true" [disabled]="true" />
                <ui-button nuiType="plain-primary" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="plain-secondary" text="Secondary" [loading]="true" [disabled]="true" />
                <ui-button nuiType="plain-secondary" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
                <ui-button nuiType="plain-primary-destructive" text="Primary Destructive" [loading]="true" [disabled]="true" />
                <ui-button nuiType="plain-primary-destructive" nuiSvgIcon="search" [loading]="true" [disabled]="true" />
            </div>
        `
    })
};

export const MediumIconButtons: Story = {
    parameters: {
        controls: { disable: true }
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <ui-button size="md" nuiType="solid-primary" nuiSvgIcon="search" />
                <ui-button size="md" nuiType="solid-primary" nuiSvgIcon="status-in-progress-colored" nuiTrailingIcon="keyboard_arrow_down" />
                <ui-button size="md" nuiType="solid-secondary" nuiSvgIcon="search" />
                <ui-button size="md" nuiType="solid-secondary" nuiSvgIcon="status-in-progress-colored" nuiTrailingIcon="keyboard_arrow_down" />
                <ui-button size="md" nuiType="solid-primary" nuiSvgIcon="rocket_launch" text="2" />
                <ui-button size="md" nuiType="solid-secondary" nuiSvgIcon="chat_bubble" text="12" />
            </div>
        `
    })
};

export const SmallIconButtons: Story = {
    parameters: {
        controls: { disable: true }
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <ui-button size="sm" nuiType="solid-primary" nuiSvgIcon="search" />
                <ui-button size="sm" nuiType="solid-primary" nuiSvgIcon="status-in-progress-colored" nuiTrailingIcon="keyboard_arrow_down" />
                <ui-button size="sm" nuiType="solid-secondary" nuiSvgIcon="search" />
                <ui-button size="sm" nuiType="solid-secondary" nuiSvgIcon="status-in-progress-colored" nuiTrailingIcon="keyboard_arrow_down" />
                <ui-button size="sm" nuiType="solid-primary" nuiSvgIcon="rocket_launch" text="2" />
                <ui-button size="sm" nuiType="solid-secondary" nuiSvgIcon="chat_bubble" text="12" />
            </div>
        `
    })
};

export const ExtraSmallIconButtons: Story = {
    parameters: {
        controls: { disable: true }
    },
    render: args => ({
        props: args,
        styles: [sharedStyles],
        template: `
            <div class="demo-container">
                <ui-button size="xs" nuiType="solid-primary" nuiSvgIcon="search" />
                <ui-button size="xs" nuiType="solid-primary" nuiSvgIcon="status-in-progress-colored" nuiTrailingIcon="keyboard_arrow_down" />
                <ui-button size="xs" nuiType="solid-secondary" nuiSvgIcon="search" />
                <ui-button size="xs" nuiType="solid-secondary" nuiSvgIcon="status-in-progress-colored" nuiTrailingIcon="keyboard_arrow_down" />
                <ui-button size="xs" nuiType="solid-primary" nuiSvgIcon="rocket_launch" text="2" />
                <ui-button size="xs" nuiType="solid-secondary" nuiSvgIcon="chat_bubble" text="12" />
            </div>
        `
    })
};
