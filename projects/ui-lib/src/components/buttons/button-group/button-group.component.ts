import { Component, EventEmitter, input, Input, linkedSignal, Output } from '@angular/core';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UINUIIcon } from '../../../types';
import { Icon } from '../../icon/svg-icon/icons';
import { UIButtonGroupItemComponent } from './button-group-item.component';

export interface UIButtonGroupOption {
    id: string;
    icon?: string;
    svgIcon?: Icon;
    nuiSvgIcon?: UINUIIcon;
    text?: string;
    disabled?: boolean;
    value: string;
}

@Component({
    imports: [UIButtonGroupItemComponent],
    selector: 'ui-button-group',
    templateUrl: './button-group.component.html',
    styleUrls: ['./button-group.component.scss', './button-group.new.component.scss'],
    host: {
        '[class.input]': 'true',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIButtonGroupComponent {
    /**
     * Options array, needs to be the type UIButtonToggleOption
     */
    @Input('options') options: UIButtonGroupOption[];

    /**
     * The default selected value, that matches the value in the options. If null, nothing will be selected.
     */
    @Input('value') value: string;

    /**
     * Use primary selection style of the button.
     */
    @Input() primarySelectionStyle?: boolean;

    /**
     * The size of the button group.
     */
    size = input<'xs' | 'sm' | 'md'>('md');

    /**
     * If multiple selection is allowed.
     */
    multiple = input<boolean>(false);

    /**
     * ids of the selected options.
     */
    values = input<string[]>([]);
    selectedValues = linkedSignal<string[]>(() => this.values());

    /**
     * Value of whatever is selected.
     */
    @Output('valueChange') valueChange = new EventEmitter();

    updateValue(value: string): void {
        if (this.multiple()) {
            this.selectedValues.update(values => {
                if (values.includes(value)) {
                    values = values.filter(v => v !== value);
                } else {
                    values = [...values, value];
                }
                this.valueChange.emit(values);
                return values;
            });
        } else {
            this.value = value;
            this.valueChange.emit(value);
        }
    }
}
