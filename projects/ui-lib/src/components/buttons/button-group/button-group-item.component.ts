import { Component, EventEmitter, inject, input, Input, Output } from '@angular/core';
import { Icon, UIIconComponent, UISVGIconComponent } from '../../icon/';
import { UINUIIcon } from '../../../types';
import { UINewThemeService } from '../../../services';
import { UILabelComponent } from '../../label';

@Component({
    imports: [UIIconComponent, UISVGIconComponent, UILabelComponent],
    selector: 'ui-button-group-item',
    templateUrl: './button-group-item.component.html',
    styleUrls: ['./button-group-item.component.scss', './button-group-item.new.component.scss'],
    host: {
        '[class.has-text]': '!!text'
    }
})
export class UIButtonGroupItemComponent {
    private uiNewThemeService = inject(UINewThemeService);

    @Input() icon: string | undefined;
    @Input() svgIcon: Icon | undefined;
    @Input() text: string | undefined;
    @Input() active = false;
    size = input<'xs' | 'sm' | 'md'>('md');
    nuiSvgIcon = input<UINUIIcon | undefined>();

    @Output() activeChange = new EventEmitter();

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;
}
