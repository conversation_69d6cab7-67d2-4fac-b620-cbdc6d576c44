import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UIButtonGroupItemComponent } from './button-group-item.component';
import { UIButtonGroupComponent } from './button-group.component';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UIButtonGroupComponent> = {
    title: 'Components/Buttons/ButtonGroup',
    component: UIButtonGroupComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIButtonGroupComponent,
                UIIconComponent,
                UISVGIconComponent,
                UIButtonGroupItemComponent,
                CommonModule,
                FormsModule,
                BrowserModule
            ]
        })
    ]
};
export default meta;

type Story = StoryObj<UIButtonGroupComponent>;

export const TwoOptions: Story = {
    args: {
        options: [
            {
                id: 'left-button',
                svgIcon: 'direction-left',
                text: 'With text',
                value: 'icon-with-text'
            },
            { id: 'center-button', icon: 'analytics', text: 'With text', value: 'analytics-icon' }
        ]
    }
};

export const TwoOptionsNui: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        value: 'square',
        options: [
            {
                id: 'left-button',
                nuiSvgIcon: 'square',
                value: 'square',
                text: 'Square'
            },
            {
                id: 'right-button',
                nuiSvgIcon: 'crop_free',
                value: 'crop_free'
            },
            {
                id: 'right-button',
                nuiSvgIcon: 'crop_landscape',
                value: 'crop_landscape',
                disabled: true
            }
        ]
    }
};

export const OptionsMultipleNui: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        values: ['strikethrough', 'uppercase'],
        multiple: true,
        options: [
            {
                id: 'strikethrough',
                nuiSvgIcon: 'format_strikethrough',
                value: 'strikethrough'
            },
            {
                id: 'underline',
                nuiSvgIcon: 'format_underlined',
                value: 'underline'
            },
            {
                id: 'uppercase',
                nuiSvgIcon: 'uppercase',
                value: 'uppercase'
            }
        ]
    }
};

export const AllOptionsDisabled: Story = {
    args: {
        options: [
            {
                id: 'left-button',
                svgIcon: 'direction-left',
                text: 'With text-disabled',
                value: 'icon-with-text',
                disabled: true
            },
            {
                id: 'center-button',
                icon: 'analytics',
                text: 'With text-disabled',
                value: 'analytics-icon',
                disabled: true
            }
        ]
    }
};

export const OneOptionDisabled: Story = {
    args: {
        options: [
            { id: 'center-button', icon: 'analytics', text: 'With text', value: 'analytics-selected' },
            {
                id: 'left-button',
                svgIcon: 'direction-left',
                text: 'With text',
                value: 'icon-with-text'
            },
            {
                id: 'center-button',
                icon: 'analytics',
                text: 'With text-disabled',
                value: 'analytics-icon',
                disabled: true
            }
        ]
    }
};

export const PrimarySelectionStyle: Story = {
    args: {
        options: [
            { id: 'center-button', icon: 'analytics', text: 'With text', value: 'analytics-selected' },
            {
                id: 'left-button',
                svgIcon: 'direction-left',
                text: 'With text',
                value: 'icon-with-text'
            },
            {
                id: 'center-button',
                icon: 'analytics',
                text: 'With text-disabled',
                value: 'analytics-icon',
                disabled: true
            }
        ],
        primarySelectionStyle: true
    }
};
