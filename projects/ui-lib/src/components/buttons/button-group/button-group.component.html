@for (option of options; track $index) {
    <ui-button-group-item
        [id]="option.id"
        [icon]="option.icon"
        [svgIcon]="option.svgIcon"
        [nuiSvgIcon]="option.nuiSvgIcon"
        [size]="size()"
        [text]="option.text"
        (click)="updateValue(option.value)"
        [class.primary-selection]="primarySelectionStyle"
        [class.disabled]="option.disabled"
        [class.active]="
            multiple() ? selectedValues().includes(option.value) : option.value === value
        " />
}
