:where(:root:not([data-uinew])) :host {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--ui-font-weight-bold);
    border-right: 1px solid var(--ui-color-border);
    text-transform: uppercase;
    background-color: var(--ui-color-surface-second);
    color: var(--ui-color-text-second);
    cursor: pointer;
    width: 100%;

    &:last-child {
        border-right: none;
    }

    &.active {
        color: var(--ui-color-primary);
        background-color: var(--ui-color-surface);

        &.disabled {
            background-color: var(--ui-color-disabled);
        }
    }

    &.disabled {
        background-color: var(--ui-color-disabled-second);
        color: var(--ui-color-text-disabled);
        pointer-events: none;
        opacity: 0.5;
    }

    .icon {
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .text {
        text-align: center;
    }

    .space {
        margin-left: 5px;
    }
}
