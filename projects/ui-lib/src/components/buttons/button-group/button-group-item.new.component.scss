:where(:root[data-uinew]) :host {
    display: inline-flex;
    height: var(--nui-button-height);
    width: fit-content;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    cursor: pointer;
    border-radius: var(--nui-button-group-item-radius, 2px);
    background: var(--nui-fill-transparent);
    padding: 0 var(--nui-button-group-item-space-padding-horizontal-icon);
    border: var(--nui-button-group-item-border) solid var(--nui-fill-transparent);
    flex: 1;

    --color: var(--nui-icon-secondary);

    &.has-text {
        padding: 0 var(--nui-button-group-item-space-padding-horizontal-text);
    }

    &:last-child {
        border-right: none;
    }

    &.active {
        background: var(--nui-fill-neutral-subtlest);
        border: var(--nui-button-group-item-border) solid var(--nui-border-neutral-secondary-bold);

        --color: var(--nui-icon-primary);

        &.disabled {
            background: var(--nui-fill-transparent);

            --color: var(--nui-icon-disabled);
        }
    }

    &.disabled {
        pointer-events: none;
        background: var(--nui-fill-transparent);

        --color: var(--nui-icon-disabled);
    }
}
