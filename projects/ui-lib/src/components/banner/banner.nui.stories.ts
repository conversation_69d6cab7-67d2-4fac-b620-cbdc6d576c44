import { Meta, StoryObj, argsToTemplate, moduleMetadata } from '@storybook/angular';
import { UISVGIconComponent } from '../icon/svg-icon/svg-icon.component';
import { UIBannerComponent } from './banner.component';

type UIBannerPropsAndCustomArgs = UIBannerComponent & { content?: string };

export default {
    title: 'NUI/Banner',
    component: UIBannerComponent,
    tags: ['Nui'],
    globals: {
        rootAttributes: {
            'data-uinew': 'true'
        }
    },
    decorators: [
        moduleMetadata({
            imports: [UIBannerComponent, UISVGIconComponent]
        })
    ],
    render: ({ content, ...args }) => ({
        props: args,
        template: `<ui-banner ${argsToTemplate(args)}>${content}</ui-banner>`
    }),
    parameters: {
        controls: {
            include: ['type', 'content', 'showCloseIcon']
        }
    },
    args: {
        showCloseIcon: false
    },
    argTypes: {
        type: {
            control: 'select',
            options: ['info', 'success', 'warning', 'error', 'neutral'],
            description: 'The type of the banner, affects color and icon.'
        },
        content: {
            control: 'text',
            description: 'The content displayed inside the banner (projected via ng-content).'
        }
    }
} as Meta<UIBannerPropsAndCustomArgs>;

type Story = StoryObj<UIBannerPropsAndCustomArgs>;

export const InfoWithCloseIcon: Story = {
    args: {
        type: 'info',
        showCloseIcon: true,
        content: 'This is an informational message.'
    }
};

export const Info: Story = {
    args: {
        type: 'info',
        content: 'This is an informational message.'
    }
};

export const Success: Story = {
    args: {
        type: 'success',
        content: 'Operation completed successfully!'
    }
};

export const Warning: Story = {
    args: {
        type: 'warning',
        content: 'Warning: Please check the details.'
    }
};

export const Error: Story = {
    args: {
        type: 'error',
        content: 'An error has occurred. Please try again.'
    }
};

export const Neutral: Story = {
    args: {
        type: 'neutral',
        content: 'This is a neutral message.'
    }
};
