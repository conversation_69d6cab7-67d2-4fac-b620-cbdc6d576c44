:host {
    display: block;
    border-radius: var(--nui-banner-radius);
    padding: var(--nui-banner-space-padding-vertical) var(--nui-banner-space-padding-horizontal);
    background-color: var(--current-background);
    color: var(--current-text-color);

    .banner-content {
        display: flex;
        align-items: center;
        gap: var(--nui-banner-space-gap);
    }

    .banner-icon {
        color: var(--current-icon-color);
    }

    .banner-text {
        color: var(--current-text-color);
    }

    .close-button {
        margin-left: auto;
    }

    &.info {
        --current-background: var(--nui-banner-fill-info);
        --current-text-color: var(--nui-banner-text-info);
        --current-icon-color: var(--nui-banner-icon-info);
    }

    &.success {
        --current-background: var(--nui-banner-fill-success);
        --current-text-color: var(--nui-banner-text-success);
        --current-icon-color: var(--nui-banner-icon-success);
    }

    &.warning {
        --current-background: var(--nui-banner-fill-warning);
        --current-text-color: var(--nui-banner-text-warning);
        --current-icon-color: var(--nui-banner-icon-warning);
    }

    &.error {
        --current-background: var(--nui-banner-fill-error);
        --current-text-color: var(--nui-banner-text-error);
        --current-icon-color: var(--nui-banner-icon-error);
    }

    &.neutral {
        --current-background: var(--nui-banner-fill-neutral);
        --current-text-color: var(--nui-banner-text-neutral);
        --current-icon-color: var(--nui-banner-icon-neutral);
    }
}
