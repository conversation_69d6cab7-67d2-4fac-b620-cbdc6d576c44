import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { DATASET_SIZE } from '../../services';
import { UIButtonComponent } from '../buttons/button/button.component';
import { UISVGIconComponent } from '../icon/svg-icon/svg-icon.component';
import { UILabelComponent } from '../label/label.component';

export type UIBannerType = 'info' | 'success' | 'warning' | 'error' | 'neutral';

@Component({
    selector: 'ui-banner',
    imports: [UISVGIconComponent, UILabelComponent, UIButtonComponent],
    templateUrl: './banner.component.html',
    styleUrls: ['./banner.new.component.scss'],
    host: {
        '[class]': 'type()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    },
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UIBannerComponent {
    type = input<UIBannerType>('info');
    size = input<'xs' | 'sm' | 'md'>('md');
    showCloseIcon = input<boolean>(false);

    closeClicked = output<void>();

    iconName = computed(() => {
        switch (this.type()) {
            case 'info':
                return 'info';
            case 'success':
                return 'check_circle';
            case 'warning':
                return 'warning';
            case 'error':
                return 'error';
            case 'neutral':
                return 'info';
            default:
                return 'info';
        }
    });
}
