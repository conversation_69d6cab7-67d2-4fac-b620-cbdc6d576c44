import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import {
    UIDropdownComponent,
    UIDropdownItemComponent,
    UIDropdownTargetDirective,
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverService,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective,
    UITooltipComponent,
    UITooltipDirective,
    UITooltipService
} from '../../popovers';
import { UIHelpMenuComponent } from './help-menu.component';

const meta: Meta<UIHelpMenuComponent> = {
    title: 'Components/Static/help-menu',
    component: UIHelpMenuComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIHelpMenuComponent,
                UIIconComponent,
                UISVGIconComponent,
                UIPopoverDirective,
                UIDropdownTargetDirective,
                UIPopoverComponent,
                UIPopoverTemplateDirective,
                UIPopoverTargetDirective,
                UIPopoverTemplateDirective,
                UIDropdownComponent,
                UIDropdownItemComponent,
                UITooltipDirective,
                UITooltipComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                BrowserAnimationsModule,
                OverlayModule
            ],
            providers: [UIPopoverService, UITooltipService]
        })
    ]
};
export default meta;
type Story = StoryObj<UIHelpMenuComponent>;
export const HelpMenu: Story = {
    render: args => ({
        props: {
            ...args,
            onIntercom(): void {
                alert(
                    'If Intercom widget is available in your app, you should initialize a new message by Intercom("showNewMessage")'
                );
            },
            onIntercomNews(): void {
                alert(
                    'If Intercom widget is available in your app, you should initialize it with news space by Intercom("showSpace", "news");'
                );
            },
            onKeyboardShortcuts(): void {
                alert(
                    'To display the keyboard shortcuts option, ensure that the `showKeyboardShortcuts` output is implemented in your component.'
                );
            }
        },
        template: `
        <ui-help-menu
            (showIntercom)="onIntercom()"
            (showIntercomNews)="onIntercomNews()"
            (showKeyboardShortcuts)="onKeyboardShortcuts()"
        />
        `
    })
};
