:where(:root:not([data-uinew])) :host {
    display: block;
    height: 100%;

    .help-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        cursor: pointer;
        user-select: none;

        &:hover {
            .help-icon {
                transform: scale(1.1);
                color: var(--ui-color-primary);
                border: 1px solid var(--ui-color-primary);
            }
        }
    }

    .help-icon {
        $size: 2.2rem;

        width: $size;
        height: $size;
        font-size: $size;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--ui-color-text-second);
        border: 1px solid var(--ui-color-text-second);
        border-radius: 50%;
        transition: all 0.1s linear;
    }
}
