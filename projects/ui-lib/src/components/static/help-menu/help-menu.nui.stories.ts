import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { userEvent, within } from '@storybook/test';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIHelpMenuComponent } from './help-menu.component';

const meta: Meta<UIHelpMenuComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/HelpMenu',
    component: UIHelpMenuComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIHelpMenuComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                BrowserAnimationsModule,
                OverlayModule
            ]
        })
    ],
    parameters: {
        controls: {
            include: ['size', 'isIntercomAvailable']
        }
    },
    argTypes: {
        size: {
            type: 'string',
            control: 'inline-radio',
            options: ['xs', 'sm', 'md']
        },
        isIntercomAvailable: { type: 'boolean' }
    },
    args: {
        size: 'md',
        isIntercomAvailable: true
    }
};
export default meta;
type Story = StoryObj<UIHelpMenuComponent>;
export const HelpMenu: Story = {
    render: args => ({
        props: {
            ...args,
            onIntercom(): void {
                alert(
                    'If Intercom widget is available in your app, you should initialize a new message by Intercom("showNewMessage")'
                );
            },
            onIntercomNews(): void {
                alert(
                    'If Intercom widget is available in your app, you should initialize it with news space by Intercom("showSpace", "news");'
                );
            },
            onKeyboardShortcuts(): void {
                alert(
                    'To display the keyboard shortcuts option, ensure that the `showKeyboardShortcuts` output is implemented in your component.'
                );
            }
        },
        template: `
        <ui-help-menu
            [isIntercomAvailable]="isIntercomAvailable"
            [size]="size"
            (showIntercom)="onIntercom()"
            (showIntercomNews)="onIntercomNews()"
            (showKeyboardShortcuts)="onKeyboardShortcuts()"
        />
        `
    }),
    play: async ({ canvasElement }) => {
        // Force NUI before play function finishes
        canvasElement.ownerDocument.documentElement.dataset['uinew'] = 'true';
        await new Promise<void>(res => {
            const intervalId = setInterval(() => {
                if (canvasElement.ownerDocument.documentElement.dataset['uinew']) {
                    res();
                    clearInterval(intervalId);
                }
            }, 100);
        });

        const canvas = within(canvasElement);
        const button = await canvas.findByTestId('help-button');
        await userEvent.click(button);
    }
};
