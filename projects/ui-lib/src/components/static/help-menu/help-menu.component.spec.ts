import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { UINewThemeService } from '../../../services/uinew-theme.service';
import { UIHelpMenuComponent } from './help-menu.component';

describe('UIHelpMenuComponent', () => {
    let component: UIHelpMenuComponent;
    let fixture: ComponentFixture<UIHelpMenuComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            providers: [UINewThemeService],
            imports: [UIHelpMenuComponent]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(UIHelpMenuComponent);
        component = fixture.componentInstance;
    });

    it('openSupportTab should open new support window', () => {
        const windowSpy = spyOn(window, 'open');
        component.openSupportTab();
        expect(windowSpy).toHaveBeenCalledWith('https://support.bannerflow.com', '_blank');
    });
});
