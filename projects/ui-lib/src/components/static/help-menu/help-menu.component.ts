import { Component, EventEmitter, inject, input, Input, Output } from '@angular/core';
import { DATASET_SIZE, UINewThemeService } from '../../../services';
import { UIButtonComponent } from '../../buttons';
import { UIIconComponent } from '../../icon';
import {
    UIDropdownComponent,
    UIDropdownItemComponent,
    UIDropdownTargetDirective
} from '../../popovers/dropdown';

@Component({
    imports: [
        UIButtonComponent,
        UIDropdownComponent,
        UIDropdownItemComponent,
        UIDropdownTargetDirective,
        UIIconComponent
    ],
    selector: 'ui-help-menu',
    templateUrl: 'help-menu.component.html',
    styleUrls: ['./help-menu.component.scss', './help-menu.component.new.scss'],
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIHelpMenuComponent {
    private readonly uiNewThemeService = inject(UINewThemeService);

    /**
     * Is intercom widget available in application. True as default.
     */
    @Input() isIntercomAvailable = true;

    size = input<'xs' | 'sm' | 'md'>('md');
    /**
     * Show intercom message void event
     */
    @Output() showIntercom = new EventEmitter<void>();

    /**
     * Show intercom news space void event
     */
    @Output() showIntercomNews = new EventEmitter<void>();

    /**
     * Show keyboard shortcuts void event
     */
    @Output() showKeyboardShortcuts = new EventEmitter<void>();

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    openSupportTab(): void {
        window.open('https://support.bannerflow.com', '_blank');
    }

    emitIntercomNews(): void {
        this.showIntercomNews.emit();
    }

    emitIntercomMessage(): void {
        this.showIntercom.emit();
    }

    emitKeyboardShortcuts(): void {
        this.showKeyboardShortcuts.emit();
    }

    get isKeyboardShortcutsObserved(): boolean {
        return this.showKeyboardShortcuts.observed;
    }
}
