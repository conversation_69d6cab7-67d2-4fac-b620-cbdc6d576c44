<div
    class="help-wrapper"
    [uiDropdownTarget]="helpMenuDropDownMenu">
    @if (isNewUI()) {
        <ui-button
            data-testId="help-button"
            nuiType="plain-primary"
            svgIcon="none"
            nuiSvgIcon="help"
            [size]="size()" />
    } @else {
        <span
            class="help-icon"
            id="interaction-help-center">
            <ui-icon icon="help" />
        </span>
    }
    <ui-dropdown
        #helpMenuDropDownMenu
        size="sm">
        <ui-dropdown-item
            id="interaction-knowledge-base"
            svgIcon="question-mark-s"
            nuiIcon="help_center"
            (click)="openSupportTab()">
            Knowledge base
        </ui-dropdown-item>

        @if (isIntercomAvailable) {
            <ui-dropdown-item
                id="interaction-chat-with-us"
                svgIcon="comments"
                nuiIcon="chat_bubble"
                (click)="emitIntercomMessage()">
                Chat with us
            </ui-dropdown-item>

            <ui-dropdown-item
                id="interaction-launched-features"
                svgIcon="announcement"
                nuiIcon="campaign"
                (click)="emitIntercomNews()">
                Launched features
            </ui-dropdown-item>
        }

        @if (isKeyboardShortcutsObserved) {
            <ui-dropdown-item
                id="interaction-keyboard-shortcuts"
                nuiIcon="keyboard"
                svgIcon="keyboard"
                (click)="emitKeyboardShortcuts()">
                Keyboard shortcuts
            </ui-dropdown-item>
        }
    </ui-dropdown>
</div>
