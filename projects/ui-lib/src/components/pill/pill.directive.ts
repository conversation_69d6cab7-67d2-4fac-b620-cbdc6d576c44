import { NgStyle } from '@angular/common';
import {
    computed,
    Directive,
    effect,
    ElementRef,
    inject,
    input,
    KeyValueDiffers,
    Renderer2
} from '@angular/core';
import { DATASET_SIZE } from '../../services/uinew-theme.service';
import { UIPillType, UIPillVariant } from '../../types';

@Directive({
    standalone: true,
    selector: '[ui-pill], [uiPill]',
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIPillDirective extends NgStyle {
    variant = input<UIPillVariant>('info', { alias: 'ui-pill-variant' });
    type = input<UIPillType>('primary', { alias: 'ui-pill-type' });
    size = input<'xs' | 'sm' | 'md'>('md', { alias: 'ui-pill-size' });

    private backgroundColor = computed(() => this.computeBackgroundColor(this.type()));
    private textColor = computed(() => this.computeTextColor(this.type()));
    private fontWeight = computed(() => (this.size() === 'md' ? 'regular' : 'bold'));

    constructor() {
        const elementRef = inject(ElementRef);

        super(elementRef, inject(KeyValueDiffers), inject(Renderer2));

        effect(() => {
            const backgroundColor = this.backgroundColor();
            const textColor = this.textColor();
            const fontWeight = this.fontWeight();
            // Set styles for component
            this.ngStyle = {
                display: 'inline-flex',
                'justify-content': 'center',
                'align-items': 'center',
                'max-width': '250px',

                padding:
                    'var(--nui-pill-space-padding-vertical, 0px) var(--nui-pill-space-padding-horizontal, 4px)',
                'border-radius': 'var(--nui-pill-radius, 4px)',

                color: textColor,
                'background-color': backgroundColor,

                'font-family': `var(--nui-label-${fontWeight}-font-family, "Roboto Flex")`,
                'font-size': `var(--nui-label-${fontWeight}-font-size)`,
                'font-style': 'normal',
                'font-weight': `var(--nui-label-${fontWeight}-font-weight)`,
                'line-height': `var(--nui-label-${fontWeight}-line-height)`,
                'letter-spacing': `var(--nui-body-${fontWeight}-letter-spacing)`
            };
        });
    }

    private computeBackgroundColor(type: 'primary' | 'secondary'): string {
        const variant = this.variant();
        switch (variant) {
            case 'neutral':
                return `var(--nui-pill-fill-${type}-neutral)`;
            case 'error':
                return `var(--nui-pill-fill-${type}-error)`;
            case 'warning':
                return `var(--nui-pill-fill-${type}-warning)`;
            case 'success':
                return `var(--nui-pill-fill-${type}-success)`;
            case 'indigo':
                return `var(--nui-pill-fill-${type}-category-indigo)`;
            case 'pink':
                return `var(--nui-pill-fill-${type}-category-pink)`;
            case 'teal':
                return `var(--nui-pill-fill-${type}-category-teal)`;
            case 'tomato':
                return `var(--nui-pill-fill-${type}-category-tomato)`;
            case 'orange':
                return `var(--nui-pill-fill-${type}-category-orange)`;
            case 'green':
                return `var(--nui-pill-fill-${type}-category-green)`;
            case 'info':
            default:
                return `var(--nui-pill-fill-${type}-info)`;
        }
    }

    private computeTextColor(type: 'primary' | 'secondary'): string {
        const variant = this.variant();
        switch (variant) {
            case 'neutral':
                return `var(--nui-pill-text-${type}-neutral)`;
            case 'error':
                return `var(--nui-pill-text-${type}-error)`;
            case 'warning':
                return `var(--nui-pill-text-${type}-warning)`;
            case 'success':
                return `var(--nui-pill-text-${type}-success)`;
            case 'indigo':
                return `var(--nui-pill-text-${type}-indigo)`;
            case 'pink':
                return `var(--nui-pill-text-${type}-pink)`;
            case 'teal':
                return `var(--nui-pill-text-${type}-teal)`;
            case 'tomato':
                return `var(--nui-pill-text-${type}-tomato)`;
            case 'orange':
                return `var(--nui-pill-text-${type}-orange)`;
            case 'green':
                return `var(--nui-pill-text-${type}-green)`;
            case 'info':
            default:
                return `var(--nui-pill-text-${type}-info)`;
        }
    }
}
