import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { injectInjectorToProps } from '../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIPillDirective } from './pill.directive';
import { UIPillComponent } from './pill.component';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIComponentSizeDirective } from '../../directives';

const statuses = ['info', 'warning', 'error', 'success', 'neutral'];
const categories = ['indigo', 'pink', 'teal', 'tomato', 'orange', 'green'];
const variants = [...statuses, ...categories];

const meta: Meta = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Pills',
    decorators: [
        moduleMetadata({
            imports: [UIPillDirective, UIPillComponent, UIComponentSizeDirective]
        }),
        injectInjectorToProps
    ],
    args: {
        variant: 'info',
        type: 'primary',
        size: 'md'
    },
    argTypes: {
        variant: {
            control: 'select',
            options: variants
        },
        type: {
            control: 'select',
            options: ['primary', 'secondary']
        },
        size: {
            control: 'select',
            options: ['md', 'sm', 'xs']
        }
    }
};

export default meta;

type Story = StoryObj;

export const Single: Story = {
    render: ({ ...args }) => ({
        props: args,
        template: `
        <div>
            <div [uiPill] [ui-pill-variant]="variant" [ui-pill-type]="type" [ui-pill-size]="size" >Pill: {{variant}}</div>
            <ui-pill  [variant]="variant" [type]="type" [size]="size">Pill: {{variant}}</ui-pill>
        </div>`
    })
};

export const NUIPillDirective: Story = {
    args: {
        variants
    },
    render: args => ({
        ...args,
        props: {
            ...args
        },
        template: `
        <style>
            .pill-showcase {
                display: flex;
                gap: 24px;
                padding: 30px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .size-container {
                display: flex;
                flex-direction: column;
                text-align: center;
            }
            h5, h6 {
                margin: 16px 0;
            }

            .size-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
                min-width: 200px;
                justify-items: center;
                text-align: center;
            }
        </style>
        <div class="pill-showcase">
            <div class="size-container">
                <h5 [uiSize]="'lg'">md</h5>
                <div class="size-grid">
                    <!-- Headers -->
                    <h6 [uiSize]="'md'">secondary</h6>
                    <h6 [uiSize]="'md'">primary</h6>
                    @for (variant of variants; track variant) {
                        <div [uiPill] ui-pill-size="md" [ui-pill-variant]="variant" ui-pill-type="secondary">Pill</div>
                        <div [uiPill] ui-pill-size="md" [ui-pill-variant]="variant" ui-pill-type="primary">Pill</div>
                    }

                </div>
            </div>

            <div class="size-container">
                <h5 [uiSize]="'lg'">sm</h5>
                <div class="size-grid">
                    <!-- Headers -->
                    <h6 [uiSize]="'md'">secondary</h6>
                    <h6 [uiSize]="'md'">primary</h6>
                    @for (variant of variants; track variant) {
                        <div [uiPill] ui-pill-size="sm" [ui-pill-variant]="variant" ui-pill-type="secondary">Pill</div>
                        <div [uiPill] ui-pill-size="sm" [ui-pill-variant]="variant" ui-pill-type="primary">Pill</div>
                    }
                </div>
            </div>

            <div class="size-container">
                <h5 [uiSize]="'lg'">xs</h5>
                <div class="size-grid">
                    <!-- Headers -->
                    <h6 [uiSize]="'md'">secondary</h6>
                    <h6 [uiSize]="'md'">primary</h6>

                    @for (variant of variants; track variant) {
                        <div [uiPill] ui-pill-size="xs" [ui-pill-variant]="variant" ui-pill-type="secondary">Pill</div>
                        <div [uiPill] ui-pill-size="xs" [ui-pill-variant]="variant" ui-pill-type="primary">Pill</div>
                    }
                </div>
            </div>
        </div>
        `
    })
};

export const NUIPillComponent: Story = {
    args: {
        variants
    },
    render: args => ({
        ...args,
        props: {
            ...args
        },
        template: `
        <style>
            .pill-showcase {
                display: flex;
                gap: 24px;
                padding: 30px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .size-container {
                display: flex;
                flex-direction: column;
                text-align: center;
            }
            h5, h6 {
                margin: 16px 0;
            }

            .size-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
                min-width: 200px;
                justify-items: center;
                text-align: center;
            }
        </style>
        <div class="pill-showcase">
            <div class="size-container">
                <h5 [uiSize]="'lg'">md</h5>
                <div class="size-grid">
                    <!-- Headers -->
                    <h6 [uiSize]="'md'">secondary</h6>
                    <h6 [uiSize]="'md'">primary</h6>

                    @for (variant of variants; track variant) {
                        <ui-pill size="md" [variant]="variant" type="secondary">Pill</ui-pill>
                        <ui-pill size="md" [variant]="variant" type="primary">Pill</ui-pill>
                    }
                </div>
            </div>


            <div class="size-container">
                <h5 [uiSize]="'lg'">sm</h5>
                <div class="size-grid">
                    <!-- Headers -->
                    <h6 [uiSize]="'md'">secondary</h6>
                    <h6 [uiSize]="'md'">primary</h6>

                    @for (variant of variants; track variant) {
                        <ui-pill size="sm" [variant]="variant" type="secondary">Pill</ui-pill>
                        <ui-pill size="sm" [variant]="variant" type="primary">Pill</ui-pill>
                    }
                </div>
            </div>

            <div class="size-container">
                <h5 [uiSize]="'lg'">xs</h5>
                <div class="size-grid">
                    <!-- Headers -->
                    <h6 [uiSize]="'md'">secondary</h6>
                    <h6 [uiSize]="'md'">primary</h6>

                    @for (variant of variants; track variant) {
                        <ui-pill size="xs" [variant]="variant" type="secondary">Pill</ui-pill>
                        <ui-pill size="xs" [variant]="variant" type="primary">Pill</ui-pill>
                    }
                </div>
            </div>
        </div>
        `
    })
};
