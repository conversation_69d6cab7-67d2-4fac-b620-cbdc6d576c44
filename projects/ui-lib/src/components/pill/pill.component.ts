import { Component, input } from '@angular/core';
import { DATASET_SIZE } from '../../services/uinew-theme.service';
import { UIPillType, UIPillVariant } from '../../types';
import { UILabelComponent } from '../label/label.component';

@Component({
    selector: 'ui-pill',
    template: `
        <ui-label
            [truncate]="true"
            class="ui-label"
            [weight]="size() === 'md' ? 'regular' : 'bold'"
            [size]="size()">
            <ng-content />
        </ui-label>
    `,
    styles: [
        `
            :where(:root[data-uinew]) :host {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                max-width: 100%;
                width: fit-content;
                padding: var(--nui-pill-space-padding-vertical, 0px)
                    var(--nui-pill-space-padding-horizontal, 4px);
                border-radius: var(--nui-pill-radius, 4px);
                background-color: var(--computed-bg-color);

                --computed-bg-color: var(--nui-pill-fill-primary-info);
                --computed-text-color: var(--nui-pill-text-primary-info);

                ui-label {
                    color: var(--computed-text-color);
                }

                &.primary.neutral {
                    --computed-bg-color: var(--nui-pill-fill-primary-neutral);
                    --computed-text-color: var(--nui-pill-text-primary-neutral);
                }
                &.primary.error {
                    --computed-bg-color: var(--nui-pill-fill-primary-error);
                    --computed-text-color: var(--nui-pill-text-primary-error);
                }
                &.primary.warning {
                    --computed-bg-color: var(--nui-pill-fill-primary-warning);
                    --computed-text-color: var(--nui-pill-text-primary-warning);
                }
                &.primary.success {
                    --computed-bg-color: var(--nui-pill-fill-primary-success);
                    --computed-text-color: var(--nui-pill-text-primary-success);
                }
                &.primary.info {
                    --computed-bg-color: var(--nui-pill-fill-primary-info);
                    --computed-text-color: var(--nui-pill-text-primary-info);
                }

                &.primary.indigo {
                    --computed-bg-color: var(--nui-pill-fill-primary-category-indigo);
                    --computed-text-color: var(--nui-pill-text-primary-indigo);
                }

                &.primary.pink {
                    --computed-bg-color: var(--nui-pill-fill-primary-category-pink);
                    --computed-text-color: var(--nui-pill-text-primary-pink);
                }

                &.primary.teal {
                    --computed-bg-color: var(--nui-pill-fill-primary-category-teal);
                    --computed-text-color: var(--nui-pill-text-primary-teal);
                }

                &.primary.tomato {
                    --computed-bg-color: var(--nui-pill-fill-primary-category-tomato);
                    --computed-text-color: var(--nui-pill-text-primary-tomato);
                }

                &.primary.orange {
                    --computed-bg-color: var(--nui-pill-fill-primary-category-orange);
                    --computed-text-color: var(--nui-pill-text-primary-orange);
                }

                &.primary.green {
                    --computed-bg-color: var(--nui-pill-fill-primary-category-green);
                    --computed-text-color: var(--nui-pill-text-primary-green);
                }

                &.secondary.neutral {
                    --computed-bg-color: var(--nui-pill-fill-secondary-neutral);
                    --computed-text-color: var(--nui-pill-text-secondary-neutral);
                }
                &.secondary.error {
                    --computed-bg-color: var(--nui-pill-fill-secondary-error);
                    --computed-text-color: var(--nui-pill-text-secondary-error);
                }
                &.secondary.warning {
                    --computed-bg-color: var(--nui-pill-fill-secondary-warning);
                    --computed-text-color: var(--nui-pill-text-secondary-warning);
                }
                &.secondary.success {
                    --computed-bg-color: var(--nui-pill-fill-secondary-success);
                    --computed-text-color: var(--nui-pill-text-secondary-success);
                }
                &.secondary.info {
                    --computed-bg-color: var(--nui-pill-fill-secondary-info);
                    --computed-text-color: var(--nui-pill-text-secondary-info);
                }

                &.secondary.indigo {
                    --computed-bg-color: var(--nui-pill-fill-secondary-category-indigo);
                    --computed-text-color: var(--nui-pill-text-secondary-indigo);
                }

                &.secondary.pink {
                    --computed-bg-color: var(--nui-pill-fill-secondary-category-pink);
                    --computed-text-color: var(--nui-pill-text-secondary-pink);
                }

                &.secondary.teal {
                    --computed-bg-color: var(--nui-pill-fill-secondary-category-teal);
                    --computed-text-color: var(--nui-pill-text-secondary-teal);
                }

                &.secondary.tomato {
                    --computed-bg-color: var(--nui-pill-fill-secondary-category-tomato);
                    --computed-text-color: var(--nui-pill-text-secondary-tomato);
                }

                &.secondary.orange {
                    --computed-bg-color: var(--nui-pill-fill-secondary-category-orange);
                    --computed-text-color: var(--nui-pill-text-secondary-orange);
                }

                &.secondary.green {
                    --computed-bg-color: var(--nui-pill-fill-secondary-category-green);
                    --computed-text-color: var(--nui-pill-text-secondary-green);
                }
            }
        `
    ],
    host: {
        '[class]': 'variant()',
        '[class.secondary]': 'type() === "secondary"',
        '[class.primary]': 'type() === "primary"',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    },
    imports: [UILabelComponent]
})
export class UIPillComponent {
    variant = input<UIPillVariant>('info');
    type = input<UIPillType>('primary');
    size = input<'xs' | 'sm' | 'md'>('md');
}
