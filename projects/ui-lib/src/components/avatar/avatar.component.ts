import { SlicePipe, UpperCasePipe } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { DATASET_SIZE } from '../../services';

/**
 * UIAvatar shows an user Avatar
 * Can show initals or picture
 *
 * Style parameters:
 * * `--background-color`: background color when the style is 'initials'
 * * `--text-color`: text color when the style is 'initials'
 */
@Component({
    selector: 'ui-avatar',
    imports: [UpperCasePipe, SlicePipe],
    template: `
        @if (style() === 'initials') {
            <span>{{ text() | slice: 0 : 2 | uppercase }}</span>
        }
    `,
    styles: `
        :host {
            --background-color: var(--nui-fill-neutral-boldest, #1e1f24);
            --text-color: var(--nui-text-primary-inverted, #fff);

            display: flex;
            width: var(--nui-avatar-width, 40px);
            height: var(--nui-avatar-height, 40px);
            justify-content: center;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
            border-radius: var(--nui-avatar-radius-default, 8px);
            background: var(--background-color);

            --image-path: none;

            &.rounded {
                border-radius: var(--nui-avatar-radius-full, 112px);
            }

            &.picture {
                /* this, but split: background: url(<path-to-image>) lightgray 50% / cover no-repeat, var(--fill-neutral-boldest, #1E1F24);*/
                background-color: lightgray;
                background-image: var(--image-path);
                background-repeat: no-repeat;
                background-size: contain;
                background-position:
                    50%,
                    0 0;
                background-size: cover, auto;
                background-repeat: no-repeat;
                background-attachment: scroll;
                background-origin: padding-box;
                background-clip: border-box;
            }

            span {
                color: var(--text-color);
                text-align: center;

                font-family: var(--nui-body-bold-md-font-family, 'Roboto Flex');
                font-size: var(--nui-body-bold-md-font-size, 14px);
                font-style: normal;
                font-weight: var(--nui-body-bold-md-font-weight, 500);
                line-height: var(--nui-body-bold-md-line-height, 21px); /* 150% */
                letter-spacing: var(--nui-body-bold-md-letter-spacing, 0.18px);
            }
        }
    `,
    host: {
        '[class.rounded]': 'rounded()',
        '[class.picture]': 'style() === "picture"',
        '[style.--image-path]': 'url()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIAvatarComponent {
    size = input<'sm' | 'md'>('md');
    style = input<'initials' | 'picture'>('initials');
    text = input<string>('');
    pictureUrl = input<string>();
    rounded = input(false);

    protected url = computed(() => `url(${this.pictureUrl()})`);
}
