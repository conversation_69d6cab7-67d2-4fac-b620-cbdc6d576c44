import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIAvatarComponent } from './avatar.component';

const meta: Meta<UIAvatarComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Avatar',
    component: UIAvatarComponent,
    argTypes: {
        style: { type: 'string', control: { type: 'radio' }, options: ['initials', 'picture'] },
        pictureUrl: { type: 'string' }
    }
};
export default meta;

type Story = StoryObj<UIAvatarComponent & { size: 'sm' | 'md' }>;

export const Playground: Story = {
    argTypes: {
        size: {
            control: 'inline-radio',
            options: ['sm', 'md']
        }
    },
    args: {
        pictureUrl: `${window.location.origin}/images/avatars/avatar-01.jpg`,
        style: 'initials',
        text: 'SH',
        rounded: false,
        size: 'sm'
    }
};
export const AllOptions: StoryObj = {
    args: {
        pictureUrl: `${window.location.origin}/images/avatars/avatar-01.jpg`
    },
    render: args => ({
        args,
        props: { ...args },
        template: `
<div style="display:flex; flex-direction:column;">
    <strong>initials - MD</strong>
    <div style="display:flex; gap: 16px; ">
        <ui-avatar [style]="'initials'" [text]="'SH'" [rounded]="false" [size]="'md'"/>
        <ui-avatar [style]="'initials'" [text]="'SH'" [rounded]="true" [size]="'md'"/>
    </div>
    <hr />
    <strong>initials - SM</strong>
    <div style="display:flex; gap: 16px; ">
     <ui-avatar [style]="'initials'" [text]="'SH'" [rounded]="false" [size]="'sm'"/>
     <ui-avatar [style]="'initials'" [text]="'SH'" [rounded]="true" [size]="'sm'"/>
    </div>
    <hr />
    <strong>Picture - MD</strong>
    <div style="display:flex; gap: 16px; ">
     <ui-avatar [style]="'picture'" [pictureUrl]="pictureUrl" [rounded]="false" [size]="'md'"/>
     <ui-avatar [style]="'picture'" [pictureUrl]="pictureUrl" [rounded]="true" [size]="'md'"/>
    </div>
    <hr />
    <strong>Picture - SM</strong>
    <div style="display:flex; gap: 16px; ">
        <ui-avatar [style]="'picture'" [pictureUrl]="pictureUrl" [rounded]="false" [size]="'sm'"/>
        <ui-avatar [style]="'picture'" [pictureUrl]="pictureUrl" [rounded]="true" [size]="'sm'"/>
    </div>
</div>
     `
    })
};
