@let invalid = validation?.invalid && (validation?.touched || !validation?.pristine);

@if (label) {
    @if (!isNewUI()) {
        <label
            class="label"
            for="{{ id }}">
            {{ label }}
        </label>
    } @else if (nuiType() !== 'secondary') {
        <div class="label-wrapper">
            <label
                class="label"
                [uiSize]="size() === 'xs' ? 'xs' : 'sm'"
                for="{{ id }}">
                {{ label }}
            </label>
        </div>
    }
}

<div
    class="input-wrapper"
    [class.dynamic-width]="dynamicWidth">
    <input
        class="input"
        id="{{ id }}"
        spellcheck="false"
        [uiSize]="size()"
        [tabindex]="disabled ? -1 : tabindex ? tabindex : 0"
        [attr.maxlength]="maxlength"
        [attr.minlength]="minlength"
        [autocomplete]="autocomplete"
        [readonly]="disabled"
        [class.invalid]="invalid"
        [class.icon-left]="(icon || svgIcon || nuiSvgIcon()) && (isNewUI() || iconPosition === 'left')"
        [class.icon-right]="(icon || svgIcon || nuiSvgIcon()) && !isNewUI() && iconPosition === 'right'"
        [class.keyboard-focus]="hasKeyboardFocus && inputFocused"
        [class.ui-title]="typographyStyle() === 'title'"
        [placeholder]="placeholder"
        [type]="type"
        [value]="value"
        [(ngModel)]="value"
        (blur)="onBlur()"
        (focus)="onFocus()"
        (input)="pushChanges($event)"
        (keydown)="onKeyDown($event)"
        (keyup)="onKeyUp($event)"
        (keyup.escape)="cancel.emit()"
        #valueContainer />
    @if (dynamicWidth && isNewUI()) {
        <div
            class="input"
            [uiSize]="size()"
            [class.invalid]="invalid"
            [class.icon-left]="
                (icon || svgIcon || nuiSvgIcon()) && (isNewUI() || iconPosition === 'left')
            "
            [class.icon-right]="
                (icon || svgIcon || nuiSvgIcon()) && !isNewUI() && iconPosition === 'right'
            "
            [class.keyboard-focus]="hasKeyboardFocus && inputFocused"
            [textContent]="value || placeholder"
            [class.ui-title]="typographyStyle() === 'title'"></div>
    }

    @if (icon) {
        <ui-icon
            class="input-icon"
            [class.expand-icon]="expand"
            [class.input-icon-left]="isNewUI() || iconPosition === 'left'"
            [class.input-icon-right]="!isNewUI() && iconPosition === 'right'"
            [icon]="icon">
        </ui-icon>
    }

    @if (svgIcon || nuiSvgIcon()) {
        <ui-svg-icon
            [icon]="svgIcon"
            [size]="size()"
            [nuiIcon]="nuiSvgIcon()"
            [class.expand-icon]="expand"
            [class.input-icon-left]="isNewUI() || iconPosition === 'left'"
            [class.input-icon-right]="!isNewUI() && iconPosition === 'right'"
            class="input-icon"
            (click)="onClickIcon()">
        </ui-svg-icon>
    }
    @if (isNewUI() && nuiType() !== 'secondary' && nuiType() !== 'transparent') {
        <ui-svg-icon
            class="input-icon input-icon-right clear-icon"
            [icon]="'close'"
            [class.hidden]="!value"
            [size]="size()"
            (click)="clearInput($event)" />
    }
</div>

@if (isNewUI() && helpText && nuiType() !== 'secondary') {
    <div class="help-text-wrapper">
        <ui-label
            class="help-text"
            size="xs"
            [leadingIcon]="invalid ? 'error' : undefined"
            [type]="disabled ? 'disabled' : invalid ? 'destructive' : 'secondary'">
            {{ helpText }}
        </ui-label>
    </div>
}
