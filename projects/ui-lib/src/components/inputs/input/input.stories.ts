import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { fn } from 'storybook/test';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent, UISVGIconComponent } from '../../icon/';
import { UIInputComponent } from '../../inputs';

const meta: Meta<UIInputComponent> = {
    title: 'Components/Inputs/Input',
    component: UIInputComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIInputComponent,
                UIIconComponent,
                UISVGIconComponent,
                CommonModule,
                FormsModule,
                BrowserModule
            ]
        })
    ]
};
export default meta;

type Story = StoryObj<UIInputComponent>;

export const Input: Story = {
    args: {
        type: 'text',
        onChange: fn(),
        onTouched: fn(),
        valueChange: fn()
    }
};
