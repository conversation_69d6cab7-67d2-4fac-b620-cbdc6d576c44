import { moduleMetadata } from '@storybook/angular';
import type { <PERSON>a, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { materialIcons } from '../../icon/svg-icon/nui-material.icons';
import { icons } from '../../icon/svg-icon/nui.icons';
import { UIInputComponent } from '../../inputs';

const meta: Meta<UIInputComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Input',
    component: UIInputComponent,
    decorators: [moduleMetadata({ imports: [UIInputComponent] })],
    argTypes: {
        size: {
            control: 'inline-radio',
            options: ['xs', 'sm', 'md']
        },
        nuiSvgIcon: {
            control: 'select',
            options: [undefined, ...icons, ...materialIcons]
        },
        placeholder: { control: 'text' },
        value: { control: 'text' },
        nuiType: { control: 'inline-radio', options: ['primary', 'secondary', 'transparent'] },
        typographyStyle: {
            control: 'inline-radio',
            options: ['label', 'title']
        },
        dynamicWidth: {
            control: 'boolean'
        }
    },
    args: {
        helpText: 'Help text',
        nuiType: 'primary',
        label: 'Label',
        nuiSvgIcon: undefined,
        placeholder: 'My placeholder',
        size: 'md',
        dynamicWidth: false
    },
    parameters: {
        controls: {
            include: [
                'label',
                'helpText',
                'nuiSvgIcon',
                'nuiType',
                'placeholder',
                'value',
                'size',
                'disabled',
                'expand',
                'typographyStyle',
                'dynamicWidth'
            ]
        },
        docs: {
            description: {
                component: `
The following element sizes should be consistent across **all forms** components (input, search, number, select, textarea).
Sometimes you may notice that something is different in Figma.
In this case please contact a designer and ask if the change was intentional or if it is a mistake.

#### Sizes
##### MD
- label: **sm**
- input: **md**
- help text: **xs**

##### SM
- label: **sm**
- input: **sm**
- help text: **xs**

##### XS
- label: **xs**
- input: **xs**
- help text: **xs**
                `
            }
        }
    }
};
export default meta;

type Story = StoryObj<UIInputComponent>;

export const Playground: Story = {};

export const ExpandableSearchInput: Story = {
    parameters: {
        controls: {
            include: ['disabled', 'size', 'nuiType']
        }
    },
    args: {
        size: 'md',
        disabled: false
    },
    render: args => ({
        props: args,
        template: `
<ui-input
    [nuiType]="nuiType"
    [size]="size"
    nuiSvgIcon="search"
    [expand]="true"
    [disabled]="disabled" />
        `
    })
};
