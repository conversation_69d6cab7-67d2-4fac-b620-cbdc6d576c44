@use '../../../style/mixins';

:where(:root:not([data-uinew])) :host {
    position: relative;
    display: block;
    overflow: hidden;
    box-sizing: border-box;

    .input-wrapper {
        position: relative;
        width: 100%;
        height: inherit;
        display: inherit;

        input {
            width: inherit;
        }
    }

    &.discrete {
        .input {
            border-color: transparent;
            background: transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &[readonly] {
                border: none;
            }

            &:hover {
                border: var(--ui-border);
                background: var(--ui-color-surface);
            }

            &:focus {
                border: 1px solid var(--ui-color-focus);
                background: var(--ui-color-surface);
            }
        }
    }

    &.super-discrete {
        .input {
            border-color: transparent;
            background: transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &[readonly] {
                border: none;
            }

            &:hover {
                border: 1px solid transparent;
                background: var(--ui-color-surface);
            }

            &:focus {
                border: 1px solid transparent;
                background: var(--ui-color-surface);
            }
        }
    }

    &.expand {
        width: var(--height);
        transition: width 0.2s ease;

        .input {
            &.icon-left {
                padding-left: 0;
            }

            &.icon-right {
                padding-right: 0;
            }
        }

        ui-icon.expand-icon {
            color: var(--ui-color-primary);

            &.input-icon-left {
                margin-left: calc((var(--height) - var(--font-size)) / 2);
            }

            &.input-icon-right {
                margin-right: calc((var(--height) - var(--font-size)) / 2);
            }
        }

        ui-svg-icon.expand-icon {
            color: var(--ui-color-primary);

            &.input-icon-left {
                margin-left: calc((var(--height) - var(--font-size)) / 2);
            }

            &.input-icon-right {
                margin-right: calc((var(--height) - var(--font-size)) / 2);
            }
        }

        &:focus-within,
        &.keep-expanded {
            width: 100%;

            ui-icon.expand-icon {
                color: var(--ui-color-text-second);
            }

            ui-svg-icon.expand-icon {
                color: var(--ui-color-text-second);
            }

            .input {
                &.icon-left {
                    padding-left: calc(var(--height) - 4px);
                }

                &.icon-right {
                    padding-right: calc(var(--height) - 4px);
                }
            }
        }
    }

    .label {
        display: block;
        color: var(--ui-color-text);
        text-align: left;
        font-family: inherit;
        font-size: var(--font-size);
        font-weight: var(--font-weight);
        margin-bottom: var(--margin-bottom);
    }

    .input {
        width: 100%;
        display: block;
        height: var(--height);
        font-size: var(--font-size);
        font-weight: inherit;
        text-shadow: inherit;
        border: var(--ui-border);
        padding: var(--padding);
        outline: none;
        background-color: var(--ui-color-surface);
        color: var(--ui-color-text);
        border-radius: var(--ui-border-radius);

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            /* display: none; <- Crashes Chrome on hover */
            appearance: none;
            margin: 0;

            /* <-- Apparently some margin are still there even though it's hidden */
        }

        &::-ms-clear {
            display: none;
        }

        &[type='number'] {
            appearance: textfield;
        }

        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus {
            box-shadow: 0 0 0 1000px var(--ui-color-surface) inset !important;
        }

        &:focus {
            border: 1px solid var(--ui-color-primary);

            @include mixins.ui-placeholder {
                color: var(--ui-color-text-second);
            }
        }

        &[readonly] {
            background-color: var(--ui-color-disabled);
            color: var(--ui-color-text-disabled);
            border: var(--ui-border);
        }

        @include mixins.ui-placeholder {
            color: var(--ui-color-text-second);
        }

        &.invalid {
            border-color: var(--ui-color-alert);
        }

        &-icon {
            position: relative;
            margin-top: var(--margin-top);
            line-height: inherit;
            font-size: var(--font-size);
            color: var(--ui-color-text-second);
            pointer-events: none;
        }

        &.icon-left {
            padding-left: 2.5rem;
        }

        &.icon-right {
            padding-right: 2.5rem;
        }
    }

    ui-icon.input-icon {
        &-right {
            float: right;
            margin-right: 0.75rem;
        }

        &-left {
            float: left;
            margin-left: 0.75rem;
        }
    }

    ui-svg-icon.input-icon {
        &-right {
            float: right;
            margin-right: 0.75rem;
        }

        &-left {
            float: left;
            margin-left: 0.75rem;
        }
    }
}
