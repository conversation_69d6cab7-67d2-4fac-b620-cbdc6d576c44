@use '../../../style/mixins';

:where(:root[data-uinew]) :host {
    --input-padding-horizontal: var(--nui-forms-space-padding-horizontal, 12px);
    --input-height: var(--nui-forms-height, 40px);

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .input-wrapper {
        position: relative;
        width: 100%;
        height: inherit;
        display: inherit;

        &.dynamic-width {
            width: auto;
            max-width: 100%;
            
            div.input {
                visibility: hidden;
                white-space: pre;
                max-width: 100%;
                overflow: hidden;
            }

            input {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
            }
        }

        .input {
            width: inherit;
            font-family: var(--nui-label-regular-font-family);
            font-weight: var(--nui-label-regular-font-weight);
            font-size: var(--nui-label-regular-font-size);
            line-height: var(--nui-label-regular-line-height);
            letter-spacing: var(--nui-label-regular-letter-spacing);

            &.ui-title {
                font-family: var(--nui-title-font-family);
                font-weight: var(--nui-title-font-weight);
                font-size: var(--nui-title-font-size);
                line-height: var(--nui-title-line-height);
                letter-spacing: var(--nui-title-letter-spacing);
            }
        }
    }

    .label-wrapper {
        padding: 0 0 var(--nui-forms-label-space-padding-bottom, 4px) var(--nui-forms-label-space-padding-left, 2px);

        .label {
            display: flex;
            font-style: normal;
            font-size: var(--nui-label-regular-font-size, 12px);
            line-height: var(--nui-label-regular-line-height, 16px);
            font-weight: var(--nui-label-regular-font-weight, 400);
            letter-spacing: var(--nui-label-regular-letter-spacing);
        }
    }

    .help-text-wrapper {
        padding: var(--nui-forms-help-space-padding-top, 4px) 0 0 var(--nui-forms-help-space-padding-left, 4px);
    }

    .clear-icon {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
        color: var(--nui-icon-primary);
        visibility: visible;

        &.hidden {
            visibility: hidden;
        }
    }

    input.input:not(:focus) ~ .clear-icon:not(:active) { // hides the close icon if input is not focused but keeps it visible for click event to trigger
        visibility: hidden;
    }

    .input {
        position: relative;
        display: flex;
        height: var(--input-height);
        padding: 0 var(--input-padding-horizontal);
        align-items: center;
        gap: var(--nui-forms-space-gap, 8px);
        align-self: stretch;
        outline: none;
        border-radius: var(--nui-forms-radius, 4px);

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            /* display: none; <- Crashes Chrome on hover */
            appearance: none;
            margin: 0;
        }

        &::-ms-clear {
            display: none;
        }

        &[type='number'] {
            appearance: textfield;
        }

        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus {
            box-shadow: 0 0 0 1000px var(--ui-color-surface) inset !important;
        }

        &[readonly] {
            border-radius: var(--nui-forms-radius, 4px);
        }

        &.icon-left {
            padding-left: calc(var(--input-padding-horizontal) + var(--nui-icon-width) + var(--nui-label-space-gap));
        }

        &.icon-right {
            padding-right: calc(var(--input-padding-horizontal) + var(--nui-icon-width) + var(--nui-label-space-gap));
        }

        &:has(~ .clear-icon) {
            padding-right: calc(var(--input-padding-horizontal) + var(--nui-icon-width) + var(--nui-label-space-gap));
        }

        &.keyboard-focus {
            outline: 2px solid var(--nui-border-system-focus);
            outline-offset: 2px;
        }
    }

    &.primary {
        color: var(--nui-forms-text-input-primary-enabled);

        .label {
            color: var(--nui-forms-text-label-primary-enabled);
        }

        .help-text {
            color: var(--nui-forms-text-help-primary-enabled);
        }

        // with value
        &:has(.input:not(:placeholder-shown)) {
            .label {
                color: var(--nui-forms-text-label-primary-filled);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-filled);
            }
        }

        &:hover:not(.expand) { // expand has it's own hover styles
            .input:not([readonly], :focus, .invalid) {
                border-color: var(--nui-forms-border-primary-hover);
                background: var(--nui-forms-fill-primary-hover);
            }

            .label {
                color: var(--nui-forms-text-label-primary-hover);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-hover);
            }
        }

        &:has(.input.invalid) {
            .label {
                color: var(--nui-forms-text-label-primary-error);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-error);
            }
        }

        &:has(.input[readonly]) {
            .label {
                color: var(--nui-forms-text-label-primary-disabled);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-disabled);
            }
        }

        .input {
            color: var(--nui-forms-text-input-primary-filled); // caret color
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-primary-enabled);
            background: var(--nui-forms-fill-primary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-primary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--input-color, var(--nui-forms-text-input-primary-filled));
                border-color: var(--nui-forms-border-primary-filled);
                background: var(--nui-forms-fill-primary-filled);
            }

            &:focus {
                color: var(--input-color, var(--nui-forms-text-input-primary-selected));
                border-color: var(--nui-forms-border-primary-selected);
                background: var(--nui-forms-fill-primary-selected);
            }

            &.invalid {
                color: var(--nui-forms-text-input-primary-error);
                border-color: var(--nui-forms-border-primary-error);
                background: var(--nui-forms-fill-primary-error);
            }

            &[readonly] {
                border-color: var(--nui-forms-border-primary-disabled);
                background: var(--nui-forms-fill-primary-disabled);
                color: var(--nui-forms-text-input-primary-disabled);

                &::placeholder {
                    color: var(--nui-forms-text-input-primary-disabled);
                }

                & ~ .input-icon {
                    color: var(--nui-forms-icon-primary-disabled);
                }
            }
        }
    }

    &.secondary,
    &.transparent {
        color: var(--nui-forms-text-input-secondary-enabled);

        &:hover:not(.expand) { // expand has it's own hover styles
            .input:not([readonly], :focus, .invalid) {
                border-color: var(--nui-forms-border-secondary-hover);
                background: var(--nui-forms-fill-secondary-hover);
            }
        }

        .input {
            color: var(--nui-forms-text-input-secondary-filled); // caret color
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-secondary-enabled);
            background: var(--nui-forms-fill-secondary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-secondary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--input-color, var(--nui-forms-text-input-secondary-filled));
                border-color: var(--nui-forms-border-secondary-filled);
                background: var(--nui-forms-fill-secondary-filled);
            }

            &:focus {
                color: var(--input-color, var(--nui-forms-text-input-secondary-selected));
                border-color: var(--nui-forms-border-secondary-selected);
                background: var(--nui-forms-fill-secondary-selected);
            }

            &.invalid {
                border-color: var(--nui-forms-border-secondary-error);
                background: var(--nui-forms-fill-secondary-error);
            }

            &[readonly] {
                border-color: var(--nui-forms-border-secondary-disabled);
                background: var(--nui-forms-fill-secondary-disabled);
                color: var(--nui-forms-text-input-secondary-disabled);

                &::placeholder {
                    color: var(--nui-forms-text-input-secondary-disabled);
                }
            }
        }
    }

    &.transparent {
        &:hover:not(.expand) {
            .input:not([readonly], :focus, .invalid) {
                background: var(--nui-forms-fill-transparent);
            }
        }

        .input {
            &,
            &:not(:placeholder-shown),
            &:focus,
            &.invalid,
            &[readonly] {
                background: var(--nui-forms-fill-transparent);
            }
        }
    }

    ui-icon.input-icon,
    ui-svg-icon.input-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        &-right {
            right: var(--nui-forms-space-padding-horizontal);
        }

        &-left {
            left: var(--nui-forms-space-padding-horizontal);
        }
    }

    &.expand {
        width: var(--input-height);
        transition: width 0.2s ease;

        &:has(.input:not(:focus)[readonly]) {
            ui-icon.expand-icon,
            ui-svg-icon.expand-icon {
                color: var(--nui-button-icon-disabled);
            }
        }

        .input {
            transition: border-radius 0.1s ease;

            &:not(:focus) {
                cursor: pointer;
                background: var(--nui-button-fill-secondary);
                border-color: var(--nui-button-fill-secondary);
                border-radius: var(--nui-button-radius);

                &:hover {
                    background: var(--nui-button-fill-secondary-hover);
                    border-color: var(--nui-button-fill-secondary-hover);
                }

                &[readonly] {
                    pointer-events: none;
                    background: var(--nui-button-fill-primary-disabled);
                    border-color: var(--nui-button-fill-primary-disabled);
                }
            }

            &.icon-left {
                padding-left: 0;
            }

            &.icon-right {
                padding-right: 0;
            }

            &:has(~ .clear-icon) {
                padding-right: 0;
            }
        }

        ui-icon.expand-icon,
        ui-svg-icon.expand-icon {
            color: var(--nui-button-icon-primary-inverted);
            pointer-events: none;

            &.input-icon-left,
            &.input-icon-right {
                left: calc(var(--input-height) / 2);
                right: auto;
                transform: translate(-50%, -50%);
            }
        }

        &:focus-within,
        &.keep-expanded {
            width: 100%;

            ui-icon.expand-icon,
            ui-svg-icon.expand-icon {
                color: var(--nui-forms-icon-primary-selected);
            }

            .input {
                &.icon-left {
                    padding-left: calc(var(--input-height) / 2 + var(--nui-icon-width) / 2 + var(--nui-label-space-gap));
                }

                &.icon-right {
                    padding-right: calc(var(--input-height) / 2 + var(--nui-icon-width) / 2 + var(--nui-label-space-gap));
                }
            }
        }
    }
}
