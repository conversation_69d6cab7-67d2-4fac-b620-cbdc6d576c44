:where(:root[data-uinew]) :host {
    --input-text-align: start;
    --input-height: var(--nui-forms-height, 40px);
    --input-padding-horizontal: var(--nui-forms-space-padding-horizontal, 12px);

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &.disabled {
        pointer-events: none;
    }

    .input-wrapper {
        position: relative;
        display: block;
        width: 100%;
    }

    .label-wrapper {
        padding: 0 0 var(--nui-forms-label-space-padding-bottom) var(--nui-forms-label-space-padding-left);

        .label {
            display: flex;
            font-style: normal;
            font-size: var(--nui-label-regular-font-size, 12);
            line-height: var(--nui-label-regular-line-height, 16px);
            font-weight: var(--nui-label-regular-font-weight, 400);
            letter-spacing: var(--nui-label-regular-letter-spacing);
        }
    }

    .help-text-wrapper {
        padding: var(--nui-forms-help-space-padding-top) 0 0 var(--nui-forms-help-space-padding-left);
    }

    .input {
        position: relative;
        display: flex;
        align-items: center;
        gap: var(--nui-forms-space-gap);
        align-self: stretch;
        height: var(--input-height);
        width: inherit;
        padding: 0 var(--input-padding-horizontal);
        outline: none;
        border-radius: var(--nui-forms-radius);
        text-align: var(--input-text-align);
        font-family: var(--nui-label-regular-font-family);
        font-weight: var(--nui-label-regular-font-weight);
        font-size: var(--nui-label-regular-font-size);
        line-height: var(--nui-label-regular-line-height);
        letter-spacing: var(--nui-label-regular-letter-spacing);

        &.keyboard-focus {
            outline: 2px solid var(--nui-border-system-focus);
            outline-offset: 2px;
        }
    }

    &.primary {
        color: var(--nui-forms-text-input-primary-enabled);

        .label {
            color: var(--nui-forms-text-label-primary-enabled);
        }

        .help-text {
            color: var(--nui-forms-text-help-primary-enabled);
        }

        // with value
        &:has(.input:not(:placeholder-shown)) {
            .label {
                color: var(--nui-forms-text-input-primary-filled);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-filled);
            }
        }

        &:hover {
            .input:not([disabled], :focus, .invalid) {
                border-color: var(--nui-forms-border-primary-hover);
                background: var(--nui-forms-fill-primary-hover);
            }

            .label {
                color: var(--nui-forms-text-label-primary-hover);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-hover);
            }
        }

        &:has(.input.invalid) {
            .label {
                color: var(--nui-forms-text-label-primary-error);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-error);
            }
        }

        &:has(.input[disabled]) {
            .unit-label {
                color: var(--nui-forms-text-input-primary-disabled);
            }

            .label {
                color: var(--nui-forms-text-label-primary-disabled);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-disabled);
            }
        }

        .input {
            color: var(--nui-forms-text-input-primary-filled); // caret color
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-primary-enabled);
            background: var(--nui-forms-fill-primary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-primary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--nui-forms-text-input-primary-filled);
                border-color: var(--nui-forms-border-primary-filled);
                background: var(--nui-forms-fill-primary-filled);
            }

            &:focus {
                color: var(--nui-forms-text-input-primary-selected);
                border-color: var(--nui-forms-border-primary-selected);
                background: var(--nui-forms-fill-primary-selected);
            }

            &.invalid {
                color: var(--nui-forms-text-input-primary-error);
                border-color: var(--nui-forms-border-primary-error);
                background: var(--nui-forms-fill-primary-error);
            }

            &[disabled] {
                border-color: var(--nui-forms-border-primary-disabled);
                background: var(--nui-forms-fill-primary-disabled);
                color: var(--nui-forms-text-input-primary-disabled);

                &::placeholder {
                    color: var(--nui-forms-text-input-primary-disabled);
                }
            }
        }
    }

    &.secondary {
        color: var(--nui-forms-text-input-secondary-enabled);

        &:hover {
            .input:not([disabled], :focus, .invalid) {
                border-color: var(--nui-forms-border-secondary-hover);
                background: var(--nui-forms-fill-secondary-hover);
            }
        }

        &:has(.input[disabled]) {
            .unit-label {
                color: var(--nui-forms-text-input-secondary-disabled);
            }
        }

        .input {
            color: var(--nui-forms-text-input-secondary-filled); // caret color
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-secondary-enabled);
            background: var(--nui-forms-fill-secondary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-secondary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--nui-forms-text-input-secondary-filled);
                border-color: var(--nui-forms-border-secondary-filled);
                background: var(--nui-forms-fill-secondary-filled);
            }

            &:focus {
                color: var(--nui-forms-text-input-secondary-selected);
                border-color: var(--nui-forms-border-secondary-selected);
                background: var(--nui-forms-fill-secondary-selected);
            }

            &.invalid {
                border-color: var(--nui-forms-border-secondary-error);
                background: var(--nui-forms-fill-secondary-error);
            }

            &[disabled] {
                border-color: var(--nui-forms-border-secondary-disabled);
                background: var(--nui-forms-fill-secondary-disabled);
                color: var(--nui-forms-text-input-secondary-disabled);

                &::placeholder {
                    color: var(--nui-forms-text-input-secondary-disabled);
                }
            }
        }
    }

    .right-content {
        position: absolute;
        top: 50%;
        right: var(--nui-forms-space-padding-horizontal);
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        gap: var(--nui-forms-space-gap);
        pointer-events: none;

        .unit-label {
            color: inherit;
            font-size: var(--nui-label-regular-font-size);
            line-height: var(--nui-label-regular-line-height);
            letter-spacing: var(--nui-label-regular-letter-spacing);
        }

        .arrow-buttons {
            display: flex;
            pointer-events: auto;
        }
    }
}
