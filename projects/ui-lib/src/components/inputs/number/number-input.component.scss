@use '../../../style/mixins';

:where(:root:not([data-uinew])) :host {
    --input-font-weight: normal;

    position: relative;
    display: block;
    overflow: hidden;
    box-sizing: border-box;

    &:hover {
        .unit-label {
            display: none;
        }

        .buttons {
            display: flex;
        }
    }

    &.discrete {
        .input {
            border-color: transparent;
            background: transparent;

            &:hover {
                border: var(--ui-border);
                background: var(--background-color);
            }

            &:focus {
                border: 1px solid var(--ui-color-focus);
                background: var(--background-color);
            }
        }
    }

    &.disabled {
        pointer-events: none;
        opacity: var(--ui-disabled-opacity);
    }

    .label {
        display: inline-block;
        color: var(--ui-color-text);
        text-align: left;
        font-family: inherit;
        font-size: var(--font-size);
        font-weight: var(--font-weight);
        margin-bottom: var(--margin-bottom);
    }

    .container {
        position: relative;
        display: block;
    }

    .input {
        width: 100%;
        display: block;
        height: var(--height);
        font-size: var(--font-size);
        font-weight: var(--input-font-weight);
        border: var(--ui-border);
        padding: var(--padding);
        outline: none;
        background-color: var(--ui-color-surface);
        color: var(--ui-color-text);
        border-radius: var(--ui-border-radius);

        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus {
            box-shadow: 0 0 0 1000px var(--background-color) inset !important;
        }

        &:focus {
            border: 1px solid var(--ui-color-focus);

            @include mixins.ui-placeholder {
                color: var(--placeholder-focus-text-color);
            }
        }

        &.disabled {
            background-color: var(--ui-color-disabled);
            color: var(--ui-color-text-disabled);
        }

        @include mixins.ui-placeholder {
            color: inherit;
            opacity: 0.55;
        }

        &.invalid {
            border-color: var(--ui-color-alert);
        }
    }

    .buttons {
        display: none;
        position: absolute;
        right: 0.1rem;
        top: 0.1rem;
        bottom: 0.1rem;
        width: calc(var(--height) * 0.6);
        background: var(--ui-color-surface);
        border-radius: 0 0.2rem 0.2rem 0;
        flex-direction: column;
        border-left: 0.1rem solid var(--ui-color-border);

        .button {
            flex: 1;
            position: relative;
            cursor: pointer;
            user-select: none;

            &.disabled {
                pointer-events: none;

                &.up {
                    &::after {
                        border-bottom: 0.3rem solid var(--ui-color-text-disabled);
                    }
                }

                &.down {
                    &::after {
                        border-top: 0.3rem solid var(--ui-color-text-disabled);
                    }
                }
            }

            &:active {
                background: var(--ui-color-background);
            }

            &.up {
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 0;
                    height: 0;
                    border-left: 0.3rem solid transparent;
                    border-right: 0.3rem solid transparent;
                    border-bottom: 0.3rem solid var(--ui-color-text);
                    margin-left: -0.3rem;
                    margin-top: -0.2rem;
                }
            }

            &.down {
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 0;
                    height: 0;
                    border-left: 0.3rem solid transparent;
                    border-right: 0.3rem solid transparent;
                    border-top: 0.3rem solid var(--ui-color-text);
                    margin-left: -0.3rem;
                    margin-top: -0.2rem;
                }
            }
        }
    }

    .unit-label {
        display: block;
        position: absolute;
        right: 0.5rem;
        top: 0.1rem;
        bottom: 0.1rem;
        color: var(--ui-color-text-disabled);
        font-size: calc(var(--font-size) - 1px);
        line-height: calc(var(--height) - 2px);
        text-align: center;
        background: var(--ui-color-surface);
        border-radius: 0 0.2rem 0.2rem 0;
    }
}
