import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UINumberInputComponent } from './number-input.component';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UINumberInputComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Number',
    component: UINumberInputComponent,
    decorators: [moduleMetadata({ imports: [UINumberInputComponent, UIComponentSizeDirective] })]
};

export default meta;

type Story = StoryObj<UINumberInputComponent>;

export const Playground: Story = {
    parameters: {
        controls: {
            include: [
                'helpText',
                'label',
                'placeholder',
                'size',
                'type',
                'unitLabel',
                'value',
                'disabled'
            ]
        }
    },
    args: {
        helpText: 'Help text',
        label: 'Label',
        placeholder: 'Placeholder',
        size: 'md',
        type: 'primary',
        unitLabel: 'px',
        value: 1337
    },
    argTypes: {
        helpText: { control: 'text' },
        label: { control: 'text' },
        placeholder: { control: 'text' },
        size: { control: 'inline-radio', options: ['xs', 'sm', 'md'] },
        type: { control: 'inline-radio', options: ['primary', 'secondary'] },
        unitLabel: { control: 'text' },
        value: { control: 'number' }
    }
};

export const NUINumberInputShowcase: Story = {
    parameters: { controls: { disable: true } },
    render: () => ({
        template: `
            <div class="showcase-container">

                <div class="showcase-section">
                    <h5 [uiSize]="'lg'" class="showcase-title">Types</h5>
                    <div class="showcase-grid">
                        <div>
                            <h6 [uiSize]="'md'">Primary</h6>
                            <ui-number-input
                                [value]="50"
                                [step]="1"
                                [min]="1"
                                [max]="100"
                                unitLabel="%"
                                label="Label"
                                helpText="Help text"
                                type="primary">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">Secondary</h6>
                            <ui-number-input
                                [value]="50"
                                [step]="1"
                                [min]="1"
                                [max]="100"
                                unitLabel="%"
                                label="Label"
                                helpText="Help text"
                                type="secondary">
                            </ui-number-input>
                        </div>
                    </div>
                </div>

                <div class="showcase-section">
                    <h5 [uiSize]="'lg'" class="showcase-title">Unit Label without Arrows</h5>
                    <div class="showcase-grid">
                        <div>
                            <h6 [uiSize]="'md'">Primary</h6>
                            <ui-number-input
                                [value]="75"
                                unitLabel="%"
                                [arrowButtons]="false"
                                label="Percentage"
                                helpText="Enter a percentage value"
                                type="primary">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">Secondary</h6>
                            <ui-number-input
                                [value]="24"
                                unitLabel="kg"
                                [arrowButtons]="false"
                                label="Weight"
                                helpText="Enter weight in kilograms"
                                type="secondary">
                            </ui-number-input>
                        </div>
                    </div>
                </div>

                <div class="showcase-section">
                    <h5 [uiSize]="'lg'" class="showcase-title">Size Variants</h5>
                    <div class="showcase-grid">
                        <div>
                            <h6 [uiSize]="'md'">Medium (Default)</h6>
                            <ui-number-input
                                [value]="100"
                                unitLabel="px"
                                label="Width"
                                type="primary">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">Small</h6>
                            <ui-number-input
                                [value]="50"
                                unitLabel="px"
                                label="Margin"
                                type="primary"
                                size="sm">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">Extra Small</h6>
                            <ui-number-input
                                [value]="10"
                                unitLabel="px"
                                label="Border"
                                type="primary"
                                size="xs">
                            </ui-number-input>
                        </div>
                    </div>
                </div>

                <div class="showcase-section">
                    <h5 [uiSize]="'lg'" class="showcase-title">States</h5>
                    <div class="showcase-grid">
                        <div>
                            <h6 [uiSize]="'md'">Disabled</h6>
                            <ui-number-input
                                [value]="25"
                                unitLabel="%"
                                label="Completion"
                                helpText="Read-only value"
                                type="primary"
                                [disabled]="true">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">Invalid</h6>
                            <ui-number-input
                                [value]="150"
                                [min]="0"
                                [max]="100"
                                unitLabel="%"
                                label="Progress"
                                helpText="Value must be between 0-100"
                                type="primary"
                                [validation]="{ invalid: true }">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">With Min/Max Labels</h6>
                            <ui-number-input
                                [value]="1"
                                [step]="1"
                                [min]="1"
                                [max]="100"
                                maxLabel="Max"
                                minLabel="Min"
                                unitLabel="%"
                                label="Volume"
                                type="primary">
                            </ui-number-input>
                        </div>
                        <div>
                            <h6 [uiSize]="'md'">Empty State</h6>
                            <ui-number-input
                                placeholder="Enter amount"
                                unitLabel="€"
                                label="Price"
                                helpText="Optional field"
                                type="secondary">
                            </ui-number-input>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .showcase-container {
                    display: flex;
                    flex-direction: column;
                    gap: 24px;
                    padding: 20px;
                }

                .showcase-section {
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 20px;
                }

                h5, h6 {
                    margin-bottom: 16px;
                }

                .showcase-title {
                    margin-top: 0;
                    margin-bottom: 16px;
                }

                .showcase-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                    gap: 20px;
                }
            </style>
        `
    })
};
