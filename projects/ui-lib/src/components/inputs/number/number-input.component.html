@let invalid = validation()?.invalid && (validation()?.touched || !validation()?.pristine);

@if (isNewUI()) {
    @if (label() && type() !== 'secondary') {
        <div class="label-wrapper">
            <label
                class="label"
                [uiSize]="size() === 'xs' ? 'xs' : 'sm'"
                for="{{ id() }}-input">
                {{ label() }}
            </label>
        </div>
    }

    <div class="input-wrapper">
        <input
            class="input"
            #valueContainer
            id="{{ id() }}-input"
            type="text"
            autocomplete="off"
            [tabindex]="disabled ? -1 : tabindex() ? tabindex() : 0"
            [disabled]="disabled"
            [class.invalid]="invalid"
            [class.keyboard-focus]="hasKeyboardFocus && hasFocus"
            [placeholder]="currentPlaceholder"
            [(ngModel)]="inputFieldValue"
            [size]="autoResize() ? textSize : 20"
            (blur)="onBlur()"
            (focus)="onFocus()"
            (keydown)="onKeyDown($event)"
            (keyup.escape)="cancel.emit()" />

        @if (unitLabel() || arrowButtons()) {
            <div class="right-content">
                @if (unitLabel()) {
                    <span class="unit-label">{{ unitLabel() }}</span>
                }
                @if (arrowButtons()) {
                    <div class="arrow-buttons">
                        <ui-button
                            id="{{ id() }}-decrease"
                            class="button"
                            type="plain-secondary"
                            [size]="size()"
                            nuiSvgIcon="remove"
                            [disabled]="disabled"
                            (mousedown)="stepValue(-step(), true)"
                            (mouseup)="onMouseUp()"></ui-button>
                        <ui-button
                            id="{{ id() }}-increase"
                            class="button"
                            type="plain-secondary"
                            [size]="size()"
                            nuiSvgIcon="add"
                            [disabled]="disabled"
                            (mousedown)="stepValue(step(), true)"
                            (mouseup)="onMouseUp()"></ui-button>
                    </div>
                }
            </div>
        }
    </div>

    @if (helpText() && type() !== 'secondary') {
        <div class="help-text-wrapper">
            <ui-label
                class="help-text"
                size="xs"
                [leadingIcon]="invalid ? 'error' : undefined"
                [type]="disabled ? 'disabled' : invalid ? 'destructive' : 'secondary'">
                {{ helpText() }}
            </ui-label>
        </div>
    }
} @else {
    @if (label()) {
        <label
            class="label"
            for="{{ id() }}-input"
            >{{ label() }}</label
        >
    }
    <div class="container">
        <input
            class="input"
            #valueContainer
            id="{{ id() }}-input"
            type="text"
            autocomplete="off"
            [tabindex]="disabled ? -1 : tabindex() ? tabindex() : 0"
            [disabled]="disabled"
            [ngClass]="{
                invalid: validation()?.invalid && (validation()?.touched || !validation()?.pristine)
            }"
            [placeholder]="currentPlaceholder"
            [attr.disabled]="disabled ? '' : null"
            [(ngModel)]="inputFieldValue"
            [size]="autoResize() ? textSize : 20"
            [ngStyle]="{ 'padding-right': autoResize() ? '2px' : '7px' }"
            (blur)="onBlur()"
            (focus)="onFocus()"
            (keydown)="onKeyDown($event)"
            (keyup.escape)="cancel.emit()" />
        @if (unitLabel()) {
            <div class="unit-label">
                {{ unitLabel() }}
            </div>
        }
        @if (arrowButtons()) {
            <div class="buttons">
                <div
                    id="{{ id() }}-increase"
                    class="button up"
                    [ngClass]="{ disabled: btnUpDisabled() }"
                    (mousedown)="stepValue(step(), true)"
                    (mouseup)="onMouseUp()"></div>
                <div
                    id="{{ id() }}-decrease"
                    class="button down"
                    [ngClass]="{ disabled: btnDownDisabled() }"
                    (mousedown)="stepValue(-step(), true)"
                    (mouseup)="onMouseUp()"></div>
            </div>
        }
    </div>
}
