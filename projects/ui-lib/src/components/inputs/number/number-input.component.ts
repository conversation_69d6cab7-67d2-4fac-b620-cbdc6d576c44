import { format<PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    ElementRef,
    inject,
    input,
    OnD<PERSON>roy,
    OnInit,
    output,
    viewChild
} from '@angular/core';
import { FormsModule, UntypedFormControl } from '@angular/forms';
import { UIDebounce } from '../../../decorators';
import { UIComponentSizeDirective } from '../../../directives';
import { UIGlobalEvent } from '../../../services/global-event';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { CustomValueAccessorDirective, customValueProvider } from '../../../utils';
import { UIButtonComponent } from '../../buttons/button/button.component';
import { UILabelComponent } from '../../label';

@Component({
    imports: [
        FormsModule,
        NgClass,
        NgStyle,
        UIButtonComponent,
        UIComponentSizeDirective,
        UILabelComponent
    ],
    selector: 'ui-number-input',
    templateUrl: './number-input.component.html',
    styleUrls: ['./number-input.component.scss', './number-input.new.component.scss'],
    host: {
        '[class]': 'type()',
        '[class.input]': 'true',
        '[class.discrete]': 'discrete()',
        '[class.disabled]': 'disabled',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(document:keydown)': 'handleKeyDown($event)',
        '(document:mousedown)': 'handleMouseDown()'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [customValueProvider(UINumberInputComponent)]
})
export class UINumberInputComponent
    extends CustomValueAccessorDirective<number>
    implements OnInit, OnDestroy, AfterViewInit
{
    private changeDetectorRef = inject(ChangeDetectorRef);
    private globalEvent = inject(UIGlobalEvent);
    private uiNewThemeService = inject(UINewThemeService);

    /**
     * ID.
     */
    readonly id = input<string>(Math.random().toString(36).substring(2, 9));

    /**
     * Step amount.
     */
    readonly step = input<number>(1);
    /**
     * Step amount.
     */
    readonly min = input<number>();
    /**
     * Step amount.
     */
    readonly max = input<number>();

    /**
     * When min value show this string as placeholder
     */
    readonly minLabel = input<string>();

    /**
     * When max value show this string as placeholder
     */
    readonly maxLabel = input<string>();

    /**
     * Placeholder text.
     */
    readonly placeholder = input<string>('');

    /**
     * Label
     */
    readonly label = input<string>();

    /**
     * Unit label, shown when not hovering on the component
     * where the arrow buttons are placed
     */
    readonly unitLabel = input<string>();

    /**
     * Disable arrow button up
     */
    readonly btnUpDisabled = input<boolean>(false);

    /**
     * Disable arrow button down
     */
    readonly btnDownDisabled = input<boolean>(false);

    /**
     * Show arrow buttons to the right for
     * incrementing/decrementing number value
     */
    readonly arrowButtons = input<boolean>(true);

    /**
     * Should the number input be allowed to
     * have an empty value
     */
    readonly allowEmpty = input<boolean>(true);

    /**
     * Multiplier, the rendered value in the input box will
     * be multiplied with defined multiplier. Eg. 100 for percentages.
     */
    readonly multiplier = input<number>(1);

    /**
     * Tabindex
     */
    readonly tabindex = input<number>();

    /**
     * Autofocus automatically sets focus to the input
     * when the input component is initialized
     */
    readonly autofocus = input<boolean>(false);

    /**
     * Validation
     */
    readonly validation = input<UntypedFormControl>();

    /**
     * Type
     */
    readonly type = input<'primary' | 'secondary'>('primary');

    /**
     * Size
     */
    readonly size = input<'xs' | 'sm' | 'md'>('md');

    /**
     * Help text
     */
    readonly helpText = input<string>();

    /**
     * Number format. See https://angular.io/api/common/DecimalPipe
     */
    readonly format = input<string>();

    /**
     * Discrete mode
     */
    readonly discrete = input<boolean>(false);

    /**
     * Disable native undo
     */
    readonly disableUndo = input<boolean>(false);

    /**
     * Emit valueChange after user have stopped typing
     */
    readonly keyboardEmit = input<boolean>(false);

    /**
     * Submit event.
     */
    readonly submit = output<void>();

    /**
     * Event emitter that gets called when a mouse up event occurs on the arrow buttons.
     */
    readonly mouseUp = output<void>();

    /**
     * Cancel event.
     */
    readonly cancel = output<void>();

    /**
     * Focus event.
     */
    readonly _focus = output<void>();

    /**
     * Blur event.
     */
    readonly blur = output<void>();

    /**
     * Change event.
     */
    readonly valueChange = output<number>();

    /**
     * Undo event.
     */
    readonly undo = output<void>();

    /**
     * Redo event.
     */
    readonly redo = output<void>();

    /**
     * Select all text when input gets focus
     */
    readonly selectTextOnFocus = input<boolean>(false);

    /**
     * Handle step functionaliy. Notice, this won't fire submit event when set.
     */
    readonly onStep = input<((step: number) => void) | undefined>();

    /** autoResize if set to true size input attribute will match value length */
    readonly autoResize = input<boolean>(false);

    labelSize = computed(() => (this.size() === 'xs' ? 'xs' : 'sm'));

    textSize = 1;

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    /**
     * Reference to the input element
     */
    readonly valueContainer = viewChild.required<ElementRef>('valueContainer');

    get currentPlaceholder(): string {
        const maxLabel = this.maxLabel();
        const minLabel = this.minLabel();
        const max = this.max();
        const min = this.min();

        if (maxLabel && this.value === max) {
            return maxLabel;
        }

        if (minLabel && this.value === min) {
            return minLabel;
        }

        if (this.placeholder()) {
            return this.placeholder();
        }

        return this.allowEmpty() === false ? `${min || 0}` : '';
    }

    /**
     * Value to show in input field.
     * When using max min labels show them when reaching limits
     * And only when not having focus
     */
    get inputFieldValue(): string {
        return this.getInputFieldStringFromValue(this.value);
    }

    /**
     * Don't do anything when setting this value.
     */
    set inputFieldValue(_val: string) {}

    hasFocus = false;
    hasKeyboardFocus = false;
    lastValidValue: number | undefined;

    private __intervalRef: any;
    private __timeoutRef: any;

    ngOnInit(): void {
        this.globalEvent.on('theme-change', () => {});
    }

    ngAfterViewInit(): void {
        if (this.autofocus()) {
            setTimeout(() => {
                this.focus();
            });
        }
        this.writeValue(this.value, false);
        this.lastValidValue = this.value!;
    }

    /**
     * Destroy component
     */
    ngOnDestroy(): void {
        this.globalEvent.off('theme-change', () => {});
    }

    handleKeyDown(event: KeyboardEvent) {
        if (event.key === 'Tab') {
            this.hasKeyboardFocus = true;
        }
    }

    handleMouseDown() {
        this.hasKeyboardFocus = false;
    }

    /**
     * Step value up and down
     */
    stepValue(step: number, auto: boolean = false): void {
        const sanitizedValue = this.sanitizeValue(this.value!);
        let newValue = this.truncateValue((sanitizedValue ? sanitizedValue : 0) + step);
        this.writeValue(newValue);
        if (auto) {
            this.__timeoutRef = setTimeout(() => {
                this.__intervalRef = setInterval(() => {
                    newValue = this.truncateValue(this.value! + step);
                    this.writeValue(newValue);
                    if (this.onStep()) {
                        this.onStep()!(step);
                        return;
                    }
                    this.submit.emit();
                }, 65);
            }, 350);
            window.document.addEventListener('mouseup', this.clearStepTimeouts);
        }
        if (this.onStep()) {
            this.onStep()!(step);
            return;
        }
        this.submit.emit();
    }

    /**
     * Write value in input and emit value changes
     * @param value
     */
    writeValue(value: any, emit: boolean = true): void {
        const element = this.valueContainer().nativeElement;

        if (element) {
            const newValue = this.sanitizeValue(value);
            const newInputText = this.getInputFieldStringFromValue(newValue);
            const textChanged = newInputText !== element.value;
            const valueChanged = newValue !== this.value;

            if (valueChanged || textChanged) {
                this.lastValidValue = newValue;
                this.value = newValue;
                element.value = newInputText;
                this.resizeInput(element.value.length);
                this.changeDetectorRef.detectChanges();

                if (emit) {
                    this.onChange(newValue);
                    if (typeof newValue === 'number') {
                        this.valueChange.emit(newValue);
                    }
                }
            }
        }
    }

    /**
     * Clears the current value
     */
    clear(): void {
        this.writeValue('');
    }

    resizeInput(textLength: number): void {
        this.textSize = textLength <= 0 ? 1 : textLength;
    }

    /**
     * When user enter values with keyboard (when input is focused)
     */
    onKeyDown(event: KeyboardEvent): any {
        event.stopPropagation();
        const key = event.key;

        if (key === 'Enter') {
            const valueContainer = this.valueContainer();
            this.writeValue(valueContainer.nativeElement.value);
            this.submit.emit();
            valueContainer.nativeElement.blur();
            return;
        }

        if (key === 'ArrowUp') {
            this.stepValue(event.shiftKey ? this.step() * 10 : this.step());
            event.preventDefault();
            return;
        }

        if (key === 'ArrowDown') {
            this.stepValue(-(event.shiftKey ? this.step() * 10 : this.step()));
            event.preventDefault();
            return;
        }

        // Notify the change after delay
        if (this.keyboardEmit()) {
            this.keyboardEventChangeEmit();
        }

        if (this.autoResize()) {
            const valueLength = this.valueContainer().nativeElement.value.length;
            const textLength = key === 'Backspace' ? valueLength - 1 : valueLength;
            this.resizeInput(textLength);
        }

        const differFromEffectiveText = this.lastValidValue !== this.value;
        const defmodKey = navigator.userAgent.includes('Mac OS X') ? event.metaKey : event.ctrlKey;
        const arrowKey = key === 'ArrowLeft' || key === 'ArrowRight';
        const specialKey = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', '.', '-'].find(
            k => k === key
        );

        if (defmodKey || arrowKey || specialKey) {
            if (defmodKey && this.disableUndo()) {
                let isUndoRedo = false;
                if (key.toUpperCase() === 'Z') {
                    if (event.shiftKey) {
                        this.redo.emit();
                    } else {
                        if (differFromEffectiveText) {
                            this.value = this.lastValidValue;
                            event.preventDefault();
                            return;
                        }
                        this.undo.emit();
                    }
                    isUndoRedo = true;
                }
                if (isUndoRedo) {
                    event.preventDefault();
                    setTimeout(() => (this.lastValidValue = this.value!));
                }
            }

            return;
        }

        // Ensure that it is a number and stop the keypress
        if (isNaN(Number(key))) {
            event.preventDefault();
            return false;
        }
    }

    /**
     * Called in order to clear the timeouts created by the stepValue function
     */
    private clearStepTimeouts = () => {
        window.document.removeEventListener('mouseup', this.clearStepTimeouts);

        clearTimeout(this.__timeoutRef);
        clearInterval(this.__intervalRef);
    };

    /**
     * When focus leaves input field.
     */
    onBlur(): void {
        this.hasFocus = false;
        let value = this.valueContainer().nativeElement.value;

        // When using max/min labels the field can be empty when leaving, use latest value in those cases.
        if (
            !value &&
            ((this.maxLabel() && this.value === this.max()) ||
                (this.minLabel() && this.value === this.min()))
        ) {
            value = this.value;
        }
        this.writeValue(value);
        this.blur.emit();
    }

    /**
     * When input get focused
     */
    onFocus(): void {
        this.hasFocus = true;
        const element = this.valueContainer().nativeElement;

        // When using max/min labels the field can be empty when focusing, swap to real value in those cases.
        if (
            !element.value &&
            ((this.maxLabel() && this.value === this.max()) ||
                (this.minLabel() && this.value === this.min()))
        ) {
            element.value = this.value;
        }

        if (this.selectTextOnFocus()) {
            setTimeout(() => {
                this.valueContainer().nativeElement.select();
            });
        }
        this._focus.emit();
    }

    /**
     * Emits a mouseUp event when a mouseUp event occurs in the arrow buttons.
     */
    onMouseUp(): void {
        this.mouseUp.emit();
    }

    /**
     * Set focus to the input field
     * @param select Select the text when focusing
     */
    focus(select: boolean = false): void {
        (this.valueContainer().nativeElement as HTMLInputElement).focus();
        if (select) {
            (this.valueContainer().nativeElement as HTMLInputElement).select();
        }
    }

    /**
     * Make sure input is a number, keep value between min and max and round value.
     * @param value
     */
    sanitizeValue(value: any): number | undefined {
        let sanitizedValue;

        const isEmpty = value === '' || value === null || value === undefined;

        if (this.allowEmpty() && isEmpty) {
            return undefined;
        } else {
            // Use last valid value for empty values and invalid values such as 'a', NaN etc
            sanitizedValue = isNaN(value) ? this.lastValidValue : Number(value);
            sanitizedValue = this.truncateValue(sanitizedValue ? sanitizedValue : 0);
            sanitizedValue =
                sanitizedValue % 1 === 0 ? sanitizedValue : parseFloat(sanitizedValue.toFixed(2));
        }

        return sanitizedValue;
    }

    private getInputFieldStringFromValue(value: number | undefined): string {
        if (typeof value === 'number' && (this.maxLabel() || this.minLabel()) && !this.hasFocus) {
            const maxLabel = this.maxLabel();
            const minLabel = this.minLabel();
            const max = this.max();
            const min = this.min();
            const isGreaterThanMax = maxLabel && typeof max === 'number' && value >= max;
            const isSmallerThanMin = minLabel && typeof min === 'number' && value <= min;

            if (isGreaterThanMax || isSmallerThanMin) {
                return '';
            }
        }

        if (this.allowEmpty() && (typeof value !== 'number' || isNaN(value))) {
            return '';
        }

        return this.format()
            ? formatNumber(value || 0, 'en-gb', this.format())
            : (value || 0).toString();
    }

    /**
     * Make sure value is kept between max and min
     */
    private truncateValue(number: number): number {
        const min = this.min();
        const max = this.max();
        if (typeof min !== 'undefined' && number < min) {
            return min;
        }
        if (typeof max !== 'undefined' && number > max) {
            return max;
        }
        return number;
    }

    /**
     * Trigger change event.
     */
    @UIDebounce(300)
    private keyboardEventChangeEmit(): void {
        const val = this.valueContainer().nativeElement.value;
        if (!val && !this.allowEmpty()) {
            return;
        }
        const value = this.sanitizeValue(val);
        const min = this.min();
        if (typeof min !== 'undefined' && value === min) {
            return;
        }
        if (typeof value === 'number' && value !== this.value) {
            this.writeValue(value, true);
        }
    }
}
