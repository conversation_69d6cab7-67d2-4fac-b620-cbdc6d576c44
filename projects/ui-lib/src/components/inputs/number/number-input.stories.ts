import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { fn } from 'storybook/test';
import { UINumberInputComponent } from './number-input.component';

// Why this way: https://storybook.js.org/tutorials/intro-to-storybook/angular/en/simple-component/

// !! this.onChange is not a function Error because buggy input

const meta: Meta<UINumberInputComponent> = {
    title: 'Components/Inputs/Number',
    component: UINumberInputComponent,
    decorators: [
        moduleMetadata({
            imports: [UINumberInputComponent, CommonModule, FormsModule]
        })
    ]
};

export default meta;
type Story = StoryObj<UINumberInputComponent>;

export const Default: Story = {
    args: {
        value: 1337,
        keyboardEmit: true,
        onChange: fn(),
        onTouched: fn(),
        onStep: fn(),
        valueChange: fn()
    }
};

export const Simple: Story = {
    args: {
        step: 10,
        arrowButtons: false,
        onChange: fn(),
        onTouched: fn(),
        onStep: fn(),
        valueChange: fn()
    }
};

export const Stepper: Story = {
    args: {
        value: 100,
        step: 1,
        min: 1,
        max: 100,
        unitLabel: '%',
        allowEmpty: false,
        maxLabel: 'Max',
        minLabel: 'Min',
        label: 'inputOne with label',
        onChange: fn(),
        onTouched: fn(),
        onStep: fn(),
        valueChange: fn()
    }
};
