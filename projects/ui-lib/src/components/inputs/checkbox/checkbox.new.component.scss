:where(:root[data-uinew]) :host {
    cursor: pointer;

    .wrapper {
        display: flex;
        align-items: center;
        gap: var(--nui-label-space-gap, 8px);
    }

    .checkbox-box {
        height: var(--nui-space-500);
        width: var(--nui-space-500);
        border-radius: var(--nui-space-050);
        border: var(--nui-space-050) solid var(--nui-icon-primary);
        display: flex;
        justify-content: center;
        align-items: center;
        color: transparent;
        user-select: none;
        margin: var(--nui-space-050);

        &:active {
            transform: scale(0.98);
        }
    }

    &[data-uisize='sm'] {
        .checkbox-box {
            height: var(--nui-space-400);
            width: var(--nui-space-400);
        }

        .checkbox-label {
            font-size: var(--nui-font-size-300, 12px);
        }

        .wrapper {
            gap: var(--nui-label-space-gap-sm, 6px);
        }
    }

    .checkbox-label {
        margin-left: 0;
        color: var(--nui-text-primary);
        font-size: var(--nui-font-size-350, 14px);

        &:hover {
            color: var(--nui-text-brand);
        }
    }

    &.selected {
        .checkbox-box {
            color: var(--nui-text-primary-inverted);
            background: var(--nui-icon-primary);
            border-color: transparent;

            &:hover {
                border-color: var(--nui-border-brand-primary-boldest);
                background-color: var(--nui-icon-brand);
            }

            &:active {
                background-color: var(--nui-icon-brand);
            }
        }

        &.disabled .checkbox-box {
            background-color: var(--nui-icon-disabled);
            border-color: transparent;
            pointer-events: none;
        }
    }

    &.disabled {
        color: var(--nui-text-disabled);
        cursor: default;
        pointer-events: none;

        .checkbox-label {
            color: var(--nui-text-disabled);
        }

        .checkbox-box {
            border-color: var(--nui-border-disabled);
        }
    }

    &:not(.disabled):hover {
        .checkbox-box {
            border-color: var(--nui-border-brand-primary-boldest);
            background-color: transparent;
        }

        .checkbox-label {
            color: var(--nui-text-brand);
        }
    }

    &.selected:not(.disabled):hover {
        .checkbox-box {
            color: var(--nui-icon-primary-inverted);
            background-color: var(--nui-icon-brand);
        }
    }

    .checkbox-label:hover + .checkbox-box {
        border-color: var(--nui-border-brand-primary-boldest);
        color: var(--nui-text-brand);
    }

    .checkbox-icon {
        color: var(--nui-checkbox-item-checkbox-icon-checked, #FCFCFD);
        
        ::ng-deep .material-symbols-rounded {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateY(-50%) translateX(-50%);
            font-variation-settings: 'wght' 500;
        }
    }
}
