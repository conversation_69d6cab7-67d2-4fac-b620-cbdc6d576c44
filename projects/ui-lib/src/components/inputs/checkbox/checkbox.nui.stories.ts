import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UISVGIconComponent } from '../../icon/svg-icon/svg-icon.component';
import { UISelectableBaseDirective } from '../selectable-list';
import { UICheckboxComponent } from './checkbox.component';

const meta: Meta<UICheckboxComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Checkbox',
    component: UICheckboxComponent,
    decorators: [
        moduleMetadata({
            imports: [UICheckboxComponent, UISVGIconComponent],
            providers: [
                {
                    provide: UISelectableBaseDirective,
                    useExisting: UICheckboxComponent,
                    multi: true
                }
            ]
        })
    ],
    argTypes: {
        size: {
            control: 'inline-radio',
            options: ['sm', 'md']
        }
    }
};

export default meta;
type Story = StoryObj<UICheckboxComponent>;

export const Playground: Story = {
    parameters: {
        controls: {
            include: ['disabled', 'indeterminate', 'label', 'selected', 'size']
        }
    },
    args: {
        disabled: false,
        indeterminate: false,
        label: 'My beautiful label',
        selected: false,
        size: 'md'
    }
};

export const Checked: Story = {
    args: {
        selected: true,
        indeterminate: false
    }
};

export const Indeterminate: Story = {
    args: {
        indeterminate: true,
        selected: true
    }
};

export const DisabledSelected: Story = {
    args: {
        selected: true,
        disabled: true,
        label: 'Checkbox Text'
    }
};

export const DisabledNotSelected: Story = {
    args: {
        selected: false,
        disabled: true,
        label: 'Checkbox Text'
    }
};

export const SmallSize: Story = {
    args: {
        selected: false,
        size: 'sm'
    }
};

export const SmallSizeChecked: Story = {
    args: {
        selected: true,
        size: 'sm'
    }
};

export const SmallSizeDisabled: Story = {
    args: {
        selected: false,
        disabled: true,
        size: 'sm'
    }
};
