import { Component, HostListener, Input, computed, input } from '@angular/core';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UISVGIconComponent } from '../../icon/svg-icon/svg-icon.component';
import { UISelectableBaseDirective } from '../selectable-list/selectable.component';

@Component({
    imports: [UISVGIconComponent],
    selector: 'ui-checkbox',
    templateUrl: 'checkbox.component.html',
    styleUrls: ['checkbox.component.scss', 'checkbox.new.component.scss'],
    providers: [
        {
            provide: UISelectableBaseDirective,
            useExisting: UICheckboxComponent,
            multi: true
        }
    ],
    host: {
        class: 'checkbox',
        'class.disabled': 'disabled',
        'class.selected': 'selected',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UICheckboxComponent extends UISelectableBaseDirective {
    /**
     * Label of the checkbox
     */
    @Input() label: string;

    /**
     * Indeterminate state of the checkbox
     */
    @Input() indeterminate = false;

    /**
     * Size size of the checkbox
     */
    size = input<'md' | 'sm'>('md');

    /**
     * Maps checkbox size to icon size
     */
    iconSize = computed(() => (this.size() === 'sm' ? 'xs' : 'sm'));

    @HostListener('click') onClick(): void {
        if (this.disabled) {
            return;
        }
        if (this.indeterminate) {
            this.indeterminate = false;
            this.select();
            return;
        }
        this.toggleSelect();
    }
}
