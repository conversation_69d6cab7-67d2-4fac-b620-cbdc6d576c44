:where(:root:not([data-uinew])) :host {
    --height: 1.6rem !important;

    height: auto;
    display: inline-block;
    align-items: center;
    cursor: pointer;
    user-select: none;

    &.disabled {
        cursor: default;
        color: var(--ui-color-text-disabled);
        pointer-events: none;
    }

    &.selected {
        .checkbox-box {
            background-color: var(--ui-color-primary);
            border-color: transparent;

            &.disabled {
                border-color: var(--ui-color-border);
                background-color: var(--ui-color-border);
            }
        }

        .checkbox-icon {
            color: var(--ui-color-white);
            font-size: var(--height, 16px);
        }
    }

    .wrapper {
        align-items: center;
        display: flex;
    }

    .checkbox-box {
        display: flex;
        justify-content: center;
        align-items: center;
        height: var(--height);
        width: var(--height);
        border: 1px solid var(--ui-color-border, #ccc);
        border-radius: 2px;
        background-color: var(--ui-color-white);
        box-sizing: border-box;

        &:hover {
            border-color: var(--ui-color-grey-71);
        }

        &:active {
            transform: scale(0.98);
        }

        &.disabled {
            border-color: var(--ui-color-disabled-second);
            background-color: var(--ui-color-disabled-second);
        }
    }

    .checkbox-icon {
        transform: scale(1) !important;
    }

    .checkbox-label {
        margin-left: 10px;
    }
}
