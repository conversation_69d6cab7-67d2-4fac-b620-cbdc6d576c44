import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UISelectableBaseDirective } from '../selectable-list';
import { UICheckboxComponent } from './checkbox.component';
import { UISVGIconComponent } from '../../icon/svg-icon/svg-icon.component';

const meta: Meta<UICheckboxComponent> = {
    title: 'Components/Inputs/Checkbox',
    component: UICheckboxComponent,
    decorators: [
        moduleMetadata({
            imports: [UICheckboxComponent, UISVGIconComponent],
            providers: [
                {
                    provide: UISelectableBaseDirective,
                    useExisting: UICheckboxComponent,
                    multi: true
                }
            ]
        })
    ],
    argTypes: {
        onClick: { action: 'clicked' }
    }
};

export default meta;
type Story = StoryObj<UICheckboxComponent>;

const render: Story['render'] = args => ({
    args,
    template: `<ui-checkbox
        [selected]="selected"
        [indeterminate]="indeterminate ?? false"
        (onClick)="selected = !selected"
        [label]="label ?? 'Checkbox Text'"
        [size]="size"
        [disabled]="disabled"
    /> `
});

export const Default: Story = {
    args: {
        indeterminate: false
    },
    render
};

export const Checked: Story = {
    args: {
        selected: true,
        indeterminate: false
    }
};

export const Indeterminate: Story = {
    args: {
        indeterminate: true,
        selected: true
    }
};

export const DisabledSelected: Story = {
    args: {
        selected: true,
        disabled: true,
        label: 'Checkbox Text'
    }
};

export const DisabledNotSelected: Story = {
    args: {
        selected: false,
        disabled: true,
        label: 'Checkbox Text'
    }
};

export const SmallSize: Story = {
    args: {
        selected: false,
        size: 'sm'
    }
};

export const SmallSizeChecked: Story = {
    args: {
        selected: true,
        size: 'sm'
    }
};

export const SmallSizeDisabled: Story = {
    args: {
        selected: false,
        disabled: true,
        size: 'sm'
    }
};
