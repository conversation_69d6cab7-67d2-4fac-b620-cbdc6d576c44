:where(:root[data-uinew]) :host {
    --input-padding-horizontal: var(--nui-forms-space-padding-horizontal, 12px);
    --input-height: var(--nui-forms-height, 40px);

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &[data-uisize='md'] {
        max-width: 550px;
    }

    &[data-uisize='sm'] {
        max-width: 440px;
    }

    &[data-uisize='xs'] {
        max-width: 430px;
    }

    .input-wrapper {
        position: relative;
        width: 100%;
        height: inherit;
        display: inherit;

        input {
            width: inherit;
            font-family: var(--nui-label-regular-font-family);
            font-weight: var(--nui-label-regular-font-weight);
            font-size: var(--nui-label-regular-font-size);
            line-height: var(--nui-label-regular-line-height);
            letter-spacing: var(--nui-label-regular-letter-spacing);
        }
    }

    .clear-icon {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
        color: var(--nui-icon-primary);
        visibility: visible;

        &.hidden {
            visibility: hidden;
        }
    }

    .input:not(:focus) ~ .clear-icon:not(:active) { // hides the close icon if input is not focused but keeps it visible for click event to trigger
        visibility: hidden;
    }

    .input {
        position: relative;
        display: flex;
        height: var(--input-height);
        padding: 0 var(--input-padding-horizontal);
        padding-left: calc(
            var(--input-padding-horizontal) + var(--nui-icon-width) + var(--nui-label-space-gap)
        );
        align-items: center;
        gap: var(--nui-forms-space-gap, 8px);
        align-self: stretch;
        outline: none;
        border-radius: var(--nui-forms-radius, 4px);

        &:has(~ .clear-icon) {
            padding-right: calc(
                var(--input-padding-horizontal) + var(--nui-icon-width) + var(--nui-label-space-gap)
            );
        }

        &.keyboard-focus {
            outline: 2px solid var(--nui-border-system-focus);
            outline-offset: 2px;
        }
    }

    ui-svg-icon.input-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
        color: var(--nui-forms-icon-primary-enabled);

        &-right {
            right: var(--input-padding-horizontal);
        }

        &-left {
            left: var(--input-padding-horizontal);
        }
    }

    &.primary {
        color: var(--nui-forms-text-input-primary-enabled);

        &:hover:not(.expand) { // expand has it's own hover styles
            .input:not(:focus) {
                border-color: var(--nui-forms-border-primary-hover);
                background: var(--nui-forms-fill-primary-hover);

                ~ ui-svg-icon.input-icon {
                    color: var(--nui-forms-icon-primary-hover);
                }
            }
        }

        .input {
            color: var(--nui-forms-text-input-primary-filled); // caret color
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-primary-enabled);
            background: var(--nui-forms-fill-primary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-primary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--nui-forms-text-input-primary-filled);
                border-color: var(--nui-forms-border-primary-filled);
                background: var(--nui-forms-fill-primary-filled);

                ~ ui-svg-icon.input-icon {
                    color: var(--nui-forms-icon-primary-filled);
                }
            }

            &:focus {
                color: var(--nui-forms-text-input-primary-selected);
                border-color: var(--nui-forms-border-primary-selected);
                background: var(--nui-forms-fill-primary-selected);

                ~ ui-svg-icon.input-icon {
                    color: var(--nui-forms-icon-primary-selected);
                }
            }
        }
    }

    &.secondary {
        color: var(--nui-forms-text-input-secondary-enabled);

        &:hover:not(.expand) { // expand has it's own hover styles
            .input:not(:focus) {
                border-color: var(--nui-forms-border-secondary-hover);
                background: var(--nui-forms-fill-secondary-hover);
            }
        }

        .input {
            color: var(--nui-forms-text-input-secondary-filled); // caret color
            border: var(--nui-border-width-small, 1px) solid var(--nui-forms-border-secondary-enabled);
            background: var(--nui-forms-fill-secondary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-secondary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--nui-forms-text-input-secondary-filled);
                border-color: var(--nui-forms-border-secondary-filled);
                background: var(--nui-forms-fill-secondary-filled);

                ~ ui-svg-icon.input-icon {
                    color: var(--nui-forms-icon-primary-filled);
                }
            }

            &:focus {
                color: var(--nui-forms-text-input-secondary-selected);
                border-color: var(--nui-forms-border-secondary-selected);
                background: var(--nui-forms-fill-secondary-selected);

                ~ ui-svg-icon.input-icon {
                    color: var(--nui-forms-icon-primary-selected);
                }
            }
        }
    }

    &.expand {
        width: var(--input-height);
        transition: width 0.2s ease;

        .input {
            transition: border-radius 0.1s ease;
            padding-left: 0;

            &:has(~ .clear-icon) {
                padding-right: 0;
            }

            &:not(:focus) {
                cursor: pointer;
                background: var(--nui-button-fill-secondary);
                border-color: var(--nui-button-fill-secondary);
                border-radius: var(--nui-button-radius);

                &:hover {
                    background: var(--nui-button-fill-secondary-hover);
                    border-color: var(--nui-button-fill-secondary-hover);
                }
            }
        }

        ui-svg-icon.expand-icon {
            color: var(--nui-button-icon-primary-inverted);
            pointer-events: none;
            left: calc(var(--input-height) / 2);
            right: auto;
            transform: translate(-50%, -50%);

            &.input-icon-left {
                transition: left 0.1s ease;
            }
        }

        &:focus-within,
        &.keep-expanded {
            width: 100%;

            &.primary {
                ui-svg-icon.expand-icon {
                    color: var(--nui-forms-icon-primary-selected);
                }
            }

            &.secondary {
                ui-svg-icon.expand-icon {
                    color: var(--nui-forms-icon-secondary-selected);
                }
            }

            .input {
                padding-left: calc(
                    var(--input-padding-horizontal) + var(--nui-icon-width) + var(--nui-label-space-gap)
                );
            }

            ui-svg-icon.expand-icon {
                &.input-icon-left {
                    left: calc(var(--input-padding-horizontal) + var(--nui-icon-width) / 2);
                }
            }
        }
    }

    input[type="search"]::-webkit-search-decoration,
    input[type="search"]::-webkit-search-cancel-button,
    input[type="search"]::-webkit-search-results-button,
    input[type="search"]::-webkit-search-results-decoration {
        display: none;
    }
}
