<div class="input-wrapper">
    <input
        class="input"
        spellcheck="false"
        [class.keyboard-focus]="hasKeyboardFocus && inputFocused"
        [placeholder]="expand() ? '' : placeholder()"
        [value]="value"
        type="search"
        (input)="onInputChange($event)"
        (keydown.enter)="onSubmit()"
        (focus)="onFocus()"
        (blur)="onBlur()"
        #valueContainer />

    <ui-svg-icon
        icon="none"
        nuiIcon="search"
        [size]="computedSize()"
        [class.expand-icon]="expand()"
        class="input-icon input-icon-left">
    </ui-svg-icon>

    <ui-svg-icon
        class="input-icon input-icon-right clear-icon"
        icon="none"
        nuiIcon="close"
        [class.hidden]="!value"
        [size]="computedSize()"
        (click)="clearInput()" />
</div>
