import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UISearchInputComponent } from './search-input.component';

const meta: Meta<UISearchInputComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Search',
    component: UISearchInputComponent,
    decorators: [
        moduleMetadata({
            imports: [UISearchInputComponent]
        })
    ],
    argTypes: {
        type: {
            control: 'inline-radio',
            options: ['primary', 'secondary']
        },
        size: {
            control: 'inline-radio',
            options: ['xs', 'sm', 'md'],
            description:
                'Primary type supports only sm/md sizes. Secondary type supports only xs/sm sizes.'
        },
        placeholder: { control: 'text' },
        expand: {
            control: 'boolean',
            description: 'Placeholder is hidden for expandable search',
            table: {
                category: 'properties'
            }
        }
    },
    args: {
        type: 'primary',
        placeholder: 'Search',
        size: 'md',
        expand: false
    },
    parameters: {
        controls: {
            include: ['placeholder', 'size', 'type', 'expand']
        }
    }
};
export default meta;

type Story = StoryObj<UISearchInputComponent>;

export const Playground: Story = {
    render: args => {
        const { type, size } = args;

        let warning = '';

        if (type === 'primary' && size === 'xs') {
            warning = '⚠️ XS size not supported for Primary type - automatically converted to MD';
        } else if (type === 'secondary' && size === 'md') {
            warning = '⚠️ MD size not supported for Secondary type - automatically converted to SM';
        }

        return {
            props: { ...args, warning },
            template: `
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    ${warning ? `<div style="color: #ff6b35; font-size: 14px; font-weight: 500;">${warning}</div>` : ''}
                    <ui-search-input
                        [type]="type"
                        [size]="size"
                        [expand]="expand"
                        [placeholder]="placeholder"
                    />
                </div>
            `
        };
    }
};

export const PrimarySizes: Story = {
    name: 'Primary Type Sizes',
    render: args => ({
        props: args,
        template: `
        <div style="display: flex; flex-direction: column; gap: 16px;">
            <div>
                <h4>Primary Medium (default)</h4>
                <ui-search-input type="primary" size="md" placeholder="Primary Medium" />
            </div>
            <div>
                <h4>Primary Small</h4>
                <ui-search-input type="primary" size="sm" placeholder="Primary Small" />
            </div>
            <div>
                <h4>Primary XS (auto-converts to Small)</h4>
                <ui-search-input type="primary" size="xs" placeholder="Primary XS → Small" />
            </div>
        </div>
        `
    }),
    parameters: {
        controls: { disable: true }
    }
};

export const SecondarySizes: Story = {
    name: 'Secondary Type Sizes',
    render: args => ({
        props: args,
        template: `
        <div style="display: flex; flex-direction: column; gap: 16px;">
            <div>
                <h4>Secondary Small (default)</h4>
                <ui-search-input type="secondary" size="sm" placeholder="Secondary Small" />
            </div>
            <div>
                <h4>Secondary Extra Small</h4>
                <ui-search-input type="secondary" size="xs" placeholder="Secondary XS" />
            </div>
            <div>
                <h4>Secondary MD (auto-converts to Small)</h4>
                <ui-search-input type="secondary" size="md" placeholder="Secondary MD → Small" />
            </div>
        </div>
        `
    }),
    parameters: {
        controls: { disable: true }
    }
};

export const AllValidCombinations: Story = {
    render: args => ({
        props: args,
        template: `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
            <div>
                <h3>Primary Type (SM & MD only)</h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <ui-search-input type="primary" size="sm" placeholder="Primary Small" />
                    <ui-search-input type="primary" size="md" placeholder="Primary Medium" />
                </div>
            </div>
            <div>
                <h3>Secondary Type (XS & SM only)</h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <ui-search-input type="secondary" size="xs" placeholder="Secondary XS" />
                    <ui-search-input type="secondary" size="sm" placeholder="Secondary Small" />
                </div>
            </div>
        </div>
        `
    }),
    parameters: {
        controls: { disable: true }
    }
};

export const ExpandableSearchInput: Story = {
    parameters: {
        controls: {
            include: ['disabled', 'size', 'nuiType']
        }
    },
    args: {
        size: 'md'
    },
    render: args => ({
        props: args,
        template: `
<ui-search-input
    [nuiType]="nuiType"
    [size]="size"
    [expand]="true" />
        `
    })
};
