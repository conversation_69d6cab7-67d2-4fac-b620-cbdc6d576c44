import { Component, computed, ElementRef, input, output, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UISVGIconComponent } from '../../icon';

/**
 * This component is only meant to be used with NUI.
 */
@Component({
    imports: [FormsModule, UISVGIconComponent],
    selector: 'ui-search-input',
    templateUrl: './search-input.component.html',
    styleUrls: ['./search-input.component.scss'],
    host: {
        '[class]': 'type()',
        '[class.input]': 'true',
        '[class.expand]': 'expand()',
        '[class.keep-expanded]': 'expand() && value',
        [`[attr.data-${DATASET_SIZE}]`]: 'computedSize()',
        '(document:keydown)': 'handleKeyDown($event)',
        '(document:mousedown)': 'handleMouseDown()'
    }
})
export class UISearchInputComponent {
    placeholder = input<string>('Search');
    size = input<'xs' | 'sm' | 'md'>('md');
    type = input<'primary' | 'secondary'>('primary');
    expand = input<boolean>(false);

    submit = output<void>();
    blur = output<void>();
    valueChange = output<string>();

    protected value = '';
    protected hasKeyboardFocus = false;
    protected inputFocused = false;
    protected computedSize = computed<'xs' | 'sm' | 'md'>(() => {
        const type = this.type();
        const size = this.size();
        switch (type) {
            case 'primary':
                return size === 'sm' ? 'sm' : 'md';
            case 'secondary':
                return size === 'xs' ? 'xs' : 'sm';
            default:
                return size;
        }
    });

    private readonly valueContainer =
        viewChild.required<ElementRef<HTMLInputElement>>('valueContainer');

    private get inputElement(): HTMLInputElement {
        return this.valueContainer().nativeElement as HTMLInputElement;
    }

    onBlur(): void {
        this.inputFocused = false;
        this.blur.emit();
    }

    onFocus(): void {
        this.inputFocused = true;
    }

    onInputChange(event: Event): void {
        const value = (event.target as HTMLInputElement).value;
        this.value = value;
        this.valueChange.emit(value);
    }

    onSubmit(): void {
        this.blurInput();
        this.submit.emit();
    }

    focus(select = false): void {
        this.inputElement.focus();
        if (select) {
            this.inputElement.select();
        }
    }

    blurInput(): void {
        this.inputElement.blur();
    }

    clearInput() {
        this.value = '';
        this.valueChange.emit(this.value);
        this.valueContainer().nativeElement.focus();
    }

    protected handleKeyDown(event: KeyboardEvent): void {
        if (event.key === 'Tab') {
            this.hasKeyboardFocus = true;
        }
    }

    protected handleMouseDown(): void {
        this.hasKeyboardFocus = false;
    }
}
