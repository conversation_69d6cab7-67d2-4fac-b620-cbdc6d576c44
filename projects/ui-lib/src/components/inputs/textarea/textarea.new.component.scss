:where(:root[data-uinew]) :host {
    display: block;

    &.fit-to-container {
        display: flex;
        flex-direction: column;
        min-height: inherit;
        flex: 1;
        height: 100%;

        .label-wrapper,
        .help-text-wrapper {
            flex-shrink: 0;
        }

        .textarea-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }
    }

    .textarea-wrapper {
        position: relative;
        height: inherit;
        min-height: inherit;
    }

    .label-wrapper,
    .help-text-wrapper {
        display: flex;
        align-items: center;
        align-self: stretch;
    }

    .label-wrapper {
        padding: 0 0 var(--nui-forms-label-space-padding-bottom) var(--nui-forms-label-space-padding-left);

        .label {
            display: flex;
            font-style: normal;
            font-size: var(--nui-label-regular-font-size);
            line-height: var(--nui-label-regular-line-height);
            font-weight: var(--nui-label-regular-font-weight);
            letter-spacing: var(--nui-label-regular-letter-spacing);
        }
    }

    .help-text-wrapper {
        padding: var(--nui-forms-help-space-padding-top) 0 0 var(--nui-forms-help-space-padding-left);
    }

    .clear-icon {
        position: absolute;
        right: var(--nui-forms-space-padding-horizontal);
        top: calc((var(--nui-label-regular-line-height)
            + var(--nui-forms-space-padding-horizontal) * 2
            - var(--nui-icon-width)) / 2);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
        color: var(--nui-icon-primary);
        visibility: visible;

        &.hidden {
            visibility: hidden;
        }
    }

    .input:not(:focus) ~ .clear-icon:not(:active) { // hides the close icon if input is not focused but keeps it visible for click event to trigger
        visibility: hidden;
    }

    .textarea {
        position: relative;
        display: flex;
        outline: none;
        resize: none;
        height: inherit;
        min-height: inherit;
        font-size: var(--nui-label-regular-font-size);
        line-height: var(--nui-label-regular-line-height);
        font-weight: var(--nui-label-regular-font-weight);
        letter-spacing: var(--nui-label-regular-letter-spacing);
        box-sizing: content-box;
        width: calc(
            100% - var(--nui-forms-space-padding-horizontal) * 2 -
                var(--nui-icon-width) - var(--nui-label-space-gap)
        );
        padding: var(--nui-forms-space-padding-horizontal);
        padding-right: calc(
            var(--nui-forms-space-padding-horizontal) + var(--nui-icon-width) +
                var(--nui-label-space-gap)
        );
        border-radius: var(--nui-forms-radius);
    }

    &.primary {
        color: var(--nui-forms-text-input-primary-enabled);

        .label {
            color: var(--nui-forms-text-label-primary-enabled);
        }

        .help-text {
            color: var(--nui-forms-text-help-primary-enabled);
        }

        &:has(.textarea:not(:placeholder-shown)) {
            .label {
                color: var(--nui-forms-text-label-primary-filled);
            }
        }

        &:hover {
            .textarea:not([readonly], :focus, .invalid) {
                border-color: var(--nui-forms-border-primary-hover);
                background: var(--nui-forms-fill-primary-hover);
            }

            .label {
                color: var(--nui-forms-text-label-primary-hover);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-hover);
            }
        }

        &:has(.textarea.invalid) {
            .label {
                color: var(--nui-forms-text-label-primary-error);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-error);
            }
        }

        &:has(.textarea[readonly]) {
            .label {
                color: var(--nui-forms-text-input-primary-disabled);
            }

            .help-text {
                color: var(--nui-forms-text-help-primary-disabled);
            }

            .clear-icon {
                color: var(--nui-forms-icon-primary-disabled);
            }
        }

        .textarea {
            color: var(--nui-forms-text-input-primary-filled); // caret color
            border: var(--nui-border-width-small) solid var(--nui-forms-border-primary-enabled);
            background: var(--nui-forms-fill-primary-enabled);

            &::placeholder {
                color: var(--nui-forms-text-input-primary-enabled);
            }

            // with value
            &:not(:placeholder-shown) {
                color: var(--nui-forms-text-input-primary-filled);
                border-color: var(--nui-forms-border-primary-filled);
                background: var(--nui-forms-fill-primary-filled);
            }

            &:focus {
                color: var(--nui-forms-text-input-primary-selected);
                border-color: var(--nui-forms-border-primary-selected);
                background: var(--nui-forms-fill-primary-selected);
            }

            &.invalid {
                color: var(--nui-forms-text-input-primary-error);
                border-color: var(--nui-forms-border-primary-error);
                background: var(--nui-forms-fill-primary-error);
            }

            &[readonly] {
                color: var(--nui-forms-text-input-primary-disabled);
                border-color: var(--nui-forms-border-primary-disabled);
                background: var(--nui-forms-fill-primary-disabled);

                &::placeholder {
                    color: var(--nui-forms-text-input-primary-disabled);
                }
            }
        }
    }
}
