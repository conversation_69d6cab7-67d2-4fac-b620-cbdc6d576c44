import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UITextareaComponent } from './textarea.component';

const meta: Meta<UITextareaComponent> = {
    title: 'Components/Inputs/TextArea',
    component: UITextareaComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, FormsModule, BrowserModule, UITextareaComponent]
        })
    ]
};

export default meta;
type Story = StoryObj<UITextareaComponent>;

export const WithPlaceHolder: Story = {
    args: {
        placeholder: 'Invisible on focus',
        invisibleBorders: true,
        maxRows: 10,
        autofocus: true,
        blurOnSubmit: true
    }
};
