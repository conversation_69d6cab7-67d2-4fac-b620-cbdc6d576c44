import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    HostListener,
    Input,
    OnDestroy,
    OnInit,
    Output,
    Renderer2,
    inject,
    input,
    viewChild
} from '@angular/core';
import { AbstractControl, FormsModule, UntypedFormControl } from '@angular/forms';
import { UIComponentSizeDirective } from '../../../directives/component-size/component-size.directive';
import { UIGlobalEvent } from '../../../services/global-event';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { encodeXml } from '../../../utils';
import { UISVGIconComponent } from '../../icon';
import { UILabelComponent } from '../../label';

@Component({
    imports: [
        FormsModule,
        UISVGIconComponent,
        UILabelComponent,
        CdkTextareaAutosize,
        UIComponentSizeDirective
    ],
    selector: 'ui-textarea',
    templateUrl: './textarea.component.html',
    styleUrls: ['./textarea.component.scss', './textarea.new.component.scss'],
    host: {
        '[class.primary]': 'true',
        '[class.input]': 'true',
        '[class.textarea]': 'true',
        '[class.invisible]': 'invisibleBorders',
        '[class.fit-to-container]': '!autosize',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UITextareaComponent implements AfterViewInit, OnDestroy, OnInit {
    private uiNewThemeService = inject(UINewThemeService);
    private host = inject(ElementRef);
    private renderer = inject(Renderer2);
    private globalEvent = inject(UIGlobalEvent);

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    /**
     * Label
     */
    readonly label = input<string>();

    /**
     * Help text
     */
    readonly helpText = input<string>();

    /**
     * Size
     */
    readonly size = input<'xs' | 'sm' | 'md'>('md');

    /**
     * Validation
     */
    readonly validation = input<UntypedFormControl | AbstractControl>();

    /**
     * ID.
     */
    @Input() id?: string;

    /**
     * Placeholder text.
     */
    @Input() placeholder = '';

    /**
     * Blur text field on submit
     */
    @Input() blurOnSubmit: boolean;

    /**
     * Blur text field on submit
     */
    @Input() invisibleBorders: boolean;

    /**
     * Value.
     */
    @Input() value: string | undefined = '';

    /**
     * Maximum amount of rows allowed.
     */
    @Input() maxRows = 0;

    /**
     * Enable auto-sizing based on content. When false, textarea will inherit container height.
     */
    @Input() autosize = true;

    /**
     * Maximum amount of characters allowed.
     */
    @Input() maxCharacters: number;

    /**
     * Autofocus automatically sets focus to the input
     * when the input component is initialized
     */
    @Input() public autofocus = false;

    /**
     * Disable any input.
     */
    @Input() disabled: boolean;

    /**
     * Change event.
     */
    @Output() valueChange = new EventEmitter<string>();

    /**
     * Submit event.
     */
    @Output() submit = new EventEmitter<void>();

    /**
     * Blur event
     */
    @Output('blur') _blur = new EventEmitter<void>();
    /**
     * Focus event
     */
    @Output('focus') _focus = new EventEmitter<void>();

    /**
     * Close icon click event
     */
    @Output() closeIconClick = new EventEmitter<MouseEvent>();

    private originalValue: string;
    private readonly sizeHelper = viewChild<ElementRef>('sizeHelper');
    readonly valueContainer = viewChild.required<ElementRef>('valueContainer');
    private maxHeight: number;
    @HostListener('click') onHostClick(): void {
        this.focus();
    }

    ngOnInit(): void {
        this.originalValue = this.value ?? '';
    }

    ngAfterViewInit(): void {
        const sizeHelper = this.sizeHelper();

        // Only setup sizeHelper if it exists (old UI)
        if (sizeHelper) {
            sizeHelper.nativeElement.style.fontFamily = getComputedStyle(
                this.valueContainer().nativeElement
            ).fontFamily;
            sizeHelper.nativeElement.style.lineHeight = getComputedStyle(
                this.valueContainer().nativeElement
            ).lineHeight;
            sizeHelper.nativeElement.style.letterSpacing = getComputedStyle(
                this.valueContainer().nativeElement
            ).letterSpacing;
            sizeHelper.nativeElement.style.wordSpacing = getComputedStyle(
                this.valueContainer().nativeElement
            ).wordSpacing;
            sizeHelper.nativeElement.style.fontWeight = getComputedStyle(
                this.valueContainer().nativeElement
            ).fontWeight;
        }

        this.valueContainer().nativeElement.addEventListener('keydown', (event: KeyboardEvent) => {
            const value = (event.target as HTMLTextAreaElement).value;
            const matchedRows = value.match(/[\r\n]+/g);
            const rows = (matchedRows ? matchedRows.length : 0) + 1;
            if (event.code === 'Enter' && this.maxRows > 0 && rows >= this.maxRows) {
                event.preventDefault();
                return;
            }
        });

        this.valueContainer().nativeElement.addEventListener('keyup', (event: KeyboardEvent) => {
            if (event.code === 'Enter') {
                if (this.blurOnSubmit) {
                    this.blur();
                }
                this.submit.emit();
            }
        });

        this.globalEvent.on('theme-change', this.syncSize);

        setTimeout(() => {
            const hostStyle = getComputedStyle(this.host.nativeElement);
            const maxHeight = hostStyle.maxHeight!;

            const sizeHelperValue = this.sizeHelper();
            const valueContainer = this.valueContainer();
            if (maxHeight !== 'none') {
                if (sizeHelperValue) {
                    sizeHelperValue.nativeElement.style.maxHeight = maxHeight;

                    valueContainer.nativeElement.style.maxHeight = maxHeight;
                    this.maxHeight = parseInt(maxHeight, 10);
                }
            }

            const minHeight = hostStyle.minHeight;

            if (minHeight !== 'none') {
                if (sizeHelperValue) {
                    sizeHelperValue.nativeElement.style.minHeight = minHeight;
                    valueContainer.nativeElement.style.minHeight = minHeight;
                }
            }

            this.syncSize(this.value);

            if (this.autofocus) {
                this.focus();
            }
        });
    }

    public focus(): void {
        this.valueContainer().nativeElement.focus();
    }

    public blur(): void {
        this.valueContainer().nativeElement.blur();
    }

    onFocus(): void {
        const focusedClass = 'focused';

        this.renderer.addClass(this.host.nativeElement, focusedClass);
        this._focus.emit();
    }

    onBlur(): void {
        const focusedClass = 'focused';

        this.renderer.removeClass(this.host.nativeElement, focusedClass);
        this._blur.emit();
    }

    syncSize = (value: any) => {
        if (this.maxCharacters && value.length > this.maxCharacters) {
            this.valueContainer().nativeElement.value = this.value;
            return;
        }
        this.value = value;

        const sizeHelper = this.sizeHelper();

        // Only do manual sizing if sizeHelper exists (old UI)
        if (sizeHelper) {
            sizeHelper.nativeElement.innerHTML = this.value
                ? encodeXml(this.value)
                      .replace(/\n/g, '<br>&#8203;')
                      .replace(/\r\n/g, '<br>&#8203;')
                      .replace(/&#8203;(.+?)/g, '$1')
                : '&#8203;';
            const height = sizeHelper.nativeElement.getBoundingClientRect().height;
            const valueContainer = this.valueContainer();

            if (height >= this.maxHeight) {
                valueContainer.nativeElement.style.overflowY = 'scroll';
            } else {
                valueContainer.nativeElement.style.overflowY = 'hidden';
            }
            valueContainer.nativeElement.style.height = `${height}px`;
        }

        this.valueChange.emit(this.value);
    };

    clearInput(event: MouseEvent) {
        this.value = this.originalValue;
        this.valueChange.emit(this.originalValue);
        this.closeIconClick.emit(event);
        this.valueContainer().nativeElement.focus();
    }

    ngOnDestroy(): void {
        this.globalEvent.off('theme-change', this.syncSize);
    }
}
