@use '../../../style/mixins';

:where(:root:not([data-uinew])) :host {
    position: relative;
    display: block;
    overflow: hidden;
    border: 1px solid var(--ui-color-border);
    border-radius: 2px;

    &.focused {
        border: 1px solid var(--ui-color-focus);
    }

    &.invisible-focused {
        border: 1px solid transparent;
    }

    &.invisible {
        border: 1px solid transparent;
    }

    .textarea {
        resize: none;
        border: none;
        outline: none;
        font-size: var(--font-size);
        padding: var(--padding);
        color: var(--text-color);
        background-color: var(--ui-color-surface);
        word-wrap: break-word;
        font-family: inherit;
        border-radius: 2px;
        white-space: pre-wrap;
        overflow-y: hidden;
        line-height: var(--line-height);
        min-height: inherit;

        @include mixins.ui-placeholder {
            color: var(--ui-color-text-second);
        }

        &:disabled {
            background-color: var(--ui-color-disabled);
            color: var(--ui-color-text-disabled);
        }
    }

    .value-container {
        position: absolute;
        top: 0;
        width: 100%;
    }
}
