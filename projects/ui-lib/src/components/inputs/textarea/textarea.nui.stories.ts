import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UITextareaComponent } from './textarea.component';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UITextareaComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Inputs/TextArea',
    component: UITextareaComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, BrowserModule, UITextareaComponent]
        })
    ],
    parameters: {
        controls: {
            include: [
                'label',
                'helpText',
                'placeholder',
                'maxRows',
                'disabled',
                'value',
                'size',
                'autosize'
            ]
        }
    },
    argTypes: {
        label: {
            control: 'text'
        },
        helpText: {
            control: 'text'
        },
        placeholder: {
            control: 'text'
        },
        maxRows: {
            control: 'number'
        },
        disabled: {
            control: 'boolean'
        },
        value: {
            control: 'text'
        },
        size: {
            control: 'inline-radio',
            options: ['md', 'sm', 'xs']
        },
        autosize: {
            control: 'boolean'
        }
    }
};

export default meta;
type Story = StoryObj<UITextareaComponent>;

export const Default: Story = {
    render: args => ({
        props: args,
        template: `
            <div  style="width: 30%;">
                <ui-textarea
                    [label]="label"
                    [helpText]="helpText"
                    [placeholder]="placeholder"
                    [maxRows]="maxRows"
                    [disabled]="disabled"
                    [value]="value"
                    [size]="size"
                    [autosize]="autosize">
                </ui-textarea>
            </div>
        `
    }),
    args: {
        label: 'Label',
        helpText: 'Help text',
        placeholder: 'Placeholder',
        disabled: false,
        value: 'This textarea will grow as you add more content.',
        autosize: true
    }
};

export const FixedAndAutoHeight: Story = {
    render: args => ({
        props: args,
        template: `
            <div style="display: flex; gap: 20px;">
                <div style="width: 300px; height: 200px; border: 2px solid #007ACC; padding: 10px; display: flex; flex-direction: column;">
                    <h3 style="margin: 0 0 10px 0; font-size: 14px; flex-shrink: 0;">Fixed Height (autosize=false)</h3>
                    <ui-textarea
                        [label]="'Label'"
                        [helpText]="'Help text'"
                        [placeholder]="'Type a long message to see the scrolling...'"
                        [autosize]="false"
                        [value]="'This is a long text that demonstrates how the textarea behaves when autosize is disabled. The textarea will maintain the container height and show scrollbars when needed.'">
                    </ui-textarea>
                </div>
                <div style="width: 300px; height: 200px; border: 2px solid #28A745; padding: 10px;">
                    <h3 style="margin: 0 0 10px 0; font-size: 14px;">Auto Height (autosize=true)</h3>
                    <ui-textarea
                        [label]="'Label'"
                        [helpText]="'Textarea will grow with content'"
                        [placeholder]="'Type to see auto-sizing...'"
                        [autosize]="true"
                        [value]="'This textarea will grow as you add more content.'">
                    </ui-textarea>
                </div>
            </div>
        `
    }),
    args: {
        autosize: false
    }
};
