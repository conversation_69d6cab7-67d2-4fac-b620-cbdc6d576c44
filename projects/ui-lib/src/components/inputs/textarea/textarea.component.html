@if (isNewUI()) {
    @let invalid = validation()?.invalid && (validation()?.touched || !validation()?.pristine);

    @if (label()) {
        <div class="label-wrapper">
            <label
                class="label"
                [uiSize]="size() === 'xs' ? 'xs' : 'sm'"
                for="{{ id }}-textarea">
                {{ label() }}
            </label>
        </div>
    }

    <div class="textarea-wrapper">
        <textarea
            #valueContainer
            [cdkTextareaAutosize]="autosize"
            [uiSize]="size()"
            [cdkAutosizeMaxRows]="maxRows"
            id="{{ id }}-textarea"
            class="input textarea value-container"
            [class.invalid]="invalid"
            [ngModel]="value"
            (ngModelChange)="syncSize($event)"
            (focus)="onFocus()"
            (blur)="onBlur()"
            [placeholder]="placeholder"
            [readonly]="disabled"
            [disabled]="!!disabled"></textarea>

        <ui-svg-icon
            class="clear-icon"
            [icon]="'close'"
            [class.hidden]="!value || disabled"
            [size]="size()"
            (click)="clearInput($event)" />
    </div>

    @if (helpText()) {
        <div class="help-text-wrapper">
            <ui-label
                class="help-text"
                [leadingIcon]="invalid ? 'error' : undefined"
                size="xs"
                [type]="disabled ? 'disabled' : invalid ? 'destructive' : 'secondary'">
                {{ helpText() }}
            </ui-label>
        </div>
    }
} @else {
    <div
        #sizeHelper
        class="input textarea"></div>
    <textarea
        #valueContainer
        id="{{ id }}-textarea"
        class="input textarea value-container"
        [ngModel]="value"
        (ngModelChange)="syncSize($event)"
        (focus)="onFocus()"
        (blur)="onBlur()"
        [placeholder]="placeholder"
        [disabled]="!!disabled"></textarea>
}
