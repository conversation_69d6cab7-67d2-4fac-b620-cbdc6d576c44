import { Component, EventEmitter, Input, Output } from '@angular/core';
import { fn } from 'storybook/test';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import dayjs from 'dayjs';
import { UIIconComponent } from '../../icon';
import { UIDatePickerComponent } from './date-picker.component';

// Fixed dates are used to prevent <PERSON><PERSON> from detecting UI changes on every PR
// When using dynamic dates (like new Date()), <PERSON><PERSON> would flag the date-picker
// as changed because the displayed dates would be different in each build
const FIXED_DATE = dayjs('2024-01-15').toDate();
const FIXED_START_DATE = dayjs('2024-01-15').toDate();
const FIXED_END_DATE = dayjs('2024-01-25').toDate();
const FIXED_MIN_DATE = dayjs('2024-01-01').toDate();
const FIXED_MAX_DATE = dayjs('2024-01-31').toDate();

@Component({
    imports: [UIDatePickerComponent],
    selector: 'storybook-date-picker-container',
    template: `
        <div
            [style.width]="isCompact ? '25.4rem' : '40rem'"
            style="margin: 0 auto;">
            <ui-date-picker
                [date]="date"
                [startDate]="startDate"
                [endDate]="endDate"
                [hourPicker]="hourPicker"
                [dateSpanPicker]="dateSpanPicker"
                [isCompact]="isCompact"
                [outerBorders]="outerBorders"
                [firstWeekdaySunday]="firstWeekdaySunday"
                [maxDate]="isMaxDate ? maxDate : undefined"
                [minDate]="minDate"
                (dateChange)="handleDateChange($event)"
                (startDateChange)="handleStartDateChange($event)"
                (endDateChange)="handleEndDateChange($event)"></ui-date-picker>
        </div>
    `
})
class DatePickerStoryContainerComponent {
    @Input() date: Date = FIXED_DATE;
    @Input() startDate: Date = FIXED_START_DATE;
    @Input() endDate: Date = FIXED_END_DATE;
    @Input() maxDate: Date = FIXED_MAX_DATE;
    @Input() minDate: Date = FIXED_MIN_DATE;
    @Input() hourPicker = false;
    @Input() dateSpanPicker = false;
    @Input() isCompact = false;
    @Input() outerBorders = true;
    @Input() firstWeekdaySunday = false;
    @Input() isMaxDate = true;

    @Output() dateChange = new EventEmitter<Date>();
    @Output() startDateChange = new EventEmitter<Date>();
    @Output() endDateChange = new EventEmitter<Date>();

    handleDateChange(newDate: Date) {
        fn()(newDate);
        this.date = newDate;
        this.dateChange.emit(newDate);
    }

    handleStartDateChange(newStartDate: Date) {
        fn()(newStartDate);
        this.startDate = newStartDate;
        this.startDateChange.emit(newStartDate);
    }

    handleEndDateChange(newEndDate: Date) {
        fn()(newEndDate);
        this.endDate = newEndDate;
        this.endDateChange.emit(newEndDate);
    }
}

const meta: Meta<DatePickerStoryContainerComponent> = {
    title: 'Components/Inputs/DatePicker',
    component: DatePickerStoryContainerComponent,
    decorators: [
        moduleMetadata({
            imports: [UIDatePickerComponent, UIIconComponent, DatePickerStoryContainerComponent]
        })
    ],
    argTypes: {
        date: { control: 'date' },
        startDate: { control: 'date' },
        endDate: { control: 'date' },
        hourPicker: { control: 'boolean' },
        dateSpanPicker: { control: 'boolean' },
        isCompact: { control: 'boolean' },
        outerBorders: { control: 'boolean' },
        firstWeekdaySunday: { control: 'boolean' },
        maxDate: { control: 'date' },
        minDate: { control: 'date' },
        isMaxDate: { control: 'boolean' }
    }
};
export default meta;
type Story = StoryObj<DatePickerStoryContainerComponent>;

const generateUsageExample = (args: Partial<DatePickerStoryContainerComponent>) => {
    const props = Object.entries(args)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => {
            if (typeof value === 'boolean' || typeof value === 'number') {
                return `[${key}]="${value}"`;
            } else if (value instanceof Date) {
                return `[${key}]="'${value.toISOString()}'"`;
            } else {
                return `[${key}]="${JSON.stringify(value)}"`;
            }
        })
        .join('\n    ');

    return `
<ui-date-picker
    ${props}
    (dateChange)="onDateChange($event)"
    (startDateChange)="onStartDateChange($event)"
    (endDateChange)="onEndDateChange($event)">
</ui-date-picker>
`;
};

export const BasicDatePicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        date: FIXED_DATE,
        minDate: FIXED_MIN_DATE,
        maxDate: FIXED_MAX_DATE,
        outerBorders: true,
        isMaxDate: true
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    date: FIXED_DATE,
                    minDate: FIXED_MIN_DATE,
                    maxDate: FIXED_MAX_DATE,
                    outerBorders: true
                })
            }
        }
    }
};

export const HourPicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        hourPicker: true,
        date: FIXED_START_DATE,
        minDate: FIXED_MIN_DATE,
        maxDate: FIXED_MAX_DATE
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    hourPicker: true,
                    date: FIXED_START_DATE,
                    minDate: FIXED_MIN_DATE,
                    maxDate: FIXED_MAX_DATE
                })
            }
        }
    }
};

export const PeriodPicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        dateSpanPicker: true,
        startDate: FIXED_START_DATE,
        endDate: FIXED_END_DATE,
        outerBorders: true,
        firstWeekdaySunday: true,
        maxDate: FIXED_MAX_DATE
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    dateSpanPicker: true,
                    startDate: FIXED_START_DATE,
                    endDate: FIXED_END_DATE,
                    outerBorders: true,
                    firstWeekdaySunday: true,
                    maxDate: FIXED_MAX_DATE
                })
            }
        }
    }
};

export const PreselectedDatePicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        dateSpanPicker: true,
        startDate: FIXED_START_DATE,
        endDate: FIXED_END_DATE
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    dateSpanPicker: true,
                    startDate: FIXED_START_DATE,
                    endDate: FIXED_END_DATE
                })
            }
        }
    }
};

export const CompactDatePicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        isCompact: true,
        date: FIXED_DATE,
        minDate: FIXED_MIN_DATE
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    isCompact: true,
                    date: FIXED_DATE,
                    minDate: FIXED_MIN_DATE
                })
            }
        }
    }
};

export const CompactPeriodPicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        isCompact: true,
        dateSpanPicker: true,
        startDate: FIXED_START_DATE,
        endDate: FIXED_END_DATE,
        firstWeekdaySunday: true,
        maxDate: FIXED_MAX_DATE
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    isCompact: true,
                    dateSpanPicker: true,
                    startDate: FIXED_START_DATE,
                    endDate: FIXED_END_DATE,
                    firstWeekdaySunday: true,
                    maxDate: FIXED_MAX_DATE
                })
            }
        }
    }
};
