@use 'sass:math';
@use '../../../style/mixins';

:where(:root:not([data-uinew])) :host {
    $itemHeight: 5rem;
    $itemInnerHeight: 4.4rem;
    $itemHeightCompact: 3.6rem;
    $itemInnerHeightCompact: 2.6rem;

    position: relative;
    display: block;
    border: 0.1rem solid transparent;
    background-color: var(--ui-color-surface);
    border-radius: 0.2rem;
    -webkit-touch-callout: none;
    user-select: none;
    text-align: center;

    @keyframes bfDatePicker__selectedDay {
        0% {
            transform: scale(0.8);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes bfDatePicker__selectedDayBetween {
        0% {
            background: #fff;
        }

        100% {
            background: var(--ui-color-primary);
        }
    }

    .date-picker-bordered::after {
        border: 0.1rem solid var(--ui-color-border);
        border-radius: 0.2rem;
        content: '';
        position: absolute;
        left: -1px;
        top: -1px;
        width: 100%;
        height: 100%;
        z-index: 3;
        pointer-events: none;
    }

    .date-picker-unbordered::after {
        content: '';
        position: absolute;
        left: -1px;
        top: -1px;
        width: 100%;
        height: 100%;
        z-index: 3;
        pointer-events: none;
    }

    .current-month {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
        text-transform: uppercase;
        border-bottom: 0.1rem solid var(--ui-color-border);
        line-height: 4rem;

        .step-month {
            flex: 0 0;
            min-width: 4rem;
            height: 4rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 22px;
            opacity: 0.6;
            transition: opacity 0.2s ease;

            &:hover {
                opacity: 1;
            }

            &.disabled {
                opacity: 0.3;
                cursor: not-allowed;
                pointer-events: none;
            }
        }

        .month-name {
            cursor: pointer;
            flex: 1 1;
            border-left: 0.1rem solid var(--ui-color-border);
            border-right: 0.1rem solid var(--ui-color-border);
        }
    }

    .day-names {
        border-bottom: 0.1rem solid var(--ui-color-border);
        z-index: 2;
        position: relative;

        .day {
            display: inline-block;
            line-height: 4rem;
            width: calc(100% / 7);
        }
    }

    .calendar {
        width: 100%;
        display: grid;
        grid-gap: 1px;
        grid-template-columns: repeat(auto-fill, minmax(5rem, 1fr));

        .item {
            height: $itemHeight;
            border: 0.1rem solid var(--ui-color-border);
            margin: -0.1rem;
            position: relative;
            cursor: pointer;
            color: #999;

            .inner {
                position: absolute;
                inset: math.div($itemHeight - $itemInnerHeight, 2) -1px
                    math.div($itemHeight - $itemInnerHeight, 2) -1px;
                line-height: $itemInnerHeight - 0.2rem;
                border: 0.1rem solid transparent;
                background-color: transparent;

                @include mixins.ui-ellipsis;
            }

            &.selected-between {
                border-right-color: transparent;
                border-left-color: transparent;

                &:first-child {
                    border-left: none;
                }

                &:last-child {
                    border-right: none;
                }

                .inner {
                    background-color: var(--ui-color-selected-background);
                    border: solid 1px var(--ui-color-selected-border);
                    color: var(--ui-color-primary);
                }
            }

            &.selected-first,
            &.selected-last {
                z-index: 1;

                .inner {
                    background-color: var(--ui-color-primary);
                    color: var(--ui-color-white);
                }

                //animation: bfDatePicker__selectedDay 0.15s cubic-bezier(0.190, 1.000, 0.220, 1.000);
            }

            &.selected-first {
                border-right-color: transparent;

                .inner {
                    border-top-left-radius: 2px;
                    border-bottom-left-radius: 2px;
                    padding-right: calc(($itemHeight - $itemInnerHeight) / 2);
                    left: calc(($itemHeight - $itemInnerHeight) / 2);
                }
            }

            &.selected-last {
                border-left-color: transparent;

                .inner {
                    border-top-right-radius: 2px;
                    border-bottom-right-radius: 2px;
                    padding-left: math.div($itemHeight - $itemInnerHeight, 2);
                    right: math.div($itemHeight - $itemInnerHeight, 2);
                }
            }

            &.disabled {
                background-color: var(--ui-color-disabled);
                pointer-events: none;

                .inner {
                    opacity: 0.3;
                }
            }

            &.out-of-month {
                background-color: var(--ui-color-disabled);

                .inner {
                    opacity: 0.3;
                }
            }

            &.today {
                color: var(--ui-color-primary);

                ::before {
                    content: '';
                    position: absolute;
                    right: 0.5rem;
                    top: 0.5rem;
                    width: 0.6rem;
                    height: 0.6rem;
                    z-index: 1;
                    background: var(--ui-color-primary);
                    border-radius: 100%;
                }

                &.selected-first,
                &.selected-last {
                    ::before {
                        background-color: var(--ui-color-white);
                    }
                }
            }
        }

        &.date {
            // 7 columns...
            grid-template-columns: auto auto auto auto auto auto auto;

            .item {
                &:nth-child(7n + 1) {
                    border-left-color: transparent;
                }

                &:nth-child(7n) {
                    border-right-color: transparent;
                }
            }
        }
    }

    // Compact view
    .date-picker-compact {
        .current-month {
            border-bottom: 0;

            .step-month {
                font-size: 1.4rem;
                color: var(--ui-color-primary);
                opacity: 1;

                &.disabled {
                    color: var(--ui-color-text-discrete);
                    opacity: 0.5;
                }
            }

            .month-name {
                border-left: none;
                border-right: none;
                font-size: 1.1rem;
                font-weight: 700;
                color: var(--ui-color-primary);
            }
        }

        .day-names {
            border-bottom: none;

            .day {
                font-size: 1.1rem;
                line-height: 3rem;
            }
        }

        .calendar {
            grid-gap: 0;

            .item {
                height: $itemHeightCompact;
                border: none;
                margin: 0;
                color: var(--ui-color-text);

                .inner {
                    inset: calc(($itemHeightCompact - $itemInnerHeightCompact) / 2) 0;
                    line-height: $itemInnerHeightCompact;
                    border: none;

                    &::before {
                        content: none;
                    }
                }

                .inner-content {
                    position: relative;
                    display: block;
                    height: 100%;

                    &::before {
                        content: '';
                        position: absolute;
                        width: 0.6rem;
                        height: 0.6rem;
                        border-radius: 100%;
                        right: calc(($itemHeightCompact - $itemInnerHeightCompact) / 2);
                        top: 0;
                    }
                }

                &.selected-between,
                &.selected-first,
                &.selected-last {
                    .inner-content::before {
                        inset: auto auto 0.2rem 50%;
                        transform: translateX(-50%);
                        width: 0.3rem;
                        height: 0.3rem;
                    }
                }

                &.selected-between {
                    .inner {
                        border: none;
                        font-weight: 700;
                        color: var(--ui-color-text);
                    }
                }

                &.selected-first,
                &.selected-last {
                    .inner {
                        background-color: var(--ui-color-selected-background);
                        font-weight: 700;
                    }

                    .inner-content {
                        background-color: var(--ui-color-primary);
                    }
                }

                &.selected-first.selected-last {
                    .inner {
                        background-color: transparent;
                        padding: 0;
                    }
                }

                &.selected-first {
                    .inner {
                        border-top-left-radius: 0.4rem;
                        border-bottom-left-radius: 0.4rem;
                        padding-right: calc(($itemHeightCompact - $itemInnerHeightCompact) / 2);
                        left: calc(($itemHeightCompact - $itemInnerHeightCompact) / 2);
                    }
                }

                &.selected-last {
                    .inner {
                        border-top-right-radius: 0.4rem;
                        border-bottom-right-radius: 0.4rem;
                        padding-left: calc(($itemHeightCompact - $itemInnerHeightCompact) / 2);
                        right: calc(($itemHeightCompact - $itemInnerHeightCompact) / 2);
                    }
                }

                &.disabled,
                &.out-of-month {
                    background-color: transparent;
                    color: var(--ui-color-text-discrete);

                    .inner {
                        opacity: 1;
                    }
                }

                &.today {
                    font-weight: 700;
                    color: var(--ui-color-primary);
                }
            }
        }
    }
}
