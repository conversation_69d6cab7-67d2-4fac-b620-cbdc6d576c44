import { NgClass } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    inject,
    input,
    linkedSignal,
    output,
    signal
} from '@angular/core';
import dayjs, { type Dayjs, type ConfigType } from 'dayjs/esm';
import customParseFormat from 'dayjs/esm/plugin/customParseFormat';
import isBetween from 'dayjs/esm/plugin/isBetween';
import utc from 'dayjs/esm/plugin/utc';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';

@Component({
    selector: 'ui-date-picker',
    imports: [NgClass, UIIconComponent, UISVGIconComponent],
    templateUrl: './date-picker.component.html',
    styleUrls: ['./date-picker.component.scss', './date-picker.new.component.scss'],
    host: {
        '[class.date-picker]': 'true',
        [`[attr.data-${DATASET_SIZE}]`]: '"sm"'
    },

    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UIDatePickerComponent {
    private uiNewThemeService = inject(UINewThemeService);
    /**
     * The selected date for single date picker mode.
     *
     * @description Used when `dateSpanPicker` is false. Acts as a semantic alias for `startDate`.
     * When both `date` and `startDate` are provided, `startDate` takes precedence.
     *
     * @example
     * ```html
     * <ui-date-picker [date]="selectedDate" (dateChange)="onDateSelected($event)"></ui-date-picker>
     * ```
     */
    readonly date = input<Date>();

    /**
     * The start date for date range selection.
     *
     * @description Used in both single date and date range picker modes. In single date mode,
     * this represents the selected date. In range mode, it represents the beginning of the range.
     *
     * @example
     * ```html
     * <ui-date-picker
     *   [startDate]="rangeStart"
     *   [endDate]="rangeEnd"
     *   [dateSpanPicker]="true"
     *   (startDateChange)="onStartDateChange($event)">
     * </ui-date-picker>
     * ```
     */
    readonly startDate = input<Date>();

    /**
     * The end date for date range selection.
     *
     * @description Only used when `dateSpanPicker` is true. Represents the end of the selected date range.
     * Must be greater than or equal to `startDate` when both are set.
     *
     * @example
     * ```html
     * <ui-date-picker
     *   [dateSpanPicker]="true"
     *   [endDate]="rangeEnd"
     *   (endDateChange)="onEndDateChange($event)">
     * </ui-date-picker>
     * ```
     */
    readonly endDate = input<Date>();

    /**
     * Enables hour selection.
     *
     * @description When enabled, clicking a date will open an hour picker allowing users to select
     * a specific hour (0-23) for the chosen date. Only available in single date mode.
     *
     * @default false
     *
     * @example
     * ```html
     * <ui-date-picker [hourPicker]="true" (dateChange)="onDateTimeSelected($event)"></ui-date-picker>
     * ```
     *
     */
    readonly hourPicker = input<boolean>(false);

    /**
     * Enables or disables the year picker functionality.
     *
     * @description When enabled, users can click on the month/year header to open a year selection grid.
     * When disabled, the header becomes non-interactive.
     *
     * @default true
     *
     * @example
     * ```html
     * <ui-date-picker [yearPicker]="false"></ui-date-picker>
     * ```
     */
    readonly yearPicker = input<boolean>(true);

    /**
     * Enables date range selection mode.
     *
     * @description When enabled, users can select a date range by clicking two dates.
     * The first click selects the start date, the second click selects the end date.
     * Hovering shows a preview of the potential range.
     *
     * @default false
     *
     * @example
     * ```html
     * <ui-date-picker
     *   [dateSpanPicker]="true"
     *   (startDateChange)="onRangeStart($event)"
     *   (endDateChange)="onRangeEnd($event)">
     * </ui-date-picker>
     * ```
     *
     */
    readonly dateSpanPicker = input<boolean>(false);

    /**
     * The maximum selectable date.
     *
     * @description Dates after this date will be disabled and unselectable.
     * Users cannot navigate to months beyond this date's month/year.
     *
     * @example
     * ```html
     * <ui-date-picker [maxDate]="new Date('2024-12-31')"></ui-date-picker>
     * ```
     *
     */
    readonly maxDate = input<Date>();

    /**
     * The minimum selectable date.
     *
     * @description Dates before this date will be disabled and unselectable.
     * Users cannot navigate to months before this date's month/year.
     *
     * @example
     * ```html
     * <ui-date-picker [minDate]="new Date('2024-01-01')"></ui-date-picker>
     * ```
     *
     */
    readonly minDate = input<Date>();

    /**
     * Sets Sunday as the first day of the week.
     *
     * @description When true, weeks start with Sunday. When false or undefined,
     * weeks start with Monday following ISO 8601 standard.
     *
     * @default false
     *
     * @example
     * ```html
     * <ui-date-picker [firstWeekdaySunday]="true"></ui-date-picker>
     * ```
     *
     */
    readonly firstWeekdaySunday = input<boolean>(false);

    /**
     * Controls the visibility of outer borders.
     *
     * @description When true, displays a border around the entire date picker.
     * When false, renders without outer borders for seamless integration.
     *
     * @default true
     *
     * @example
     * ```html
     * <ui-date-picker [outerBorders]="false"></ui-date-picker>
     * ```
     *
     */
    readonly outerBorders = input<boolean>(true);

    /**
     * Enables compact display mode.
     *
     * @description When true, renders a smaller, more condensed version of the date picker
     * suitable for space-constrained layouts. Changes styling and reduces padding/margins.
     *
     * @default false
     *
     * @example
     * ```html
     * <ui-date-picker [isCompact]="true"></ui-date-picker>
     * ```
     *
     */
    readonly isCompact = input<boolean>(false);

    /**
     * Emitted when a single date is selected.
     *
     * @description Fires when a date is selected in single date mode. The emitted date
     * includes time information if hour picker is enabled, otherwise defaults to start of day.
     *
     * @param date The selected date, or undefined if cleared
     *
     * @example
     * ```html
     * <ui-date-picker (dateChange)="onDateSelected($event)"></ui-date-picker>
     * ```
     *
     */
    readonly dateChange = output<Date | undefined>();

    /**
     * Emitted when the start date of a range is selected or changed.
     *
     * @description Fires when the start date changes in either single date or date range mode.
     * In single date mode, this fires alongside `dateChange`. In range mode, this fires
     * when the first date in a range is selected.
     *
     * @param startDate The selected start date, or undefined if cleared
     *
     * @example
     * ```html
     * <ui-date-picker
     *   [dateSpanPicker]="true"
     *   (startDateChange)="onRangeStartChange($event)">
     * </ui-date-picker>
     * ```
     *
     */
    readonly startDateChange = output<Date | undefined>();

    /**
     * Emitted when the end date of a range is selected or changed.
     *
     * @description Only fires in date range picker mode. Emits when the second date
     * in a range selection is chosen, completing the range selection.
     *
     * @param endDate The selected end date, or undefined if cleared
     *
     * @example
     * ```html
     * <ui-date-picker
     *   [dateSpanPicker]="true"
     *   (endDateChange)="onRangeEndChange($event)">
     * </ui-date-picker>
     * ```
     *
     */
    readonly endDateChange = output<Date | undefined>();

    readonly currentDate = linkedSignal<Dayjs>(() => {
        const date = this.date();
        const startDate = this.startDate();

        // Date is a semantic alias for startDate
        const d = startDate || date;
        if (d) {
            return dayjs(d);
        }
        return dayjs();
    });

    readonly years = computed<number[]>(() => {
        const minDate = this.minDate();
        const maxDate = this.maxDate();

        let date = (!minDate ? undefined : dayjs(minDate)) || dayjs().year(dayjs().year() - 15);
        const toDate = (!maxDate ? undefined : dayjs(maxDate)) || dayjs().year(dayjs().year() + 27);
        const yearsCount = toDate.year() - date.year() + 1;

        const yearsArray = [];
        for (let i = 0; i < yearsCount; i++) {
            yearsArray.push(date.year());
            date = date.add(1, 'year');
        }

        return yearsArray;
    });
    readonly days = computed<UICalendarDate[]>(() => {
        const isHoverPreview = !!this.firstSelectedDate() || !!this.hoverDateSpan();
        return this.generateCalendar(isHoverPreview);
    });
    readonly hours = signal<number[]>(new Array(24).fill(0).map((_, i) => i));
    readonly showYearPicker = signal<boolean>(false);
    readonly showHourPicker = signal<boolean>(false);
    readonly hoverDateSpan = signal<Date[] | undefined>(undefined);
    readonly tempDate = signal<Dayjs | undefined>(undefined);
    readonly firstSelectedDate = signal<Dayjs | undefined>(undefined);

    readonly isSelecting = computed(() => this.dateSpanPicker() && this.firstSelectedDate());
    readonly calendarTitle = computed<string>(() => {
        const currentDate = this.currentDate();
        return `${currentDate.format('MMM YYYY')}`;
    });

    /**
     * Checks if the previous month would be before the minDate boundary.
     * Returns false if navigation should be prevented.
     */
    protected readonly canNavigateToPrevMonth = computed(() => {
        const minDate = this.minDate();
        if (!minDate) {
            return true;
        }

        const proposedDate = this.currentDate().subtract(1, 'month');
        const minDateJs = dayjs(minDate);

        return !proposedDate.isBefore(minDateJs, 'month');
    });

    /**
     * Checks if the next month would be after the maxDate boundary.
     * Returns false if navigation should be prevented.
     */
    protected readonly canNavigateToNextMonth = computed(() => {
        const maxDate = this.maxDate();
        if (!maxDate) {
            return true;
        }

        const proposedDate = this.currentDate().add(1, 'month');
        const maxDateJs = dayjs(maxDate);

        return !proposedDate.isAfter(maxDateJs, 'month');
    });

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor() {
        dayjs.extend(utc);
        dayjs.extend(isBetween);
        dayjs.extend(customParseFormat);

        effect(() => {
            const dateSpanPicker = this.dateSpanPicker();
            const firstSelectedDate = this.firstSelectedDate();

            // Only reset selection state when mode changes, not during normal selection
            if (dateSpanPicker === undefined || !firstSelectedDate) {
                this.firstSelectedDate.set(undefined);
                this.hoverDateSpan.set(undefined);
            }
        });
    }

    hoverDate(date: Dayjs | undefined, event?: MouseEvent): void {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
        }

        if (!date) {
            return;
        }

        if (this.isSelecting()) {
            const firstSelected = this.firstSelectedDate();
            if (firstSelected) {
                if (date.isAfter(firstSelected)) {
                    this.hoverDateSpan.set([firstSelected.toDate(), date.toDate()]);
                } else {
                    this.hoverDateSpan.set([date.toDate(), firstSelected.toDate()]);
                }
            }
        }
    }

    onDateClick(date: Dayjs | undefined): void {
        if (!date) {
            return;
        }

        // Show time picker
        if (this.hourPicker() && !this.dateSpanPicker()) {
            this.tempDate.set(date);
            this.showHourPicker.set(true);
        } else {
            this.selectDate(date);
        }
    }

    /**
     * Select date
     * @param date
     * @param event
     */
    selectDate(date: Dayjs, event?: MouseEvent): void {
        if (event) {
            event.stopPropagation();
        }

        // Abort if outside allowed min or max.
        if (!this.isBetweenMinMax(date)) {
            return;
        }

        this.hoverDateSpan.set(undefined);

        // When a dateSpan should be selected (start and end date)
        if (this.dateSpanPicker()) {
            // When second date in span is selected
            const firstSelected = this.firstSelectedDate();
            if (firstSelected) {
                const startDate = dayjs(firstSelected).isBefore(dayjs(date))
                    ? firstSelected.startOf('day').toDate()
                    : date.startOf('day').toDate();
                const endDate = dayjs(firstSelected).isBefore(dayjs(date))
                    ? date.endOf('day').toDate()
                    : firstSelected.endOf('day').toDate();

                this.startDateChange.emit(startDate);
                this.endDateChange.emit(endDate);
                this.firstSelectedDate.set(undefined);
            }
            // if both dates were defined, you should restart the selection process.
            else {
                this.firstSelectedDate.set(date);
            }
        }
        // Single date
        else {
            const newDate = date.utc().toDate();
            this.startDateChange.emit(newDate);
            this.dateChange.emit(newDate);
            this.firstSelectedDate.set(undefined); // Should always be undefined for single select
        }
    }

    /**
     * Select a specific hour
     * @param hour Hour to select (0-23)
     * @param event
     */
    selectHour(hour: number, event: MouseEvent): void {
        this.showHourPicker.set(false);
        const tempDate = this.tempDate();
        if (tempDate) {
            this.selectDate(tempDate.hour(hour));
        }

        if (event) {
            event.preventDefault();
        }
    }

    /**
     * Select year
     * @param year
     * @param event
     */
    selectYear(year: number, event?: MouseEvent): void {
        this.currentDate.set(this.currentDate().year(year));
        this.showYearPicker.set(false);

        if (event) {
            event.preventDefault();
        }
    }

    /**
     * Navigates to the previous month.
     *
     * @description Updates the calendar view to show the previous month.
     * Respects min/max date boundaries and prevents navigation beyond minDate.
     */
    prevMonth(): void {
        if (!this.canNavigateToPrevMonth()) {
            return;
        }

        this.currentDate.set(this.currentDate().subtract(1, 'month'));
    }

    /**
     * Navigates to the next month.
     *
     * @description Updates the calendar view to show the next month.
     * Respects min/max date boundaries and prevents navigation beyond maxDate.
     */
    nextMonth(): void {
        if (!this.canNavigateToNextMonth()) {
            return;
        }

        this.currentDate.set(this.currentDate().add(1, 'month'));
    }

    /**
     * Sets the current date to today and selects it.
     *
     * @description Navigates the calendar to the current month and selects today's date.
     * Useful for providing a "Today" button functionality.
     */
    today(): void {
        const today = dayjs();
        this.currentDate.set(today);
        this.selectDate(today);
    }

    /**
     * Opens the year picker interface.
     *
     * @description Displays a grid of years for quick year navigation.
     * Only works if `yearPicker` is enabled.
     */
    openYearPicker(): void {
        if (!this.yearPicker()) {
            return;
        }
        this.showYearPicker.set(true);
    }

    /**
     * Clears all selected dates and resets the picker state.
     *
     * @description Removes all date selections, clears hover states, and emits
     * undefined for all date change outputs. Useful for providing a "Clear" button.
     */
    clear(): void {
        this.firstSelectedDate.set(undefined);
        this.hoverDateSpan.set(undefined);
        this.startDateChange.emit(undefined);
        this.endDateChange.emit(undefined);
        this.dateChange.emit(undefined);
    }

    trackByDate(index: number, date: UICalendarDate): string {
        // Include the index to ensure uniqueness, even for dates that might
        // produce the same calculation result
        return `${index}_${date.year || 0}_${date.month || 0}_${date.day || 0}`;
    }

    /**
     * Generate the calendar of the current month
     * @param hoverPreview Whether this is a preview of the current hover state or not
     */
    private generateCalendar(hoverPreview?: boolean): UICalendarDate[] {
        const currentDate = dayjs(this.currentDate());
        const month = currentDate.month();
        const year = currentDate.year();
        const firstWeekDay = this.firstWeekdaySunday()
            ? currentDate.date(2).day()
            : currentDate.date(1).day();
        const firstSelectedDate = this.firstSelectedDate();
        const dateSpan = hoverPreview
            ? this.hoverDateSpan() || [firstSelectedDate?.toDate(), firstSelectedDate?.toDate()]
            : [this.startDate(), this.endDate()];
        const selectedStartDate = dateSpan[0] ? dayjs(dateSpan[0]) : undefined;
        const selectedEndDate = dateSpan[1] ? dayjs(dateSpan[1]) : undefined;
        const dateOffset = 1 - (firstWeekDay !== 1 ? (firstWeekDay + 6) % 7 : 0);

        const monthEndDate = dayjs(currentDate).endOf('month').date();
        const firstDate = dayjs(currentDate)
            .startOf('month')
            .subtract(1, 'days')
            .add(dateOffset, 'days');

        const daysArray = [];
        const numberOfDaysDisplayed = monthEndDate - dateOffset;
        const iterateTo =
            monthEndDate + (numberOfDaysDisplayed % 7 !== 0 ? 6 - (numberOfDaysDisplayed % 7) : 0);

        for (let i = dateOffset; i <= iterateTo; i++) {
            const date = dayjs(firstDate).add(i - dateOffset, 'days');
            const today = dayjs().isSame(date, 'day');
            let outOfMonth = false;
            let between = false;
            let first = false;
            let last = false;

            if (this.dateSpanPicker()) {
                if (selectedStartDate && selectedEndDate) {
                    between = date.isBetween(selectedStartDate, selectedEndDate, 'day');
                    first = date.isSame(selectedStartDate, 'day');
                    last = date.isSame(selectedEndDate, 'day');
                } else if (firstSelectedDate) {
                    first = firstSelectedDate.isSame(date, 'day');
                }
            } else {
                first = last = (selectedStartDate && selectedStartDate.isSame(date, 'day')) || false;
            }

            outOfMonth =
                !((i > 0 || this.dateSpanPicker()) && (first || between || last)) &&
                date.month() !== currentDate.month();

            const day: UICalendarDate = {
                day: date.date(),
                month: i > 0 ? month : undefined,
                year: i > 0 ? year : undefined,
                enabled:
                    (date.month() === currentDate.month() || this.dateSpanPicker()) &&
                    this.isBetweenMinMax(date),
                today: today,
                selected: {
                    first: (i > 0 || this.dateSpanPicker()) && first,
                    last: (i > 0 || this.dateSpanPicker()) && last,
                    between: (i > 0 || this.dateSpanPicker()) && between
                },
                outOfMonth: outOfMonth,
                dayjsObj: date
            };

            daysArray.push(day);
        }

        return daysArray;
    }

    /**
     * Check if a date is between the min/max limits or not.
     * @param date
     */
    private isBetweenMinMax(date: Dayjs | ConfigType): boolean {
        date = this.toDayjs(date);
        const minDate = this.minDate();
        const maxDate = this.maxDate();

        if (minDate !== undefined) {
            if (maxDate !== undefined) {
                return date.isBetween(minDate, maxDate, 'day', '[]');
            }
            return !date.isBefore(minDate, 'day');
        } else if (maxDate !== undefined) {
            return !date.isAfter(maxDate, 'day');
        }
        return true;
    }

    /**
     * Make sure object is a dayjs object
     * @param date
     */
    private toDayjs(date: Dayjs | ConfigType): Dayjs {
        if (dayjs.isDayjs(date)) {
            return date;
        }
        return dayjs(date);
    }
}

export interface UICalendarDate {
    day: number;
    month: number | undefined;
    year: number | undefined;
    enabled: boolean | undefined;
    today: boolean;
    selected: {
        /** Whether this is the first date in a selection */
        first: boolean | undefined;
        /** Whether this is the last date in a selection */
        last: boolean | undefined;
        /** Whether this date is between start and end dates in a range */
        between: boolean | undefined;
    };
    dayjsObj: Dayjs;
    outOfMonth: boolean;
}
