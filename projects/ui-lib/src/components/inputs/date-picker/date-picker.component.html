<!--DATE PICKER -->

@if (!showYearPicker() && !showHourPicker()) {
    <div
        [className]="outerBorders() ? 'date-picker-bordered' : 'date-picker-unbordered'"
        [ngClass]="{ 'date-picker-compact': isCompact() }">
        <div class="current-month">
            @if (isNewUI()) {
                <ui-svg-icon
                    class="step-month"
                    [class.disabled]="!canNavigateToPrevMonth()"
                    icon="arrow-left"
                    nuiIcon="chevron_left"
                    (click)="prevMonth()" />
            } @else if (!isCompact()) {
                <ui-icon
                    class="step-month"
                    [class.disabled]="!canNavigateToPrevMonth()"
                    icon="arrow-left-b"
                    (click)="prevMonth()">
                </ui-icon>
            } @else {
                <ui-svg-icon
                    class="step-month"
                    [class.disabled]="!canNavigateToPrevMonth()"
                    icon="arrow-left"
                    (click)="prevMonth()" />
            }
            <div
                class="month-name"
                (click)="openYearPicker()">
                {{ calendarTitle() }}
            </div>
            @if (isNewUI()) {
                <ui-svg-icon
                    class="step-month"
                    [class.disabled]="!canNavigateToNextMonth()"
                    icon="arrow-right"
                    nuiIcon="chevron_right"
                    (click)="nextMonth()" />
            } @else if (!isCompact()) {
                <ui-icon
                    class="step-month"
                    [class.disabled]="!canNavigateToNextMonth()"
                    icon="arrow-right-b"
                    (click)="nextMonth()">
                </ui-icon>
            } @else {
                <ui-svg-icon
                    class="step-month"
                    [class.disabled]="!canNavigateToNextMonth()"
                    icon="arrow-right"
                    (click)="nextMonth()" />
            }
        </div>
        <div class="day-names">
            @if (firstWeekdaySunday()) {
                <div class="day">Sun</div>
            }
            <div class="day">Mon</div>
            <div class="day">Tue</div>
            <div class="day">Wed</div>
            <div class="day">Thu</div>
            <div class="day">Fri</div>
            <div class="day">Sat</div>
            @if (!firstWeekdaySunday()) {
                <div class="day">Sun</div>
            }
        </div>
        <div class="calendar date">
            @for (d of days(); track trackByDate($index, d)) {
                <div
                    class="item"
                    (mouseenter)="hoverDate(d.dayjsObj, $event)"
                    (click)="onDateClick(d.dayjsObj)"
                    [ngClass]="{
                        disabled: !d.enabled,
                        today: d.today,
                        'selected-first': d.selected.first,
                        'selected-last': d.selected.last,
                        'selected-between': d.selected.between,
                        'out-of-month': d.outOfMonth
                    }">
                    <div
                        class="inner"
                        [attr.data-testid]="'selected-' + d.day">
                        <span class="inner-content">
                            {{ d.day }}
                        </span>
                    </div>
                </div>
            }
        </div>
    </div>
}

<!--YEAR PICKER-->
@if (showYearPicker()) {
    <div class="year-picker">
        <div class="calendar year">
            @for (year of years(); track year; let i = $index) {
                <div
                    class="item"
                    (click)="selectYear(year, $event)">
                    <div class="inner">
                        {{ year }}
                    </div>
                </div>
            }
        </div>
    </div>
}

<!--HOUR PICKER-->
@if (showHourPicker()) {
    <div class="hour-picker">
        <div class="calendar hour">
            @for (hour of hours(); track hour; let i = $index) {
                <div
                    class="item"
                    (click)="selectHour(i, $event)">
                    <div class="inner">{{ i < 10 ? '0' + i : i }}:00</div>
                </div>
            }
        </div>
    </div>
}
