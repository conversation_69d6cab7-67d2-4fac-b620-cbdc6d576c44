:where(:root[data-uinew]) :host {
    display: block;
    color: var(--nui-icon-primary, #1e1f24);
    font-family: var(--nui-label-regular-font-family, 'Roboto Flex');
    font-size: var(--nui-label-regular-font-size, 12px);
    font-weight: var(--nui-label-regular-font-weight, 400);
    line-height: var(--nui-label-regular-line-height, 16px);
    letter-spacing: var(--nui-label-regular-letter-spacing, 0.14px);
    background-color: var(--nui-fill-neutral-subtlest, #fff);
    -webkit-touch-callout: none;
    user-select: none;
    width: fit-content;

    .current-month {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: var(--nui-icon-font-icon-size, 16px);
        font-style: normal;
        font-weight: 300;
        line-height: 100%;

        .step-month {
            flex: 0 0;
            display: flex;
            align-items: center;
            cursor: pointer;
            color: var(--nui-icon-primary, #1e1f24);
            font-family: 'Material Symbols Rounded';
            font-size: var(--nui-icon-font-icon-size, 16px);
            font-style: normal;
            font-weight: 300;
            line-height: 100%;

            &:hover {
                opacity: 1;
            }

            &.disabled {
                opacity: 0.3;
                cursor: not-allowed;
                pointer-events: none;
            }
        }

        .month-name {
            font-family: var(--nui-label-bold-font-family, 'Roboto Flex');
            font-size: var(--nui-label-bold-font-size, 12px);
            font-style: normal;
            font-weight: var(--nui-label-bold-font-weight, 500);
            line-height: var(--nui-label-bold-line-height, 16px);
            letter-spacing: var(--nui-label-bold-letter-spacing, 0.14px);
        }
    }

    .day-names {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        margin: var(--nui-space-200, 8px) 0;
        text-align: center;
    }

    .calendar {
        display: grid;
        justify-content: center;
        grid-gap: 1px;
        grid-template-columns: auto auto auto auto auto auto auto;
        padding: var(--nui-space-050, 2px);

        .item {
            margin: -0.1rem;
            position: relative;
            cursor: pointer;

            .inner {
                height: var(--nui-height-small, 32px);
                width: 32px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            &.selected-between {
                .inner {
                    background-color: var(--nui-calendar-date-picker-item-fill-selected-span, #e9f3ff);
                    color: var(--nui-calendar-date-picker-item-text-selected-span, #006cfd);
                    font-weight: var(--nui-label-bold-font-weight, 500);
                    line-height: var(--nui-label-bold-line-height, 16px);
                    letter-spacing: var(--nui-label-bold-letter-spacing, 0.14px);
                }
            }

            &.selected-first {
                .inner {
                    border-radius: var(--nui-border-radius-tiny, 2px) 0 0
                        var(--nui-border-radius-tiny, 2px);
                }
            }

            &.selected-last {
                .inner {
                    border-radius: 0 var(--nui-border-radius-tiny, 2px)
                        var(--nui-border-radius-tiny, 2px) 0;
                }
            }

            &.selected-first,
            &.selected-last {
                z-index: 1;

                .inner {
                    background-color: var(--nui-calendar-date-picker-item-fill-selected, #006cfd);
                    color: var(--nui-calendar-date-picker-item-text-selected, #1e1f24);
                    font-weight: var(--nui-label-bold-font-weight, 500);
                }
            }

            &.out-of-month {
                .inner {
                    color: var(--nui-calendar-date-picker-item-text-disabled, rgba(0, 8, 48, 27%));
                    pointer-events: none;
                }
            }

            &.today {
                color: var(--nui-calendar-date-picker-item-text-today, #006cfd);
            }
        }

        &.date {
            width: fit-content;
            border-radius: var(--nui-border-radius-small, 4px);
            border: 1px solid var(--nui-border-neutral-subtle, #eff0f3);
            background: var(--nui-fill-neutral-subtlest, #fff);
            box-shadow: var(--nui-shadow-subtlest-x, 0) var(--nui-shadow-subtlest-y, 4px)
                var(--nui-shadow-subtlest-blur, 8px) var(--nui-shadow-subtlest-spread, 0)
                var(--nui-shadows-subtlest, rgba(0, 0, 51, 6%));

            // 7 columns...
            grid-template-columns: auto auto auto auto auto auto auto;

            .inner {
                &:hover {
                    border-radius: var(--nui-border-radius-tiny, 2px);
                    background: var(--calendar-date-picker-item-fill-hover, #e7e8ec);
                }
            }
        }
    }
}
