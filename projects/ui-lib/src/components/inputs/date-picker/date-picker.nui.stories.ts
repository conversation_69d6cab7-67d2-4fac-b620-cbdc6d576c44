import { moduleMetadata } from '@storybook/angular';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/angular/';
import { UIDatePickerComponent } from './date-picker.component';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { Input, Output, EventEmitter, Component, OnDestroy } from '@angular/core';
import { fn } from '@storybook/test';
import dayjs from 'dayjs';
import { UIIconComponent } from '../../icon';

// Fixed dates are used to prevent <PERSON><PERSON> from detecting UI changes on every PR
// When using dynamic dates (like new Date()), <PERSON><PERSON> would flag the date-picker
// as changed because the displayed dates would be different in each build
const FIXED_DATE = dayjs('2025-01-15').toDate();
const FIXED_START_DATE = dayjs('2025-01-15').toDate();
const FIXED_END_DATE = dayjs('2025-01-25').toDate();
const FIXED_MIN_DATE = dayjs('2025-01-01').toDate();
const FIXED_MAX_DATE = dayjs('2025-01-31').toDate();

// Keep a reference to the real Date
const REAL_DATE = Date as DateConstructor;

function freezeDateTo(ms: number) {
    const Frozen: any = function (...args: any[]) {
        if (new.target) {
            return args.length ? new (REAL_DATE as any)(...args) : new (REAL_DATE as any)(ms);
        }
        return args.length ? (REAL_DATE as any)(...args) : new (REAL_DATE as any)(ms).toString();
    };

    Frozen.now = () => ms;
    Frozen.parse = REAL_DATE.parse;
    Frozen.UTC = REAL_DATE.UTC;
    Frozen.prototype = REAL_DATE.prototype;
    Object.defineProperty(Frozen, Symbol.hasInstance, {
        value: (instance: any) => instance instanceof REAL_DATE
    });

    (globalThis as any).Date = Frozen as DateConstructor;
}

function resetDate() {
    (globalThis as any).Date = REAL_DATE;
}

@Component({
    selector: 'date-freeze-reset',
    standalone: true,
    template: ``
})
class DateFreezeResetComponent implements OnDestroy {
    ngOnDestroy() {
        resetDate();
    }
}

@Component({
    selector: 'storybook-date-picker-container',
    standalone: true,
    imports: [UIDatePickerComponent, UIIconComponent],
    template: `
        <ui-date-picker
            [date]="date"
            [startDate]="startDate"
            [endDate]="endDate"
            [maxDate]="isMaxDate ? maxDate : undefined"
            [minDate]="minDate"
            [dateSpanPicker]="dateSpanPicker"
            [firstWeekdaySunday]="firstWeekdaySunday"
            (dateChange)="handleDateChange($event)"
            (startDateChange)="handleStartDateChange($event)"
            (endDateChange)="handleEndDateChange($event)"></ui-date-picker>
    `
})
class DatePickerStoryContainerComponent {
    @Input() date: Date = FIXED_DATE;
    @Input() startDate: Date = FIXED_START_DATE;
    @Input() endDate: Date = FIXED_END_DATE;
    @Input() maxDate: Date = FIXED_MAX_DATE;
    @Input() minDate: Date = FIXED_MIN_DATE;
    @Input() hourPicker = false;
    @Input() dateSpanPicker = false;
    @Input() firstWeekdaySunday = false;
    @Input() isMaxDate = true;

    @Output() dateChange = new EventEmitter<Date>();
    @Output() startDateChange = new EventEmitter<Date>();
    @Output() endDateChange = new EventEmitter<Date>();

    handleDateChange(newDate: Date) {
        fn()(newDate);
        this.date = newDate;
        this.dateChange.emit(newDate);
    }

    handleStartDateChange(newStartDate: Date) {
        fn()(newStartDate);
        this.startDate = newStartDate;
        this.startDateChange.emit(newStartDate);
    }

    handleEndDateChange(newEndDate: Date) {
        fn()(newEndDate);
        this.endDate = newEndDate;
        this.endDateChange.emit(newEndDate);
    }
}

const meta: Meta<DatePickerStoryContainerComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/DatePicker',
    component: DatePickerStoryContainerComponent,
    decorators: [
        moduleMetadata({
            imports: [UIDatePickerComponent, UIIconComponent, DatePickerStoryContainerComponent]
        })
    ],
    argTypes: {
        // date: { control: 'date' },
        startDate: { control: 'date' },
        endDate: { control: 'date' },
        dateSpanPicker: { control: 'boolean' },
        firstWeekdaySunday: { control: 'boolean' },
        maxDate: { control: 'date' },
        minDate: { control: 'date' },
        isMaxDate: { control: 'boolean' }
    }
};

export default meta;
type Story = StoryObj<UIDatePickerComponent>;

const generateUsageExample = (args: Partial<DatePickerStoryContainerComponent>) => {
    const props = Object.entries(args)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => {
            if (typeof value === 'boolean' || typeof value === 'number') {
                return `[${key}]="${value}"`;
            } else if (value instanceof Date) {
                return `[${key}]="'${value.toISOString()}'"`;
            } else {
                return `[${key}]="${JSON.stringify(value)}"`;
            }
        })
        .join('\n    ');

    return `
<ui-date-picker
    ${props}
    (dateChange)="onDateChange($event)"
    (startDateChange)="onStartDateChange($event)"
    (endDateChange)="onEndDateChange($event)">
</ui-date-picker>
`;
};

const FROZEN_NOW = new Date(FIXED_DATE).getTime();

export const BasicDatePicker: Story = {
    decorators: [
        moduleMetadata({
            imports: [DateFreezeResetComponent, UIDatePickerComponent, UIIconComponent]
        })
    ],
    render: args => {
        freezeDateTo(FROZEN_NOW);
        return {
            props: args,
            template: `
      <div>
        <ui-date-picker
          [firstWeekdaySunday]="firstWeekdaySunday"
          [maxDate]="isMaxDate ? maxDate : undefined"
          [minDate]="minDate">
        </ui-date-picker>
      </div>
    `
        };
    },
    args: {}
};

export const PreselectedDatePicker: Story = {
    render: args => ({
        component: DatePickerStoryContainerComponent,
        props: args
    }),
    args: {
        dateSpanPicker: true,
        startDate: FIXED_START_DATE,
        endDate: FIXED_END_DATE
    },
    parameters: {
        docs: {
            source: {
                code: generateUsageExample({
                    dateSpanPicker: true,
                    startDate: FIXED_START_DATE,
                    endDate: FIXED_END_DATE
                })
            }
        }
    }
};
