import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIButtonComponent } from '../../buttons';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UITooltipDirective } from '../../popovers';
import { UIInputComponent } from '../input';
import { UIInlineEditComponent } from './inline-edit.component';
import { UILabelComponent } from '../../label/label.component';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UIInlineEditComponent> = {
    title: 'Components/Inputs/InlineEdit',
    component: UIInlineEditComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIInlineEditComponent,
                UIIconComponent,
                UITooltipDirective,
                UISVGIconComponent,
                UIButtonComponent,
                UIInputComponent,
                UILabelComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                UIComponentSizeDirective
            ]
        })
    ],
    argTypes: {
        value: { control: 'text' },
        hideEditIcon: { control: 'boolean' },
        loading: { control: 'boolean' },
        isEditing: { control: 'boolean' },
        autoClose: { control: 'boolean' },
        clickOutside: { control: 'boolean' },
        leadingIcon: { control: 'text' },
        size: { control: { type: 'select' }, options: ['xs', 'sm', 'md'] },
        save: { action: 'save' },
        cancel: { action: 'cancel' },
        navigationClick: { action: 'navigationClick' }
    }
};
export default meta;
type Story = StoryObj<UIInlineEditComponent>;

const render: Story['render'] = args => ({
    props: args,
    args,
    template: `
<ui-inline-edit [isEditing]="isEditing" [value]="value ?? 'Some value'" (save)="value= $event.newValue"/> `
});
export const InlineEdit: Story = {
    args: {
        value: 'Some value',
        hideEditIcon: false,
        isEditing: false
    },
    render
};

export const NUIWithLeadingIconSizes: Story = {
    ...NUI_STORY_SETTINGS,
    render: () => ({
        props: {
            mdValue: 'Some value',
            smValue: 'Some value',
            xsValue: 'Some value'
        },
        template: `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <h3 [uiSize]="'sm'">MD</h3>
                <ui-inline-edit [value]="mdValue" leadingIcon="folder" size="md" (save)="mdValue = $event.newValue"/>
                <h3 [uiSize]="'sm'">SM</h3>
                <ui-inline-edit [value]="smValue" leadingIcon="folder" size="sm" (save)="smValue = $event.newValue"/>
                <h3 [uiSize]="'sm'">XS</h3>
                <ui-inline-edit [value]="xsValue" leadingIcon="folder" size="xs" (save)="xsValue = $event.newValue"/>
            </div>
        `
    })
};
