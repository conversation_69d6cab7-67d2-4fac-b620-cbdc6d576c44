import { NgClass } from '@angular/common';
import {
    Component,
    computed,
    ElementRef,
    inject,
    input,
    linkedSignal,
    OnChanges,
    OnInit,
    output,
    SimpleChanges
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MaterialSymbol } from 'material-symbols';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UINUIIcon } from '../../../types/icons';
import { UIButtonComponent } from '../../buttons';
import { UISVGIconComponent } from '../../icon';
import { UILabelComponent } from '../../label/label.component';
import { UITooltipDirective } from '../../popovers';
import { UIInputComponent } from '../input/input.component';

export interface InlineEditOnSaveEvent {
    newValue: string;
    oldValue: string;
    inputControl: UntypedFormControl;
}

@Component({
    imports: [
        NgClass,
        UISVGIconComponent,
        UITooltipDirective,
        UIInputComponent,
        UIButtonComponent,
        UILabelComponent
    ],
    selector: 'ui-inline-edit',
    templateUrl: './inline-edit.component.html',
    styleUrls: ['./inline-edit.component.scss', './inline-edit.new.component.scss'],
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(document:click)': 'onDocumentClick($event)'
    }
})
export class UIInlineEditComponent implements OnInit, OnChanges {
    private uiNewThemeService = inject(UINewThemeService);
    private elementRef = inject(ElementRef);

    value = input.required<string>();
    loading = input(false);
    hideEditIcon = input(false);
    isEditing = input(false);
    autoClose = input(true);
    clickOutside = input(true);
    leadingIcon = input<UINUIIcon | MaterialSymbol>();
    size = input<'xs' | 'sm' | 'md'>('md');
    disabled = input(false);
    valueCheckFunction = input<(currentValue: string, originalValue: string) => boolean>();

    navigationClick = output<MouseEvent>();
    save = output<InlineEditOnSaveEvent>();
    cancel = output<void>();

    protected inlineEditSize = computed(() => (this.size() === 'md' ? 'sm' : 'xs'));
    protected leadingIconSize = computed(() => (this.size() === 'xs' ? 'sm' : 'md'));

    protected isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    protected showEditIcon = false;
    validationControl: UntypedFormControl = new UntypedFormControl('');
    cancelDisabled = false;

    protected isEditingState = linkedSignal(() => this.isEditing());

    ngOnChanges(changes: SimpleChanges): void {
        if (
            this.validationControl.valid &&
            this.isEditingState() &&
            changes['value'] &&
            this.autoClose()
        ) {
            this.cancelDisabled = false;
            this.endEditing();
        }
    }

    ngOnInit(): void {
        this.validationControl.statusChanges.subscribe(() => {
            this.cancelDisabled = false;
        });
    }

    keyUpListener(event: KeyboardEvent, inputValue: string): void {
        if (event.key === 'Enter') {
            this.saveEdit(inputValue);
        }
        if (event.key === 'Escape') {
            this.endEditing();
        }
    }

    mouseEnter(): void {
        if (!this.hideEditIcon()) {
            this.showEditIcon = true;
        }
    }

    mouseLeave(): void {
        this.showEditIcon = false;
    }

    enableEditing(mouseEvent: MouseEvent): void {
        if (this.disabled()) {
            return;
        }

        mouseEvent.stopPropagation();
        this.isEditingState.set(true);
    }

    saveEdit(inputValue: string, mouseEvent?: MouseEvent): void {
        if (mouseEvent) {
            mouseEvent.stopPropagation();
        }

        const valueCheckFunction = this.valueCheckFunction();
        const originalValue = this.value();

        if (
            valueCheckFunction?.(inputValue, originalValue) ||
            (inputValue !== originalValue && inputValue.trim().length > 0)
        ) {
            this.cancelDisabled = true;
            this.save.emit({
                newValue: inputValue,
                oldValue: originalValue,
                inputControl: this.validationControl
            });
            this.validationControl.markAsTouched();
        }
    }

    navigate(mouseEvent: MouseEvent): void {
        this.navigationClick.emit(mouseEvent);
    }

    onDocumentClick(event: MouseEvent): void {
        if (
            this.isEditingState() &&
            this.clickOutside() &&
            !this.elementRef.nativeElement.contains(event.target)
        ) {
            this.endEditing(event);
        }
    }

    endEditing(mouseEvent?: MouseEvent): void {
        if (mouseEvent) {
            mouseEvent.stopPropagation();
        }

        if (this.cancelDisabled) {
            return;
        }

        this.isEditingState.set(false);
        this.showEditIcon = false;
        this.validationControl.reset();
        this.cancel.emit();
    }
}
