:where(:root:not([data-uinew])) :host {
    width: 100%;

    .cell-text-wrapper {
        display: flex;
        align-items: center;
    }

    .ellipsis-wrapper {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 0.2rem;
    }

    .clip-text {
        cursor: pointer;

        --icon-color: inherit;

        width: 100%;

        .item-icon {
            margin-right: 0.7rem;
            font-size: 1.4rem;
            position: relative;
            top: 2px;
            color: var(--icon-color);
        }

        a {
            text-decoration: none;
            color: var(--ui-color-text);

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .edit-icon-show {
        margin-left: 0.7rem;
        margin-right: 2rem;
        font-size: 1.4rem;
        visibility: visible;
        cursor: pointer;
        transition-duration: 0.1s;
        color: var(--ui-color-active);
    }

    .edit-icon-hide {
        visibility: hidden;
        margin-left: 7px;
        font-size: 1.4rem;
    }

    .input-wrapper {
        display: flex;
        width: 100%;

        .edit-input {
            width: 100%;
        }

        .icon-group {
            display: flex;
            align-items: center;
            margin-left: 1.2rem;
            margin-right: 5.1rem;
            justify-content: space-between;
            width: 3rem;
            gap: 0.7rem;

            .close {
                color: var(--ui-color-alert);
            }

            .checkmark:hover {
                --border-color-hover: var(--ui-color-primary);
            }

            .close:hover {
                --border-color-hover: var(--ui-color-alert);
            }
        }
    }
}
