@if (isNewUI()) {
    @if (isEditingState()) {
        <div class="input-wrapper">
            @if (leadingIcon()) {
                <ui-label
                    [leadingIcon]="leadingIcon()"
                    [size]="leadingIconSize()"
                    [type]="disabled() ? 'disabled' : 'primary'" />
            }

            <ui-input
                #editInput
                [size]="inlineEditSize()"
                [validation]="validationControl"
                [value]="value()"
                [attr.data-testid]="'edit-input-' + value()"
                [autofocus]="true"
                [disabled]="disabled()"
                (keyup)="keyUpListener($event, editInput.value!)"
                (click)="$event.stopPropagation()"
                (closeIconClick)="endEditing($event)"
                (mousedown)="$event.stopPropagation()" />

            <ui-button
                [loading]="loading()"
                nuiSvgIcon="check"
                [attr.data-testid]="'icon-group-checkmark'"
                type="solid-primary"
                [disabled]="disabled()"
                [size]="inlineEditSize()"
                (click)="saveEdit(editInput.value!, $event)" />
        </div>
    } @else {
        <div
            class="cell-text-wrapper"
            (mouseenter)="mouseEnter()"
            (mouseleave)="mouseLeave()">
            <div
                class="overflow-wrapper"
                [uiTooltip]="value()"
                [uiTooltipOnlyWhenTruncated]="true"
                uiTooltipMaxWidth="500px"
                [attr.data-testid]="'edit-name-' + value()">
                <ui-label
                    [truncate]="true"
                    [leadingIcon]="leadingIcon()"
                    [type]="disabled() ? 'disabled' : 'primary'"
                    [size]="leadingIconSize()"
                    (click)="navigate($event)">
                    {{ value() }}
                </ui-label>
            </div>

            <ui-button
                nuiSvgIcon="edit"
                type="plain-primary"
                [size]="'sm'"
                [disabled]="disabled()"
                [ngClass]="showEditIcon ? 'edit-icon-show' : 'edit-icon-hide'"
                [attr.data-testid]="'edit-icon-' + value()"
                (click)="enableEditing($event)" />
        </div>
    }
} @else {
    @if (isEditingState()) {
        <div
            class="input-wrapper"
            (clickOutside)="clickOutside() && endEditing()">
            <ui-input
                (click)="$event.stopPropagation()"
                (mousedown)="$event.stopPropagation()"
                ui-theme="small"
                class="edit-input"
                #editInput
                [validation]="validationControl"
                [value]="value()"
                [attr.data-testid]="'edit-input-' + value()"
                [autofocus]="true"
                (keyup)="keyUpListener($event, editInput.value!)" />
            <div class="icon-group">
                <ui-button
                    [loading]="loading()"
                    svgIcon="checkmark-large"
                    class="checkmark"
                    [attr.data-testid]="'icon-group-checkmark'"
                    ui-theme="small"
                    (click)="saveEdit(editInput.value!, $event)" />
                <ui-button
                    [disabled]="cancelDisabled"
                    svgIcon="close"
                    class="close"
                    [attr.data-testid]="'icon-group-close'"
                    ui-theme="small"
                    (click)="endEditing($event)" />
            </div>
        </div>
    } @else {
        <div
            class="cell-text-wrapper"
            (mouseenter)="mouseEnter()"
            (mouseleave)="mouseLeave()">
            <div
                class="ellipsis-wrapper"
                [uiTooltip]="value()"
                [uiTooltipOnlyWhenTruncated]="true"
                uiTooltipMaxWidth="500px"
                [attr.data-testid]="'edit-name-' + value()">
                <span class="clip-text">
                    <a (click)="navigate($event)">
                        <span>{{ value() }}</span>
                    </a>
                </span>
            </div>
            <ui-svg-icon
                icon="edit"
                [ngClass]="showEditIcon ? 'edit-icon-show' : 'edit-icon-hide'"
                [attr.data-testid]="'edit-icon-' + value()"
                (click)="enableEditing($event)" />
        </div>
    }
}
