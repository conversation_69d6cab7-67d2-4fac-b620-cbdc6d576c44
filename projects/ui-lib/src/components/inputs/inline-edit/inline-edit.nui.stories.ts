import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIInlineEditComponent } from './inline-edit.component';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UIInlineEditComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/InlineEdit',
    component: UIInlineEditComponent,
    decorators: [moduleMetadata({ imports: [UIInlineEditComponent, UIComponentSizeDirective] })],
    argTypes: {
        value: { control: 'text' },
        hideEditIcon: { control: 'boolean' },
        loading: { control: 'boolean' },
        isEditing: { control: 'boolean' },
        autoClose: { control: 'boolean' },
        clickOutside: { control: 'boolean' },
        leadingIcon: { control: 'text' },
        size: { control: { type: 'select' }, options: ['xs', 'sm', 'md'] },
        save: { action: 'save' },
        cancel: { action: 'cancel' },
        navigationClick: { action: 'navigationClick' }
    },
    parameters: { controls: { disable: true } }
};
export default meta;
type Story = StoryObj<UIInlineEditComponent>;

export const NUIWithLeadingIconSizes: Story = {
    render: () => ({
        props: {
            mdValue: 'Some value',
            smValue: 'Some value',
            xsValue: 'Some value'
        },
        template: `
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <h3 [uiSize]="'sm'">MD</h3>
                <ui-inline-edit [value]="mdValue" leadingIcon="folder" size="md" (save)="mdValue = $event.newValue"/>
                <h3 [uiSize]="'sm'">SM</h3>
                <ui-inline-edit [value]="smValue" leadingIcon="folder" size="sm" (save)="smValue = $event.newValue"/>
                <h3 [uiSize]="'sm'">XS</h3>
                <ui-inline-edit [value]="xsValue" leadingIcon="folder" size="xs" (save)="xsValue = $event.newValue"/>
            </div>
        `
    })
};
