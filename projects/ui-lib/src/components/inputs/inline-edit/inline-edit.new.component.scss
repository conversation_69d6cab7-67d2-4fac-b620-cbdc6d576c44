:where(:root[data-uinew]) :host {
    display: inline-flex;
    align-items: center;
    height: var(--nui-table-item-height);
    gap: var(--nui-table-item-gap);
    width: 100%;

    .cell-text-wrapper {
        display: flex;
        align-items: center;
        gap: var(--nui-table-item-gap, 8px);
        width: 100%;
    }

    .overflow-wrapper {
        overflow: hidden;
    }

    .edit-icon-show {
        visibility: visible;
    }

    .edit-icon-hide {
        visibility: hidden;
    }

    .input-wrapper {
        display: flex;
        gap: var(--nui-table-item-gap, 8px);
    }
}
