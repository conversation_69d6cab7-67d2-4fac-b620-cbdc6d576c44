<div class="wrapper">
    @if (label && isNewUI()) {
        <ui-label
            [size]="size()"
            weight="bold"
            [type]="disabled ? 'disabled' : 'primary'"
            >{{ label }}</ui-label
        >
    }
    <div
        class="switch"
        (click)="onClick()"
        [ngClass]="{ checked: selected && !interim, disabled: disabled, interim: interim }"></div>

    @if (label && !isNewUI()) {
        <span class="checkbox-label">{{ label }}</span>
    }
</div>
