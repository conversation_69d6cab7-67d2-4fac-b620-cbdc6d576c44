import { NgClass } from '@angular/common';
import {
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    computed,
    inject,
    input
} from '@angular/core';
import { UINewThemeService } from '../../../services';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UILabelComponent } from '../../label';
import { UISelectableBaseDirective } from '../selectable-list/selectable.component';

@Component({
    imports: [NgClass, UILabelComponent],
    selector: 'ui-toggle-switch',
    templateUrl: 'toggle-switch.component.html',
    styleUrls: ['toggle-switch.component.scss', 'toggle-switch.new.component.scss'],
    providers: [
        {
            provide: UISelectableBaseDirective,
            useExisting: UIToggleSwitchComponent,
            multi: true
        }
    ],
    host: {
        '[class.ui-toggle-switch]': 'true',
        '[class.disabled]': 'disabled',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIToggleSwitchComponent extends UISelectableBaseDirective {
    override host: ElementRef;
    override changeDetectorRef: ChangeDetectorRef;
    private newThemeService = inject(UINewThemeService);

    /**
     * If the interim state should be shown
     */
    @Input() interim: boolean;

    /**
     * Label of the checkbox
     */
    @Input() label: string;

    size = input<'xs' | 'sm' | 'md'>('md');

    isNewUI = computed(() => this.newThemeService.isNewThemeEnabled());

    constructor() {
        const host = inject(ElementRef);
        const changeDetectorRef = inject(ChangeDetectorRef);

        super();
        this.host = host;
        this.changeDetectorRef = changeDetectorRef;
    }

    public onClick(): void {
        if (this.disabled) {
            return;
        }
        if (this.interim) {
            // If user clicks the toggle in interim state, it always switches to TRUE
            this.interim = false;
            this.select();
        } else {
            this.toggleSelect();
        }
    }
}
