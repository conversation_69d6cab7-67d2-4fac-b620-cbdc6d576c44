import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIToggleSwitchComponent } from './toggle-switch.component';

const meta: Meta<UIToggleSwitchComponent> = {
    title: 'Components/Inputs/ToggleSwitch',
    component: UIToggleSwitchComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, FormsModule, BrowserModule, UIToggleSwitchComponent]
        })
    ],
    argTypes: {
        onClick: { action: 'clicked' }
    }
};
export default meta;
type Story = StoryObj<UIToggleSwitchComponent>;

const render: Story['render'] = args => ({
    args,
    template: `<ui-toggle-switch [selected]="selected" (onClick)="selected = !selected"/> `
});
export const Default: Story = {
    args: {
        selected: true
    },
    render
};

export const Toggled: Story = {
    args: {
        selected: true
    }
};

export const Interim: Story = {
    args: {
        interim: true
    }
};

export const Disabled: Story = {
    args: {
        disabled: true
    }
};

export const NuiToggleSwitch: Story = {
    ...NUI_STORY_SETTINGS
};
