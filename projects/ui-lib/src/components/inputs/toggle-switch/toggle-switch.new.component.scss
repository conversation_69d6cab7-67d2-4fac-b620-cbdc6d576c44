:where(:root[data-uinew]) :host {
    display: inline-block;
    cursor: pointer;

    &,
    & *,
    & *::after,
    .switch {
        box-sizing: border-box;

        &::selection {
            background: none;
        }
    }

    &.disabled {
        cursor: default;
    }

    .switch {
        --switch-handle-size: var(--nui-toggle-item-height);

        outline: 0;
        width: var(--nui-toggle-space-width);
        padding: var(--nui-toggle-item-space-padding-vertical, 2px)
            var(--nui-toggle-item-space-padding-horizontal, 2px);
        position: relative;
        user-select: none;
        background-color: var(--nui-toggle-fill-default);
        border-radius: var(--nui-toggle-item-radius);
        transition: all 0.4s ease;

        &::after {
            position: relative;
            display: block;
            content: '';
            width: var(--nui-toggle-item-width);
            height: var(--switch-handle-size);
            top: 0;
            left: 0;
            border-radius: var(--nui-toggle-item-radius);
            background-color: var(--nui-toggle-fill-handle);
            box-shadow: 0 3px 10px 0 rgba(0, 6, 46, 20%);
            transition: all 0.2s ease;
        }

        &.disabled,
        &.disabled.checked {
            pointer-events: none;
            cursor: default;
            background-color: var(--nui-toggle-fill-disabled);
        }

        &.checked::after {
            left: calc(100% - var(--nui-toggle-item-width));
        }

        &.checked {
            background-color: var(--nui-toggle-fill-selected);
        }

        &.interim {
            --switch-handle-size: 4px;

            height: var(--nui-toggle-item-height);
        }

        &.interim::after {
            left: calc(50% - var(--nui-toggle-item-width) / 2);
            top: calc(50% - var(--switch-handle-size) / 2);
            border-radius: calc(var(--switch-handle-size) / 2);
        }
    }

    .wrapper {
        display: flex;
        align-items: center;
        gap: var(--nui-toggle-space-gap, 8px);
    }

    ui-label {
        flex-grow: 1;
    }
}
