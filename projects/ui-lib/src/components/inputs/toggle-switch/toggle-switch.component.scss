:where(:root:not([data-uinew])) :host {
    display: inline-block;
    cursor: pointer;

    &,
    &::after,
    &::before,
    & *,
    & *::after,
    & *::before,
    .switch {
        box-sizing: border-box;

        &::selection {
            background: none;
        }
    }

    &.disabled {
        cursor: default;
    }

    .switch {
        outline: 0;
        width: var(--width);
        height: var(--height);
        position: relative;
        user-select: none;
        background-color: var(--ui-color-border);
        border: solid var(--toggle-border-size) var(--ui-color-border);
        border-radius: 2em;
        transition: all 0.4s ease;

        &::after,
        &::before {
            position: relative;
            display: block;
            content: '';
            width: var(--switcher-width);
            height: var(--switcher-height);
        }

        &::after {
            top: 0;
            left: 0;
            border-radius: 50%;
            background-color: #ffffff;
            box-shadow: 0 0 2px 0 rgba(0, 0, 0, 50%);
            transition: all 0.2s ease;
        }

        &::before {
            display: none;
        }

        &.disabled {
            pointer-events: none;
            cursor: default;
            opacity: 0.4;
        }

        &.checked::after {
            left: calc(100% - var(--switcher-width));
        }

        &.checked {
            background-color: var(--ui-color-primary);
            border: solid var(--toggle-border-size) var(--ui-color-primary);
        }

        &.interim {
            --switcher-height: 4px;
        }

        &.interim::after {
            left: calc(50% - var(--switcher-width) / 2);
            top: calc(50% - var(--switcher-height) / 2);
            border-radius: calc(var(--switcher-height) / 2);
        }
    }
}
