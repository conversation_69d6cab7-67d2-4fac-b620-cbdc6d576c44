import { Component, inject } from '@angular/core';
import { UINewThemeService } from '../../../services/uinew-theme.service';

@Component({
    imports: [],
    selector: 'ui-theme-toggle',
    styleUrl: './theme-toggle.component.scss',
    templateUrl: './theme-toggle.component.html'
})
export class UIThemeToggleComponent {
    private uiNewThemeService = inject(UINewThemeService);

    currentTheme = this.uiNewThemeService.currentTheme.asReadonly();

    toggleClicked(): void {
        this.uiNewThemeService.toggleTheme();
    }
}
