import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIThemeToggleComponent } from './theme-toggle.component';

const meta: Meta<UIThemeToggleComponent> = {
    title: 'Components/Inputs/ThemeToggle',
    component: UIThemeToggleComponent,
    decorators: [
        moduleMetadata({
            imports: []
        })
    ],
    parameters: {
        backgrounds: {
            values: [{ name: 'Theme', value: 'var(--nui-surface-neutral-subtlest)' }],
            default: 'Theme'
        }
    }
};
export default meta;
type Story = StoryObj<UIThemeToggleComponent>;

export const Default: Story = {};
