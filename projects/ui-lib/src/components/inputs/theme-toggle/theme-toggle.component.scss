.button {
    --color-light: #fff;
    --color-dark: #27282f;
    --color-primary: rgb(230, 81, 168);

    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 4rem;
    background: transparent;
    border: 1px solid var(--color-primary);
    width: 5rem;
    height: 2.6rem;
    border-radius: 30px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &.dark-theme {
        --color-light: #1f1f1f;
        --color-dark: #fff;
    }

    .theme-toggle {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        width: 2rem;
        height: 2rem;
        background: var(--color-dark);
        font-size: 1.3rem;
        border-radius: 50%;
        color: var(--color-light);
        box-shadow: 0 0 1.2rem var(--color-dark);
        position: absolute;
        left: 0.3rem;
        transition:
            transform 500ms,
            left 500ms;

        svg {
            height: 16px;
        }

        .sun {
            fill: white;
        }

        &.dark-theme {
            transform: rotate(360deg);
            left: calc(100% - 2.3rem);
        }
    }
}
