import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UIRadioComponent } from '../../inputs';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UIRadioComponent> = {
    title: 'Components/Inputs/Radio',
    component: UIRadioComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIRadioComponent,
                UIIconComponent,
                UISVGIconComponent,
                CommonModule,
                FormsModule,
                BrowserModule,
                BrowserAnimationsModule
            ]
        })
    ]
};

export default meta;
type Story = StoryObj<UIRadioComponent>;

const radioOptions: Partial<UIRadioComponent>[] = [
    { label: 'Option 1', id: '0', selected: true },
    { label: 'Option 2', id: '1', selected: false },
    { label: 'Option 3', id: '2', selected: false }
];

export const Radio: Story = {
    render: args => ({
        props: { ...args, radioOptions },
        template: `
            @for (option of radioOptions; track option.id) {
                <ui-radio
                    style="margin-right: 5px"
                    groupId="radioOptionsGroup"
                    [selected]="option.selected"
                    [id]="option.id"
                    [label]="option.label"
                    [withBorder]="withBorder"
                    [disabled]="option.disabled" />
            }
        `
    }),
    args: {
        selected: false,
        id: '1'
    }
};

export const NewRadio: Story = {
    ...NUI_STORY_SETTINGS,
    render: args => ({
        props: { ...args, radioOptions },
        template: `
            @for (option of radioOptions; track option.id) {
                <ui-radio
                    style="margin-right: 16px"
                    groupId="radioOptionsGroup"
                    [selected]="option.selected"
                    [id]="option.id"
                    [label]="option.label"
                    [disabled]="option.disabled" />
            }
        `
    }),
    args: {
        selected: false,
        id: '1'
    }
};

export const NewRadioDisabledStates: Story = {
    ...NUI_STORY_SETTINGS,
    render: args => ({
        props: {
            ...args,
            disabledOptions: [
                { label: 'Enabled & Selected', id: '0', selected: true, disabled: false },
                { label: 'Disabled & Selected', id: '1', selected: true, disabled: true },
                { label: 'Disabled & Unselected', id: '2', selected: false, disabled: true }
            ]
        },
        template: `
            <div style="display: flex; flex-direction: column; gap: 12px;">
                @for (option of disabledOptions; track option.id) {
                    <ui-radio
                        [groupId]="'demo-' + option.id"
                        [selected]="option.selected"
                        [id]="option.id"
                        [label]="option.label"
                        [disabled]="option.disabled" />
                }
            </div>
        `
    }),
    args: {}
};
