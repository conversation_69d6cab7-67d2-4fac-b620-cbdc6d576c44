:where(:root[data-uinew]) :host {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;

    input[type='radio'] {
        position: absolute;
        width: var(--nui-space-500);
        height: var(--nui-space-500);
        margin: 0;
        opacity: 0;
        cursor: pointer;
        z-index: 1;
    }

    .check {
        position: relative;
        width: var(--nui-space-500);
        height: var(--nui-space-500);
        border: var(--nui-border-width-medium) solid var(--nui-label-icon-primary);
        border-radius: 50%;
        background-color: var(--nui-fill-neutral-subtlest);
        transition: all 0.15s ease;
        box-sizing: border-box;

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: var(--nui-space-200);
            height: var(--nui-space-200);
            border-radius: 50%;
            background-color: transparent;
            transform: translate(-50%, -50%) scale(0);
            transition: all 0.15s ease;
        }

        &:active {
            transform: scale(0.98);
        }
    }

    .label {
        margin: 0;
        padding: 0;
        color: var(--nui-text-primary);
        font-family: var(--nui-label-regular-font-family);
        font-size: var(--nui-label-regular-font-size);
        font-weight: var(--nui-label-regular-font-weight);
        line-height: var(--nui-label-regular-line-height);
        letter-spacing: var(--nui-label-regular-letter-spacing);
        padding-left: var(--nui-label-space-gap);
        cursor: pointer;
    }

    input[type='radio']:checked ~ .check {
        border-color: var(--nui-label-icon-primary);

        &::before {
            background-color: var(--nui-label-icon-primary);
            transform: translate(-50%, -50%) scale(1);
        }
    }

    &:hover:not(.disabled) {
        .check {
            border-color: var(--nui-icon-brand);
        }

        .label {
            color: var(--nui-text-brand);
        }

        input[type='radio']:checked ~ .check {
            border-color: var(--nui-icon-brand);

            &::before {
                background-color: var(--nui-icon-brand);
            }
        }
    }

    input[type='radio']:focus-visible ~ .check {
        box-shadow: 0 0 0 var(--nui-border-width-medium) var(--nui-border-system-focus);
    }

    &.disabled {
        cursor: not-allowed;

        input[type='radio'] {
            cursor: not-allowed;
        }

        .check {
            border-color: var(--nui-icon-disabled);

            &:active {
                transform: none;
            }
        }

        .label {
            color: var(--nui-text-disabled);
            cursor: not-allowed;
        }

        input[type='radio']:checked ~ .check {
            background-color: var(--nui-fill-neutral-subtlest);
            border-color: var(--nui-icon-disabled);

            &::before {
                background-color: var(--nui-icon-disabled);
                transform: translate(-50%, -50%) scale(1);
            }
        }

        input[type='radio']:focus-visible ~ .check {
            box-shadow: none;
        }
    }
}
