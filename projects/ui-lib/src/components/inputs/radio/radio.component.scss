@mixin radio_checked_border($border_color, $background_color, $check_color) {
    input[type='radio'] {
        &:checked ~ .check {
            border-color: $border_color;
            background-color: $background_color;

            &::before {
                display: block;
                background-color: $check_color;
            }
        }
    }
}

:where(:root:not([data-uinew])) :host {
    --radio-size: 2rem;
    --radio-check-size: 0.6rem;
    --radio-font-size: inherit;
    --radio-line-height: inherit;

    height: auto;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;

    @include radio_checked_border(
        var(--ui-color-primary),
        var(--ui-color-primary),
        var(--ui-color-white)
    );

    input[type='radio'] {
        position: absolute;
        width: var(--radio-size);
        height: var(--radio-size);
        margin: 0;
        opacity: 0;
        cursor: pointer;
    }

    .label {
        font-weight: var(--ui-font-weight);
        font-size: var(--radio-font-size);
        line-height: var(--radio-line-height);
        padding-left: 1rem;
        cursor: pointer;
    }

    .check {
        position: relative;
        border: var(--ui-border);
        background-color: var(--ui-color-white);
        border-radius: 100%;
        height: var(--radio-size);
        width: var(--radio-size);
        pointer-events: none;

        &::before {
            content: '';
            display: none;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: var(--radio-check-size);
            height: var(--radio-check-size);
            border-radius: 100%;
        }
    }

    &.disabled {
        cursor: default;

        .label {
            color: var(--ui-color-text-disabled);
            cursor: default;
        }

        &:not(:checked) {
            @include radio_checked_border(
                var(--ui-color-border),
                var(--ui-static-color-grey-94),
                var(--ui-color-text-disabled)
            );
        }

        .check {
            background-color: var(--ui-static-color-grey-94);
        }
    }
}
