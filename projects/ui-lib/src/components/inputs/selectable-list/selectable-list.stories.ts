import { CommonModule } from '@angular/common';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { UISelectableListComponent } from './selectable-list.component';
import { UIRadioComponent } from '../radio';
import { UICheckboxComponent } from '../checkbox';
import { UIButtonComponent } from '../../buttons';
import { UISelectableDirective } from './selectable.directive';

interface IOption {
    label: string;
    id: number;
    type: 'directive' | 'radio' | 'checkbox';
}

const SELECT_OPTIONS: IOption[] = [
    { label: 'Option 1', id: 0, type: 'radio' },
    { label: 'Option 2', id: 1, type: 'directive' },
    { label: 'Option 3', id: 2, type: 'checkbox' }
];

const meta: Meta<UISelectableListComponent> = {
    title: 'Components/Inputs/Selectable List',
    component: UISelectableListComponent,
    decorators: [
        moduleMetadata({
            imports: [
                CommonModule,
                UIButtonComponent,
                UISelectableDirective,
                UISelectableListComponent,
                UIRadioComponent,
                UICheckboxComponent
            ]
        })
    ],
    argTypes: {
        selectedChange: { action: 'selectedChange' },
        previewChange: { action: 'previewChange' },
        previewStop: { action: 'previewStop' }
    }
};

export default meta;

type Story = StoryObj<UISelectableListComponent>;

export const SingleSelectList: Story = {
    args: {
        allowUnselected: false,
        multiSelect: false,
        disabled: false
    },
    render: args => ({
        props: {
            ...args,
            options: SELECT_OPTIONS
        },
        template: `
            <ui-selectable-list
                [(selected)]="selectedOption"
                [allowUnselected]="allowUnselected"
                [multiSelect]="multiSelect"
                [disabled]="disabled">

                @for(option of options; track option.id) {
                    <div class="option">
                        @if(option.type === 'radio') {
                            <ui-radio [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'checkbox') {
                            <ui-checkbox [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'directive') {
                            <ui-button
                                #btn="ui-selectable"
                                [ui-selectable]="option"
                                [type]="btn.selected ? 'primary' : 'default'"
                                [text]="btn.selected ? 'Deselect' : 'Select'"/>
                        }
                    </div>
                }
            </ui-selectable-list>
            <p>Selected option: {{ selectedOption?.label }}</p>
        `
    })
};

export const MultiSelectList: Story = {
    args: {
        allowUnselected: true,
        multiSelect: true,
        disabled: false
    },
    render: args => ({
        props: {
            ...args,
            options: SELECT_OPTIONS
        },
        template: `
            <ui-selectable-list
                [(selected)]="selectedOptions"
                [allowUnselected]="allowUnselected"
                [multiSelect]="multiSelect"
                [disabled]="disabled">

                @for(option of options; track option.id) {
                    <div class="option">
                        @if(option.type === 'radio') {
                            <ui-radio [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'checkbox') {
                            <ui-checkbox [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'directive') {
                            <ui-button
                                #btn="ui-selectable"
                                [ui-selectable]="option"
                                [type]="btn.selected ? 'primary' : 'default'"
                                [text]="btn.selected ? 'Deselect' : 'Select'"/>
                        }
                    </div>
                }
            </ui-selectable-list>
            <p>Number of selected options: {{ selectedOptions?.length }}</p>
        `
    })
};

export const DisabledSelectableList: Story = {
    args: {
        allowUnselected: false,
        multiSelect: false,
        disabled: true
    },
    render: args => ({
        props: {
            ...args,
            options: SELECT_OPTIONS
        },
        template: `
            <ui-selectable-list
                [(selected)]="selectedOption"
                [allowUnselected]="allowUnselected"
                [multiSelect]="multiSelect"
                [disabled]="disabled">

                @for(option of options; track option.id) {
                    <div class="option">
                        @if(option.type === 'radio') {
                            <ui-radio [disabled]="disabled" [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'checkbox') {
                            <ui-checkbox [disabled]="disabled" [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'directive') {
                            <ui-button
                                [disabled]="disabled"
                                #btn="ui-selectable"
                                [ui-selectable]="option"
                                [type]="btn.selected ? 'primary' : 'default'"
                                [text]="btn.selected ? 'Deselect' : 'Select'"/>
                        }
                    </div>
                }
            </ui-selectable-list>
            <p>Selected option: {{ selectedOption?.label }}</p>
        `
    })
};

export const GridSelectableList: Story = {
    args: {
        allowUnselected: true,
        multiSelect: true,
        disabled: false,
        grid: { min: '150px', spacing: '10px' }
    },
    render: args => ({
        props: {
            ...args,
            options: SELECT_OPTIONS
        },
        template: `
            <ui-selectable-list
                [(selected)]="selectedOptions"
                [allowUnselected]="allowUnselected"
                [multiSelect]="multiSelect"
                [grid]="grid"
                [disabled]="disabled">

                @for(option of options; track option.id) {
                    <div class="option">
                        @if(option.type === 'radio') {
                            <ui-radio [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'checkbox') {
                            <ui-checkbox [label]="option.label" [value]="option"/>
                        } @else if(option.type === 'directive') {
                            <ui-button
                                #btn="ui-selectable"
                                [ui-selectable]="option"
                                [type]="btn.selected ? 'primary' : 'default'"
                                [text]="btn.selected ? 'Deselect' : 'Select'"/>
                        }
                    </div>
                }
            </ui-selectable-list>
            <p>Number of selected options: {{ selectedOptions?.length }}</p>
        `
    })
};
