import { CommonModule } from '@angular/common';
import { fn } from 'storybook/test';
import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { UIButtonComponent } from '../../buttons';
import { UISVGIconComponent } from '../../icon';
import { UIFileDropzoneComponent } from './file-dropzone.component';

const meta: Meta<UIFileDropzoneComponent> = {
    title: 'Components/Inputs/File Dropzone',
    component: UIFileDropzoneComponent,
    decorators: [
        moduleMetadata({
            imports: [UIFileDropzoneComponent, UIButtonComponent, UISVGIconComponent, CommonModule]
        })
    ],
    argTypes: {
        accept: {
            control: 'text',
            table: { category: 'Inputs' }
        },
        hintText: {
            control: 'text',
            table: { category: 'Inputs' }
        },
        buttonText: {
            control: 'text',
            table: { category: 'Inputs' }
        },
        filesUploaded: {
            action: fn(),
            table: { category: 'Outputs' }
        }
    }
};

export default meta;

type Story = StoryObj<UIFileDropzoneComponent>;

export const Default: Story = {
    args: {
        hintText: 'Drag and drop a file to upload it or select it from your computer',
        buttonText: 'Select File',
        filesUploaded: fn()
    }
};

export const PSDFileType: Story = {
    args: {
        hintText: 'Drag and drop a PSD file here',
        buttonText: 'Select PSD File',
        accept: '.psd',
        filesUploaded: fn()
    }
};

export const ImageFileTypes: Story = {
    args: {
        hintText: 'Drag and drop up to 5 image files (jpg, png, gif etc.)',
        buttonText: 'Select Image(s)',
        accept: 'image/*',
        maxFiles: 5,
        filesUploaded: fn()
    }
};
