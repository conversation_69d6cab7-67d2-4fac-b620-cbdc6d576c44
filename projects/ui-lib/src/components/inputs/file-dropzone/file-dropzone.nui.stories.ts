import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { fn } from 'storybook/test';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIFileDropzoneComponent } from './file-dropzone.component';

const meta: Meta<UIFileDropzoneComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/File Dropzone',
    component: UIFileDropzoneComponent,
    decorators: [moduleMetadata({ imports: [UIFileDropzoneComponent] })],
    parameters: {
        controls: {
            include: ['accept', 'buttonText', 'hintText', 'isError', 'maxFiles']
        }
    },
    argTypes: {
        maxFiles: { control: 'number' }
    },
    args: {
        accept: '*',
        isError: false,
        maxFiles: 1
    }
};

export default meta;

type Story = StoryObj<UIFileDropzoneComponent>;

export const Default: Story = {
    args: {
        hintText: 'Drag and drop a file to upload it or select it from your computer',
        buttonText: 'Select File',
        filesUploaded: fn()
    }
};

export const PSDFileType: Story = {
    args: {
        hintText: 'Drag and drop a PSD file here',
        buttonText: 'Select PSD File',
        accept: '.psd',
        filesUploaded: fn()
    }
};

export const ImageFileTypes: Story = {
    args: {
        hintText: 'Drag and drop up to 5 image files (jpg, png, gif etc.)',
        buttonText: 'Select Image(s)',
        accept: 'image/*',
        maxFiles: 5,
        filesUploaded: fn()
    }
};
