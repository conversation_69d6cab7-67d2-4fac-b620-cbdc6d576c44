@if (isNewUI()) {
    <div
        class="dropzone-area ui-scrollbar"
        [ngClass]="{ 'drag-over': isDragOver(), error: isError() && !isDragOver() }"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)">
        <ui-svg-icon
            icon="none"
            nuiIcon="place_item" />
        <div class="dropzone-content">
            <div class="hint">{{ hintText() }}</div>
            <ui-button
                text="{{ buttonText() }}"
                type="solid-primary"
                nuiSvgIcon="upload"
                [disabled]="isDragOver()"
                (click)="fileInput.click()" />
        </div>
    </div>
} @else {
    <div
        class="dropzone-area ui-scrollbar"
        [ngClass]="{ 'drag-over': isDragOver() }"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)">
        <div class="hint">{{ hintText() }}</div>
        <ui-button
            text="{{ buttonText() }}"
            type="primary"
            svgIcon="upload-file"
            (click)="fileInput.click()" />
    </div>
}

<input
    #fileInput
    type="file"
    [accept]="accept()"
    [multiple]="maxFiles() > 1"
    (change)="onFileChange($event)"
    hidden />
