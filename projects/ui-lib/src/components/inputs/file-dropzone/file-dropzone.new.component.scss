:where(:root[data-uinew]) :host {
    color: var(--nui-text-secondary, #666);
    text-align: center;
    font-size: var(--nui-font-size-275);
    font-weight: var(--nui-font-font-weight-12500);
    line-height: var(--nui-font-line-height-400);
    letter-spacing: 0.14px;
    display: block;

    .dropzone-area {
        display: flex;
        padding: var(--nui-space-800, 32px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: var(--nui-space-1200, 48px);
        flex: 1 0 0;
        border-radius: var(--nui-border-radius-huge, 16px);
        border: 1px dashed var(--nui-border-brand-secondary-bold, #c1cbd9);
        background: var(--nui-surface-neutral-subtler, #f5f5f5);
        height: 100%;

        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        &.drag-over {
            border: 1px dashed var(--nui-border-brand-primary-boldest-hover, #3389fd);
        }

        ui-svg-icon {
            color: var(--nui-icon-secondary);
            opacity: 0.2;

            --nui-icon-font-icon-size: 60px;
        }

        &.error {
            border: 1px dashed var(--forms-border-error, #d63d54);
        }
    }

    .hint {
        text-align: center;
    }
}
