import {
    IUIDialogConfig,
    NEW_UIDIALOG_DEFAULT_CONFIG,
    UIDIALOG_DEFAULT_CONFIG
} from '../dialog/dialog-config.interface';

export interface IUIErrorDialogConfig extends IUIDialogConfig {
    text?: string;
    confirmText?: string;
}

export const UIER<PERSON><PERSON><PERSON>LOG_DEFAULT_CONFIG: Partial<IUIErrorDialogConfig> = {
    ...UIDIALOG_DEFAULT_CONFIG,
    headerText: 'Something went wrong',
    width: 590,
    closeButton: true,
    backdropClickClose: true,
    escKeyClose: true
};

export const NEW_UIERRORDIALOG_DEFAULT_CONFIG: Partial<IUIErrorDialogConfig> = {
    ...NEW_UIDIALOG_DEFAULT_CONFIG,
    headerText: 'Something went wrong',
    width: 590,
    closeButton: true,
    backdropClickClose: true,
    escKeyClose: true
};
