import { Component, computed, HostListener, inject, OnD<PERSON>roy, TemplateRef } from '@angular/core';
import { UIButtonComponent } from '../../buttons';
import { UIDialogButtonsComponent } from '../dialog/dialog-buttons';
import { IUIDialog } from '../dialog/dialog.interface';
import { IUIErrorDialogConfig, UIERRORDIALOG_DEFAULT_CONFIG } from './error-dialog-config.interface';
import { IBFHttpError } from './error.interface';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { computeButtonSize } from '../dialog/dialog.utils';
import { UIBannerComponent } from '../../banner';

@Component({
    imports: [UIButtonComponent, UIDialogButtonsComponent, UIBannerComponent],
    selector: 'ui-error-dialog',
    templateUrl: 'error-dialog.component.html',
    styleUrls: ['./error-dialog.component.scss', './error-dialog.new.component.scss'],
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'config?.size ?? "md"'
    }
})
export class UIErrorDialogComponent implements IUIDialog, OnDestroy {
    private uiNewThemeService = inject(UINewThemeService);

    config: IUIErrorDialogConfig;
    dialogTemplate: TemplateRef<any>;
    error: any;
    closeButtonType = computed(() =>
        this.uiNewThemeService.isNewThemeEnabled() ? 'solid-secondary' : 'default'
    );
    reloadButtonType = computed(() =>
        this.uiNewThemeService.isNewThemeEnabled() ? 'solid-primary-destructive' : 'primary'
    );
    get buttonSize() {
        return computeButtonSize(this.config.size);
    }
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    private resolve: (value: IBFHttpError) => void;
    private confirmPromise?: Promise<IBFHttpError>;

    /**
     * Initiate the error dialog component.
     * Will return a promise which will resolve to true if confirmed by user.
     * If cancelled it will resolve to false.
     * @param error
     */
    public initiate(config: IUIErrorDialogConfig, error: IBFHttpError): Promise<IBFHttpError> {
        this.config = { ...UIERRORDIALOG_DEFAULT_CONFIG, ...config };
        this.error = error;

        this.confirmPromise = new Promise<IBFHttpError>(resolve => {
            this.resolve = resolve;
        });

        return this.confirmPromise;
    }

    /**
     * The user pressed reload
     */
    public reload(): void {
        window.location.reload();
    }

    public close(): void {
        this.resolve({});
    }

    /**
     * Clean up component when removed
     */
    public ngOnDestroy(): void {
        if (this.resolve) {
            this.resolve(this.error);
        }
    }

    @HostListener('window:keyup', ['$event'])
    public keyEvent(event: KeyboardEvent): void {
        if (event.code === 'Enter') {
            this.reload();
        } else if (event.code === 'Escape') {
            this.resolve(this.error);
        }
    }
}
