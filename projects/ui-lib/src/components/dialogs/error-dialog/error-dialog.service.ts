import { Injectable, inject } from '@angular/core';
import { UIDialogService } from '../dialog/dialog.service';
import {
    IUIErrorDialogConfig,
    UIERRORDIALOG_DEFAULT_CONFIG,
    NEW_UIERRORDIALOG_DEFAULT_CONFIG
} from './error-dialog-config.interface';
import { UIErrorDialogComponent } from './error-dialog.component';
import { IBFHttpError } from './error.interface';
import { UINewThemeService } from '../../../services/uinew-theme.service';
@Injectable({ providedIn: 'root' })
export class UIErrorDialogService {
    private dialogService = inject(UIDialogService);
    private uiNewThemeService = inject(UINewThemeService);

    public async show(config: IUIErrorDialogConfig, error: IBFHttpError): Promise<IBFHttpError> {
        const defaultConfig = this.uiNewThemeService.isNewThemeEnabled()
            ? NEW_UIERRORDIALOG_DEFAULT_CONFIG
            : UIERRORDIALOG_DEFAULT_CONFIG;
        const dialogConfig = { ...defaultConfig, ...config };

        const dialogRef = this.dialogService.openComponent(UIErrorDialogComponent, dialogConfig);

        await dialogRef.afterViewInit;

        return await dialogRef.subComponentRef.instance
            .initiate(config, error)
            .then((response: IBFHttpError) => {
                dialogRef.close();
                return response;
            });
    }
}
