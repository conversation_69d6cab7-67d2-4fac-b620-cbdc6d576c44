import { OverlayModule } from '@angular/cdk/overlay';
import { Injector } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';
import { IUIDialogConfig } from '../dialog';
import { UIErrorDialogComponent } from './error-dialog.component';
import { UIErrorDialogService } from './error-dialog.service';

const meta: Meta<UIErrorDialogComponent> = {
    title: 'Components/Dialogs/ErrorDialog',
    component: UIErrorDialogComponent,
    decorators: [
        moduleMetadata({
            imports: [OverlayModule, UIButtonComponent, BrowserAnimationsModule],
            providers: [UIErrorDialogService]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true
    }
};
export default meta;
type CustomTemplate = {
    text: string;
    config: IUIDialogConfig;
    error: any;
};
type Story = StoryObj<UIErrorDialogComponent | CustomTemplate>;

export const ErrorDialog: Story = {
    render: args => ({
        props: {
            ...args,
            showError: (injector: Injector): void => {
                injector
                    .get<UIErrorDialogService>(UIErrorDialogService)
                    .show(args['config'], args['error']);
            }
        },
        template: `
        <ui-button
            [text]="text"
            (click)="showError(injector)">
        </ui-button>
      `
    }),

    args: {
        text: 'Open dialog',
        error: {
            code: 500,
            requestId: '1c36fab2-7139-4dac-9173-27a679a968a0'
        },
        config: {}
    }
};
