import { OverlayModule } from '@angular/cdk/overlay';
import { Injector } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';

import { UIErrorDialogComponent } from './error-dialog.component';
import { UIErrorDialogService } from './error-dialog.service';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UIErrorDialogComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Dialogs/ErrorDialog',
    component: UIErrorDialogComponent,
    decorators: [
        moduleMetadata({
            imports: [OverlayModule, UIButtonComponent, BrowserAnimationsModule],
            providers: [UIErrorDialogService]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true,
        controls: {
            include: ['config', 'error']
        }
    },
    argTypes: {
        config: {
            control: 'object'
        }
    },
    render: args => ({
        props: {
            ...args,
            showError: (injector: Injector): void => {
                injector
                    .get<UIErrorDialogService>(UIErrorDialogService)
                    .show(args['config'], args['error']);
            }
        },
        template: `
        <ui-button
            text="Open dialog"
            (click)="showError(injector)">
        </ui-button>
      `
    })
};
export default meta;

type Story = StoryObj<UIErrorDialogComponent>;

export const ErrorDialog: Story = {
    args: {
        error: {
            code: 500,
            requestId: '1c36fab2-7139-4dac-9173-27a679a968a0'
        },
        config: {
            headerText: 'Error Dialog',
            size: 'md'
        }
    }
};
