import { OverlayModule } from '@angular/cdk/overlay';
import { Injector } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';
import { UIAlertDialogComponent } from './alert-dialog.component';
import { UIAlertDialogService } from './alert-dialog.service';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UIAlertDialogComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Dialogs/AlertDialog',
    component: UIAlertDialogComponent,
    decorators: [
        moduleMetadata({
            imports: [UIButtonComponent, OverlayModule, BrowserAnimationsModule],
            providers: [UIAlertDialogService]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true,
        controls: {
            include: ['config']
        }
    },
    argTypes: {
        config: {
            control: 'object'
        }
    },
    render: args => ({
        props: {
            config: args.config,
            showAlert: (injector: Injector): void => {
                injector.get<UIAlertDialogService>(UIAlertDialogService).show(args['config'] || {});
            }
        },
        template: `
            <ui-button
                text="Open dialog"
                (click)="showAlert(injector)">
            </ui-button>
          `
    })
};

export default meta;

type Story = StoryObj<UIAlertDialogComponent>;

export const AlertDialog: Story = {
    args: {
        config: {
            headerText: 'This is a warning',
            text: 'Important message',
            confirmText: 'I accept',
            size: 'md'
        }
    }
};
