import { Injectable, inject } from '@angular/core';
import { UIAlertDialogComponent } from './alert-dialog.component';
import {
    IUIAlertDialogConfig,
    UIALERTDIALOG_DEFAULT_CONFIG,
    NEW_UIALERTDIALOG_DEFAULT_CONFIG
} from './alert-dialog-config.interface';
import { UIDialogService } from '../dialog/dialog.service';
import { UINewThemeService } from '../../../services/uinew-theme.service';

@Injectable({ providedIn: 'root' })
export class UIAlertDialogService {
    private dialogService = inject(UIDialogService);
    private uiNewThemeService = inject(UINewThemeService);

    /**
     * Open confirm dialog and wait for user to accept or close.
     * @param config
     */
    public async show(config: IUIAlertDialogConfig): Promise<boolean> {
        const defaultConfig = this.uiNewThemeService.isNewThemeEnabled()
            ? NEW_UIALERTDIALOG_DEFAULT_CONFIG
            : UIALERTDIALOG_DEFAULT_CONFIG;
        const dialogConfig = { ...defaultConfig, ...config };

        // Create & open a new dialog
        const dialogRef = this.dialogService.openComponent(UIAlertDialogComponent, dialogConfig);

        // Wait for view to initialize
        await dialogRef.afterViewInit;

        // Wait for user to confirm or not
        const confirm = await dialogRef.subComponentRef.instance.initiate(config);

        // Close after confirmation
        dialogRef.close();

        return confirm;
    }
}
