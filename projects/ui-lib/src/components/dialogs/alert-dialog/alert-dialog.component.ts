import { Component, computed, HostListener, inject, OnD<PERSON>roy, TemplateRef } from '@angular/core';
import { UIButtonComponent } from '../../buttons/button/button.component';
import { UIDialogButtonsComponent } from '../dialog/dialog-buttons/dialog-buttons.component';
import { IUIDialog } from '../dialog/dialog.interface';
import { IUIAlertDialogConfig, UIALERTDIALOG_DEFAULT_CONFIG } from './alert-dialog-config.interface';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { computeButtonSize } from '../dialog/dialog.utils';

@Component({
    imports: [UIDialogButtonsComponent, UIButtonComponent],
    selector: 'ui-alert-dialog',
    styleUrls: ['./alert-dialog.component.scss', './alert-dialog.new.component.scss'],
    templateUrl: './alert-dialog.component.html',
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'config?.size ?? "md"'
    }
})
export class UIAlertDialogComponent implements IUIDialog, OnDestroy {
    private uiNewThemeService = inject(UINewThemeService);
    config: IUIAlertDialogConfig;
    dialogTemplate: TemplateRef<any>;

    private resolve: (value: boolean | PromiseLike<boolean>) => void;
    private confirmPromise: Promise<boolean>;

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    buttonType = computed(() =>
        this.uiNewThemeService.isNewThemeEnabled() ? 'solid-primary' : 'primary'
    );

    get buttonSize() {
        return computeButtonSize(this.config.size);
    }

    /**
     * Initiate the alert dialog component.
     * Will return a promise which will resolve to true if confirmed by user.
     * If cancelled it will resolve to false.
     * @param config
     */
    initiate(config: IUIAlertDialogConfig): Promise<boolean> {
        this.config = { ...UIALERTDIALOG_DEFAULT_CONFIG, ...config };

        // console.log('alert.init', this.config, UIALERTDIALOG_DEFAULT_CONFIG);

        this.confirmPromise = new Promise<boolean>(resolve => {
            this.resolve = resolve!;
        });

        return this.confirmPromise;
    }

    /**
     * The user pressed confirm
     */
    confirm(): void {
        this.resolve(true);
    }

    /**
     * Clean up component when removed
     */
    ngOnDestroy(): void {
        if (this.resolve) {
            this.resolve(false);
        }
    }

    @HostListener('window:keyup', ['$event'])
    public keyEvent(event: KeyboardEvent): void {
        if (event.code === 'Enter') {
            this.confirm();
        } else if (event.code === 'Escape') {
            this.resolve(false);
        }
    }
}
