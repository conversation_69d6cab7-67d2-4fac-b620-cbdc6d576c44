import { OverlayModule } from '@angular/cdk/overlay';
import { Injector } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryFn } from '@storybook/angular';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';
import { UIAlertDialogComponent } from './alert-dialog.component';
import { UIAlertDialogService } from './alert-dialog.service';

export default {
    title: 'Components/Dialogs/AlertDialog',
    component: UIAlertDialogComponent,
    decorators: [
        moduleMetadata({
            imports: [UIButtonComponent, OverlayModule, BrowserAnimationsModule],
            providers: [UIAlertDialogService]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true
    }
} as Meta;

const template: StoryFn = args => ({
    props: {
        ...args,
        showAlert: (injector: Injector): void => {
            injector.get<UIAlertDialogService>(UIAlertDialogService).show(args['config'] || {});
        }
    },
    template: `
        <ui-button
            [text]="text"
            (click)="showAlert(injector)">
        </ui-button>
      `
});

export const AlertDialog: StoryFn = template.bind({});
AlertDialog.args = {
    text: 'Open dialog',
    config: {
        headerText: 'This is a warning',
        text: 'Important message',
        confirmText: 'I accept'
    }
};
