import {
    UIConfirmCloseDialogCallback,
    UIDiscardCloseDialogCallback,
    UISubmitResponse
} from '../../../types';
import {
    IUIDialogConfig,
    NEW_UIDIALOG_DEFAULT_CONFIG,
    UIDIALOG_DEFAULT_CONFIG
} from '../dialog/dialog-config.interface';
import { UINUIIcon } from '../../../types';

export interface UIConfirmDialogConfig extends IUIDialogConfig {
    text?: string;
    cancelText?: string;
    discardText?: string;
    discardIcon?: UINUIIcon;
    onDiscard?: () => Promise<UISubmitResponse<any>>;
    onDiscarded?: (closeDialog: UIDiscardCloseDialogCallback, error?: any) => void;
    confirmText?: string;
    confirmIcon?: UINUIIcon;
    onConfirm?: () => Promise<UISubmitResponse<any>>;
    onConfirmed?: (closeDialog: UIConfirmCloseDialogCallback, error?: any) => void;
    showCancelButton?: boolean;
    __discardable?: boolean;
    isDestructive?: boolean;
}

export const UICONFIRMDIALOG_DEFAULT_CONFIG: Partial<UIConfirmDialogConfig> = {
    ...UIDIALOG_DEFAULT_CONFIG,
    maxWidth: 520,
    text: 'Put your confirm description here.',
    headerText: 'Confirm Dialog',
    discardText: 'Discard',
    cancelText: 'Cancel',
    confirmText: 'Confirm'
};

export const NEW_UICONFIRMDIALOG_DEFAULT_CONFIG: Partial<UIConfirmDialogConfig> = {
    ...NEW_UIDIALOG_DEFAULT_CONFIG,
    maxWidth: 520,
    text: 'Put your confirm description here.',
    headerText: 'Confirm Dialog',
    discardText: 'Discard',
    cancelText: 'Cancel',
    confirmText: 'Confirm'
};
