import { OverlayModule } from '@angular/cdk/overlay';
import { Injector } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';
import { UIConfirmDialogComponent } from './confirm-dialog.component';
import { UIConfirmDialogService } from './confirm-dialog.service';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UIConfirmDialogComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Dialogs/ConfirmDialog',
    component: UIConfirmDialogComponent,
    decorators: [
        moduleMetadata({
            imports: [OverlayModule, UIButtonComponent, BrowserAnimationsModule],
            providers: [UIConfirmDialogService]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true,
        controls: {
            include: ['config']
        }
    },
    argTypes: {
        config: {
            control: 'object'
        }
    },
    render: args => ({
        props: {
            ...args,
            text: 'Confirm',
            confirmDiscard: (injector: Injector): void => {
                injector
                    .get<UIConfirmDialogService>(UIConfirmDialogService)
                    .confirm(args['config'] || {});
            }
        },
        template: `
            <ui-button
                [text]="text"
                (click)="confirmDiscard(injector)">
            </ui-button>
        `
    })
};
export default meta;

type Story = StoryObj<UIConfirmDialogComponent>;

export const ConfirmDialog: Story = {
    args: {
        config: {
            headerText: 'Confirm Dialog',
            text: 'Put your confirm description here.',
            cancelText: 'Cancel',
            confirmText: 'Confirm',
            confirmIcon: 'check',
            size: 'md'
        }
    }
};

export const DestructiveDialog: Story = {
    args: {
        config: {
            headerText: 'Confirm Destructive Dialog',
            text: 'Put your confirm description here.',
            cancelText: 'Cancel',
            confirmText: 'Confirm',
            confirmIcon: 'check',
            size: 'md'
        }
    }
};
