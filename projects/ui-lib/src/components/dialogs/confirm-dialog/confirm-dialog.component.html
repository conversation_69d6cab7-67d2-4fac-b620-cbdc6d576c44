<div
    class="text"
    [innerHTML]="config.text || ''"></div>

@if (config.__discardable) {
    <div>
        @if (config.showCancelButton) {
            <div class="distributed-buttons">
                <div class="left-actions">
                    @if (!config.onDiscard) {
                        <ui-button
                            (click)="resolve('discard')"
                            [type]="discardButtonType()"
                            [size]="buttonSize"
                            [text]="config.discardText"
                            [nuiSvgIcon]="config.discardIcon"></ui-button>
                    }
                    @if (config.onDiscard) {
                        <ui-button
                            [submit]="discard"
                            [done]="onDiscarded"
                            [type]="discardButtonType()"
                            [size]="buttonSize"
                            [text]="config.discardText"
                            [nuiSvgIcon]="config.discardIcon"></ui-button>
                    }
                </div>
                <div class="right-actions">
                    <ui-button
                        (click)="resolve('cancel')"
                        [type]="discardButtonType()"
                        [size]="buttonSize"
                        [text]="config.cancelText"></ui-button>
                    <div class="button-space"></div>
                    @if (!config.onConfirm) {
                        <ui-button
                            [type]="confirmButtonType()"
                            [size]="buttonSize"
                            [text]="config.confirmText"
                            [nuiSvgIcon]="config.confirmIcon"
                            (click)="resolve('confirm')"></ui-button>
                    }
                    @if (config.onConfirm) {
                        <ui-button
                            #confirmButton
                            [submit]="confirm"
                            [done]="onConfirmed"
                            [type]="confirmButtonType()"
                            [size]="buttonSize"
                            [text]="config.confirmText"
                            [nuiSvgIcon]="config.confirmIcon"></ui-button>
                    }
                </div>
            </div>
        }
        @if (!config.showCancelButton) {
            <div class="centered-buttons">
                @if (!config.onDiscard) {
                    <ui-button
                        (click)="resolve('discard')"
                        [type]="discardButtonType()"
                        [size]="buttonSize"
                        [text]="config.discardText"
                        [nuiSvgIcon]="config.discardIcon"></ui-button>
                }
                @if (config.onDiscard) {
                    <ui-button
                        [type]="discardButtonType()"
                        [size]="buttonSize"
                        [submit]="discard"
                        [done]="onDiscarded"
                        [text]="config.discardText"
                        [nuiSvgIcon]="config.discardIcon"></ui-button>
                }
                <div class="button-space"></div>
                @if (!config.onConfirm) {
                    <ui-button
                        [type]="confirmButtonType()"
                        [size]="buttonSize"
                        [text]="config.confirmText"
                        [nuiSvgIcon]="config.confirmIcon"
                        (click)="resolve('confirm')"></ui-button>
                }
                @if (config.onConfirm) {
                    <ui-button
                        #confirmButton
                        [submit]="confirm"
                        [done]="onConfirmed"
                        [type]="confirmButtonType()"
                        [size]="buttonSize"
                        [text]="config.confirmText"
                        [nuiSvgIcon]="config.confirmIcon"></ui-button>
                }
            </div>
        }
    </div>
}

@if (!config.__discardable) {
    <div>
        <ui-dialog-buttons>
            @if (config.cancelText) {
                <ui-button
                    (click)="resolve('cancel')"
                    [type]="discardButtonType()"
                    [size]="buttonSize"
                    [text]="config.cancelText"></ui-button>
            }
            <div class="button-space"></div>
            @if (!config.onConfirm && config.confirmText) {
                <ui-button
                    [type]="confirmButtonType()"
                    [size]="buttonSize"
                    [text]="config.confirmText"
                    [nuiSvgIcon]="config.confirmIcon"
                    (click)="resolve('confirm')"></ui-button>
            }
            @if (config.onConfirm && config.confirmText) {
                <ui-button
                    #confirmButton
                    [submit]="confirm"
                    [done]="onConfirmed"
                    [type]="confirmButtonType()"
                    [size]="buttonSize"
                    [text]="config.confirmText"
                    [nuiSvgIcon]="config.confirmIcon"></ui-button>
            }
        </ui-dialog-buttons>
    </div>
}
