import { Component, computed, inject, OnD<PERSON>roy, TemplateRef, viewChild } from '@angular/core';
import { DATASET_SIZE, UINewThemeService } from '../../../services/uinew-theme.service';
import { UIButtonType, UIConfirmDialogResult, UISubmitResponse } from '../../../types';
import { UIButtonComponent } from '../../buttons/button/button.component';
import { UIDialogButtonsComponent } from '../../dialogs/dialog/dialog-buttons/';
import { IUIDialog } from '../dialog/dialog.interface';
import {
    UICONFIRMDIALOG_DEFAULT_CONFIG,
    UIConfirmDialogConfig
} from './confirm-dialog-config.interface';
import { computeButtonSize } from '../dialog/dialog.utils';

@Component({
    imports: [UIButtonComponent, UIDialogButtonsComponent],
    selector: 'ui-confirm-dialog',
    styleUrls: ['./confirm-dialog.component.scss', './confirm-dialog.new.component.scss'],
    templateUrl: './confirm-dialog.component.html',
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'config?.size ?? "md"'
    }
})
export class UIConfirmDialogComponent implements IUIDialog, OnDestroy {
    readonly confirmButton = viewChild<UIButtonComponent>('confirmButton');
    config: UIConfirmDialogConfig = {};
    dialogTemplate: TemplateRef<any>;
    resolve: (value: UIConfirmDialogResult) => void;
    private confirmPromise: Promise<UIConfirmDialogResult>;
    private uiNewThemeService = inject(UINewThemeService);
    discardButtonType = computed(() => this.computeDiscardButtonType());
    confirmButtonType = computed(() => this.computeConfirmButtonType());

    get buttonSize() {
        return computeButtonSize(this.config.size);
    }

    initiate(config: UIConfirmDialogConfig): Promise<UIConfirmDialogResult> {
        this.config = { ...UICONFIRMDIALOG_DEFAULT_CONFIG, ...config };

        document.addEventListener('keyup', this.onPressEnter);

        this.confirmPromise = new Promise<UIConfirmDialogResult>(resolve => {
            this.resolve = resolve;
        });

        return this.confirmPromise;
    }

    cancel(): void {
        this.resolve('cancel');
    }

    discard = async (): Promise<void> => {
        if (!this.config.onDiscard) {
            throw new Error(`The configuration 'onDiscard' is not set.`);
        }
        await this.config.onDiscard();
    };

    onDiscarded = (error?: any): void => {
        if (this.config.onDiscarded) {
            this.config.onDiscarded((result: 'discard' | 'cancel') => {
                this.resolve(result);
            }, error);
        } else {
            if (!error) {
                this.resolve('discard');
                return;
            }
            throw error;
        }
    };

    confirm = async (): Promise<UISubmitResponse<any>> => {
        if (!this.config.onConfirm) {
            throw new Error(`The configuration 'onConfirm' is not set.`);
        }
        return await this.config.onConfirm();
    };

    onConfirmed = (error?: any): void => {
        if (this.config.onConfirmed) {
            this.config.onConfirmed((result: 'confirm' | 'cancel') => {
                this.resolve(result);
            }, error);
        } else {
            if (!error) {
                this.resolve('confirm');
                return;
            }
            throw error;
        }
    };

    onPressEnter = (event: KeyboardEvent): void => {
        if (event.code === 'Enter') {
            if (this.config.onConfirm) {
                this.confirm();
            } else {
                this.resolve('confirm');
            }
        }
    };

    ngOnDestroy(): void {
        this.resolve('cancel');
        document.removeEventListener('keyup', this.onPressEnter);
    }

    private computeDiscardButtonType(): UIButtonType {
        return this.uiNewThemeService.isNewThemeEnabled() ? 'solid-secondary' : 'default';
    }

    private computeConfirmButtonType(): UIButtonType {
        if (this.uiNewThemeService.isNewThemeEnabled()) {
            if (this.config.isDestructive) {
                return 'solid-primary-destructive';
            }
        }
        return 'primary';
    }
}
