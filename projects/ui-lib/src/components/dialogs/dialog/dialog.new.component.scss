@use '../../../style/mixins';

:where(:root[data-uinew]) :host {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    overflow: hidden;
    width: 100%;
    border-radius: var(--nui-dialog-radius);
    border: var(--nui-dialog-border) solid var(--nui-dialog-border-primary-default);
    background: var(--nui-dialog-fill-primary-default);
    box-shadow: var(--nui-shadow-bold-x, 0) var(--nui-shadow-bold-y, 5px)
        var(--nui-shadow-bold-blur, 20px) var(--nui-shadow-bold-spread, 0)
        var(--nui-shadows-bold, rgba(0, 6, 46, 20%));

    .header {
        padding: var(--nui-dialog-space-padding-vertical) var(--nui-dialog-space-padding-horizontal) 0
            var(--nui-dialog-space-padding-horizontal);
        color: var(--nui-text-primary);
        display: flex;
        align-items: center;
        justify-content: space-between;

        &__close {
            cursor: pointer;
        }
    }

    &[data-uisize='md'],
    &[data-uisize='sm'] {
        .header {
            .header__text {
                font-size: var(--nui-heading-font-size);
                font-weight: var(--nui-heading-font-weight);
                line-height: var(--nui-heading-line-height);
                letter-spacing: var(--nui-heading-letter-spacing);
            }
        }
    }

    &[data-uisize='xs'] {
        .header {
            .header__text {
                font-size: var(--nui-title-font-size);
                font-weight: var(--nui-title-font-weight);
                line-height: var(--nui-title-line-height);
                letter-spacing: var(--nui-title-letter-spacing);
            }
        }
    }

    .body {
        position: relative;
        width: 100%;
        overflow: auto;
        padding: 0 var(--nui-dialog-space-padding-horizontal) var(--nui-dialog-space-padding-vertical)
            var(--nui-dialog-space-padding-horizontal);
    }

    .content {
        position: relative;
        overflow: visible;
        width: 100%;
        height: auto;
        padding-top: var(--nui-dialog-space-gap-content);
        color: var(--nui-dialog-text-body);
        font-size: var(--nui-body-regular-font-size);
        font-weight: var(--nui-body-regular-font-weight);
        line-height: var(--nui-body-regular-line-height);
        letter-spacing: var(--nui-body-regular-letter-spacing);
    }
}

:host-context(.overflow-auto) {
    .body {
        .content {
            overflow: auto;
            height: 100%;
        }
    }
}

:host-context(.inlined-iframe) {
    .body {
        padding: 0;
        height: 100%;

        .content {
            height: 100%;
            overflow: hidden;
        }
    }
}

:host-context(.no-padding) {
    .body {
        padding: 0;
    }
}

:host-context(.no-margin) {
    margin: 0;
}

:host-context(.fullscreen) {
    margin: 2.5rem;

    .body,
    .content {
        height: 100%;
    }
}
