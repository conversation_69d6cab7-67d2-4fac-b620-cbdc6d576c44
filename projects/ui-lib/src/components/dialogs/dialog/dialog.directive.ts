import {
    Directive,
    TemplateRef,
    Input,
    Output,
    ContentChild,
    OnDestroy,
    EventEmitter,
    OnChanges,
    SimpleChanges,
    inject
} from '@angular/core';
import { UIDialogTemplateDirective } from './dialog-template.directive';
import { UIDialogService } from './dialog.service';
import { IUIDialogConfig } from './dialog-config.interface';
import { UIDialogRef } from './dialog-ref';
import { IUIDialog } from './dialog.interface';

@Directive({
    standalone: true,
    // Current lint rules forces directives to be used as attributes
    // TODO: Either change the ruleset or update all the references of this directive
    selector: 'ui-dialog',
    exportAs: 'ui-dialog'
})
export class UIDialogDirective implements OnDestroy, OnChanges, IUIDialog {
    private dialogService = inject(UIDialogService);

    // TODO: Fix so docs can handle double decorators and comments (Adding a JSDoc comment will crash the app)
    // TODO: Skipped for migration because:
    //  This query overrides a field from a superclass, while the superclass field
    //  is not migrated.
    @ContentChild(UIDialogTemplateDirective, { read: TemplateRef })
    dialogTemplate!: TemplateRef<any>;

    /**
     * The options available
     */
    @Input() config: IUIDialogConfig;

    @Output() afterClose: EventEmitter<void> = new EventEmitter();

    private dialogRef?: UIDialogRef;
    private afterClosedSubscriber: any;

    ngOnChanges(changes: SimpleChanges): void {
        if (this.dialogRef && changes['config'] && !changes['config'].firstChange) {
            this.dialogRef.updateConfig(changes['config'].currentValue);
        }
    }

    /**
     * Open this directive
     */
    open(): void {
        if (this.dialogRef) {
            this.dialogRef.destroy();
        }
        this.dialogRef = this.dialogService.openTemplate(this.dialogTemplate, this.config);

        this.afterClosedSubscriber = this.dialogRef.afterClose().subscribe(() => {
            this.afterClose.emit();
        });
    }

    /**
     * Close this directive
     */
    close(): void {
        this.dialogRef!.close();
        this.dialogRef = undefined;
    }

    ngOnDestroy(): void {
        if (this.dialogRef) {
            this.dialogRef.destroy();
        }
        if (this.afterClosedSubscriber) {
            this.afterClosedSubscriber.unsubscribe();
        }
    }
}
