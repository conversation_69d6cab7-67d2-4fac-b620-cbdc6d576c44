export function computeButtonSize(size: 'xs' | 'sm' | 'md' | undefined): 'sm' | 'md' {
    switch (size) {
        case 'md':
            return 'md';
        case 'sm':
            return 'sm';
        case 'xs':
            return 'sm';
        default:
            return 'md';
    }
}

export function computeHeaderTextSize(size: 'xs' | 'sm' | 'md' | undefined): 'xs' | 'sm' | 'md' | 'lg' {
    switch (size) {
        case 'md':
            return 'sm';
        case 'sm':
            return 'xs';
        case 'xs':
            return 'lg';
        default:
            return 'sm';
    }
}
