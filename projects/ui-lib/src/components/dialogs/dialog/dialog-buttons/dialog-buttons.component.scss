:where(:root:not([data-uinew])) :host {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    padding-top: var(--ui-padding);

    &.align-right {
        justify-content: flex-end;
    }

    &.align-left {
        justify-content: flex-start;
    }
}

:where(:root:not([data-uinew])) :host ::ng-deep ui-button {
    flex-shrink: 1;
    flex-basis: 180px;
    margin: 0 calc(var(--ui-padding-small) / 2);

    &:first-child {
        margin-left: 0;
    }

    &:last-child {
        margin-right: 0;
    }
}
