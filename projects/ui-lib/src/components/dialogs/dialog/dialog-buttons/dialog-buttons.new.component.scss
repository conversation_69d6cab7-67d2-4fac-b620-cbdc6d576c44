:where(:root[data-uinew]) :host {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;
    padding-top: var(--nui-dialog-space-gap);

    &.align-right {
        justify-content: flex-end;
    }

    &.align-left {
        justify-content: flex-start;
    }
}

:where(:root[data-uinew]) ::ng-deep ui-button {
    flex-shrink: 1;
    flex-basis: 180px;

    &:first-child {
        margin-left: 0;
    }

    &:last-child {
        margin-right: 0;
    }
}
