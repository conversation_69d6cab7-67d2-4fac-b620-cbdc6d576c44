@use '../../../style/mixins';

$headerHeight: 5rem;
$radius: var(--ui-border-radius);
$padding: 4rem;
$margin: 1.5rem;

:where(:root:not([data-uinew])) :host {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    align-items: stretch;
    box-shadow: var(--ui-shadow);
    border: solid 0.1rem var(--ui-color-border);
    border-radius: $radius;
    padding: 0;
    margin: $margin;
    overflow: hidden;
    width: 100%;
    background-color: var(--ui-color-surface);
    border-color: var(--ui-color-border);
    color: var(--ui-color-text);

    .header {
        display: block;
        width: 100%;
        position: relative;
        line-height: $headerHeight - 0.1rem;
        border-top-left-radius: $radius;
        border-top-right-radius: $radius;
        border-bottom: 0.1rem solid var(--ui-color-border);
        text-align: center;
        font-weight: 600;
        font-size: 1.4rem;
        text-transform: uppercase;
        padding: 0 $padding;
        flex: 0 0 $headerHeight;
        border-color: var(--ui-color-border);
        color: var(--ui-color-text);
        background-color: var(--ui-color-surface-second);

        @include mixins.ui-ellipsis;

        &__close {
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            padding: 0.5rem;
            transition: opacity 0.2s ease;
            flex: 1 1 auto;
            opacity: 0.75;

            &:hover {
                opacity: 1;
            }
        }
    }

    .body {
        padding: $padding;
        position: relative;
        width: 100%;
        overflow: auto;
        max-height: calc(100% - #{$headerHeight});
        min-height: $padding * 2 + 4rem;
        line-height: var(--ui-line-height);
    }

    .content {
        position: relative;
        overflow: visible;
        width: 100%;
        height: auto;

        @include mixins.ui-clearfix;
    }
}

:host-context(.inlined-iframe) {
    .body {
        padding: 0;
        height: 100%;

        .content {
            height: 100%;
            overflow: hidden;
        }
    }
}

:host-context(.no-margin) {
    margin: 0;
}

:host-context(.fullscreen) {
    margin: 2.5rem;

    .body,
    .content {
        height: 100%;
    }
}
