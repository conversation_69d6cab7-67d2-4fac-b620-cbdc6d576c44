<div class="header">
    <span
        class="header__text"
        [uiSize]="headerTextSize()"
        >{{ config.headerText }}</span
    >
    @if (config.closeButton) {
        @if (isNewUI()) {
            <ui-button
                type="ghost-secondary"
                [nuiSvgIcon]="'close'"
                (click)="close()"></ui-button>
        } @else {
            <div
                class="header__close"
                id="interaction-close-dialog"
                (click)="close()">
                <ui-icon icon="close-big" />
            </div>
        }
    }
</div>
<div
    #bodyRef
    class="body"
    [style.padding.rem]="isNewUI() ? undefined : config.padding"
    cdk-scrollable>
    <div
        class="content ui-scrollbar"
        [uiSize]="'md'">
        <ng-template #componentPlaceholder></ng-template>
        @if (dialogRef.templateRef) {
            <ng-container
                *ngTemplateOutlet="dialogRef.templateRef; context: { $implicit: this }"></ng-container>
        }
    </div>
</div>
