import { OverlayModule } from '@angular/cdk/overlay';
import { Injector, TemplateRef } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIButtonComponent } from '../../buttons';
import { IUIDialogConfig } from './dialog-config.interface';
import { UIDialogComponent } from './dialog.component';
import { UIDialogService } from './dialog.service';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UIDialogComponent> = {
    title: 'Components/Dialogs/Dialog',
    component: UIDialogComponent,
    decorators: [
        moduleMetadata({
            imports: [
                OverlayModule,
                UIButtonComponent,
                BrowserAnimationsModule,
                UIComponentSizeDirective
            ],
            providers: [UIDialogService]
        }),
        injectInjectorToProps
    ],
    parameters: {
        injectInjectorToProps: true
    }
};

export default meta;

type CustomTemplate = {
    text: string;
    config: IUIDialogConfig;
    error: any;
};
type Story = StoryObj<UIDialogComponent | CustomTemplate>;

export const Default: Story = {
    render: args => ({
        props: {
            ...args,
            showDialog: (injector: Injector, template: TemplateRef<any>): void => {
                injector.get(UIDialogService).openTemplate(template, {
                    headerText: 'Test dialog',
                    width: '100%',
                    height: '100%',
                    maxWidth: '100%',
                    padding: 0,
                    panelClass: ['no-padding']
                });
            }
        },
        template: `
<ng-template #testDialog>
    <h3 [uiSize]="'sm'"> This is a test </h3>
</ng-template>
<ui-button
    [text]="text"
    (click)="showDialog(injector, testDialog)">
</ui-button>
      `
    }),

    args: {
        text: 'Open dialog',
        error: {
            code: 500,
            requestId: '1c36fab2-7139-4dac-9173-27a679a968a0'
        },
        config: {}
    }
};
