import {
    AfterViewInit,
    Component,
    ElementRef,
    effect,
    input,
    model,
    output,
    signal,
    viewChild
} from '@angular/core';
import { DATASET_SIZE } from '../../services/uinew-theme.service';

@Component({
    selector: 'ui-range',
    templateUrl: './range.component.html',
    styleUrls: ['./range.component.scss', './range.component.new.scss'],
    host: {
        '[class.vertical]': `direction() === 'vertical'`,
        '[class.horizontal]': `direction() === 'horizontal'`,
        '[class.tracklight]': `tracklight()`,
        '[class.disabled]': `disabled()`,
        '[class]': 'size()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()',
        '(mousedown)': 'onMouseDown()',
        '(mouseup)': 'onMouseUp()'
    }
})
export class UIRangeComponent implements AfterViewInit {
    inputRef = viewChild.required<ElementRef<HTMLInputElement>>('inputRef');

    min = input(0);
    max = input(100);
    step = input(1);
    direction = input<'horizontal' | 'vertical'>('horizontal');
    tracklight = input(true);
    disabled = input(false);
    size = input<'xs' | 'sm' | 'md'>('md');
    value = model(0);

    mouseUp = output<number>();
    valueChange = output<number>();
    isMouseDown = output<boolean>();

    currentValue = signal(this.value());

    constructor() {
        effect(() => {
            this.currentValue.set(this.value());
        });

        effect(() => {
            this.currentValue();
            this.min();
            this.max();
            this.tracklight();
            this.updateSliderFillPercentage();
        });
    }

    ngAfterViewInit(): void {
        this.updateSliderFillPercentage();
    }

    onInputValueChange(event: Event): void {
        const target = event.target as HTMLInputElement;
        const newValue = parseFloat(target.value);
        this.currentValue.set(newValue);
        this.valueChange.emit(newValue);
    }

    onMouseDown(): void {
        this.isMouseDown.emit(true);
    }

    onMouseUp(): void {
        this.isMouseDown.emit(false);
    }

    private fillPercentage(): number {
        const min = this.min();
        const max = this.max();
        const currentValue = this.currentValue();

        if (max <= min) {
            return currentValue >= max ? 100 : 0;
        }

        let relativePosition = ((currentValue - min) / (max - min)) * 100;

        relativePosition = Math.max(0, Math.min(100, relativePosition));

        return relativePosition;
    }

    private updateSliderFillPercentage(): void {
        if (this.inputRef()?.nativeElement) {
            const percentageToSet = this.tracklight() ? this.fillPercentage() : 0;
            this.inputRef().nativeElement.style.setProperty(
                '--slider-fill-percentage',
                `${percentageToSet}%`
            );
        }
    }
}
