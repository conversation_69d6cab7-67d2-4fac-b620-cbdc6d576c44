import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { UIRangeComponent } from './range.component';
import { CommonModule } from '@angular/common';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIComponentSizeDirective } from '../../directives';

export default {
    title: 'Components/Range',
    component: UIRangeComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, UIComponentSizeDirective]
        })
    ],
    argTypes: {
        direction: {
            control: { type: 'select' },
            options: ['horizontal', 'vertical']
        },
        value: {
            control: { type: 'number' }
        },
        min: {
            control: { type: 'number' }
        },
        max: {
            control: { type: 'number' }
        },
        step: {
            control: { type: 'number' }
        },
        tracklight: {
            control: { type: 'boolean' }
        },
        disabled: {
            control: { type: 'boolean' }
        },
        valueChange: { action: 'valueChange' },
        mouseUp: { action: 'mouseUp' }
    }
} as Meta<UIRangeComponent>;

type Story = StoryObj<UIRangeComponent>;

export const Horizontal: Story = {
    args: {
        direction: 'horizontal',
        value: 50,
        min: 0,
        max: 100,
        step: 1,
        tracklight: true,
        disabled: false
    }
};

export const Vertical: Story = {
    args: {
        direction: 'vertical',
        value: 25,
        min: 0,
        max: 100,
        step: 1,
        tracklight: true,
        disabled: false
    }
};

export const Disabled: Story = {
    args: {
        direction: 'horizontal',
        value: 75,
        min: 0,
        max: 100,
        step: 1,
        tracklight: true,
        disabled: true
    }
};

export const CustomRangeAndstep: Story = {
    args: {
        direction: 'horizontal',
        value: 5,
        min: 0,
        max: 10,
        step: 0.01,
        tracklight: true,
        disabled: false
    }
};

export const NoTracklight: Story = {
    args: {
        direction: 'horizontal',
        value: 50,
        min: 0,
        max: 100,
        step: 1,
        tracklight: false,
        disabled: false
    }
};

export const NUIRange: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        value: 50,
        min: 0,
        max: 100,
        step: 1,
        tracklight: true,
        disabled: false
    },

    render: args => ({
        args,
        props: args,
        template: `
        <h6 [uiSize]="'md'">Horizontal MD</h6>
        <ui-range
        size="md"
        direction="horizontal"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="disabled"
        />

        <h6 [uiSize]="'md'">Horizontal SM</h6>
        <ui-range
        size="sm"
        direction="horizontal"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="disabled"
        />

        <h6 [uiSize]="'md'">Horizontal XS</h6>
        <ui-range
        size="xs"
        direction="horizontal"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="disabled"
        />

        <h6 [uiSize]="'md'">Horizontal MD Disabled</h6>
        <ui-range
        size="md"
        direction="horizontal"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="true"
        />

        <h6 [uiSize]="'md'"> Horizontal MD no tracklight</h6>
        <ui-range
        size="md"
        direction="horizontal"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="false"
        [disabled]="disabled"
        />

        <h6 [uiSize]="'md'"> Horizontal MD no tracklight, disabled</h6>
        <ui-range
        size="md"
        direction="horizontal"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="false"
        [disabled]="true"
        />

        <h6 [uiSize]="'md'">Vertical MD</h6>
        <ui-range
        size="md"
        direction="vertical"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="disabled"
        />

        <h6 [uiSize]="'md'">Vertical SM</h6>
        <ui-range
        size="sm"
        direction="vertical"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="disabled"
        />

        <h6 [uiSize]="'md'">Vertical XS</h6>
        <ui-range
        size="xs"
        direction="vertical"
        [value]="value"
        [min]="min"
        [max]="max"
        [step]="step"
        [tracklight]="tracklight"
        [disabled]="disabled"
        />
        `
    })
};
