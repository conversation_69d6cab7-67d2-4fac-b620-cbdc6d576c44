:where(:root:not([data-uinew])) :host {
    display: block;
    position: relative;
    cursor: pointer;
    width: 100%;

    // reset slider default styles
    input[type='range'] {
        appearance: none;
        width: 100%;
        background: transparent;
        cursor: pointer;

        --slider-fill-percentage: 0%;
    }

    input[type='range']::-webkit-slider-thumb {
        appearance: none;
    }

    input[type='range']:focus {
        outline: none;
    }

    &.vertical {
        input[type='range'] {
            writing-mode: vertical-lr;

            &::-webkit-slider-runnable-track {
                width: 2px;
                background: var(--ui-color-grey-84);
                border-radius: 1rem;
            }

            &::-webkit-slider-thumb {
                width: 10px;
                height: 10px;
                background: var(--ui-color-background-second);
                border-radius: 100%;
                border: 1px solid var(--ui-color-second);
                margin-left: -4px;
            }
        }
    }

    &.horizontal {
        input[type='range'] {
            height: 10px;

            &::-webkit-slider-runnable-track {
                width: 100%;
                height: 2px;
                background: var(--ui-color-grey-84);
                border-radius: 1rem;
            }

            &::-webkit-slider-thumb {
                width: 10px;
                height: 10px;
                background: var(--ui-color-background-second);
                border-radius: 100%;
                border: 1px solid var(--ui-color-second);
                margin-top: -4px;
            }
        }
    }

    &.tracklight {
        &.horizontal input[type='range'] {
            &::-webkit-slider-runnable-track {
                background: linear-gradient(
                    to right,
                    var(--ui-color-primary) var(--slider-fill-percentage),
                    var(--ui-color-grey-84) var(--slider-fill-percentage)
                );
            }
        }

        &.vertical input[type='range'] {
            &::-webkit-slider-runnable-track {
                background: linear-gradient(
                    to bottom,
                    var(--ui-color-primary) var(--slider-fill-percentage),
                    var(--ui-color-grey-84) var(--slider-fill-percentage)
                );
            }
        }
    }

    &.disabled {
        opacity: var(--default-disabled-opacity);
        pointer-events: none;
    }
}
