:where(:root[data-uinew]) :host {
    display: block;
    position: relative;
    cursor: pointer;
    width: 100%;

    // reset slider default styles
    input[type='range'] {
        appearance: none;
        width: 100%;
        background: transparent;
        cursor: pointer;

        --slider-fill-percentage: 0%;
    }

    input[type='range'] {
        &::-webkit-slider-thumb {
            appearance: none;
        }
    }

    input[type='range']:focus {
        outline: none;
    }

    &.vertical {
        input[type='range'] {
            writing-mode: vertical-lr;

            &::-webkit-slider-runnable-track {
                width: 2px;
                background: var(--nui-range-fill-enabled, #eff0f3);
                border-radius: 1rem;
            }

            &::-webkit-slider-thumb {
                width: var(--nui-range-slider-handle-width, 20px);
                height: var(--nui-range-slider-handle-height, 20px);
                border-radius: var(--nui-border-radius-full, 112px);
                border: 1px solid var(--nui-range-handle-border-default, #cdced7);
                background: var(--nui-range-handle-fill-default, #fff);
            }
        }

        &.md {
            input[type='range'] {
                &::-webkit-slider-thumb {
                    margin-left: -9px;
                }
            }
        }

        &.sm {
            input[type='range'] {
                &::-webkit-slider-thumb {
                    margin-left: -7px;
                }
            }
        }

        &.xs {
            input[type='range'] {
                &::-webkit-slider-thumb {
                    margin-left: -5px;
                }
            }
        }
    }

    &.horizontal {
        input[type='range'] {
            height: 10px;

            &::-webkit-slider-runnable-track {
                width: 100%;
                height: 2px;
                background: var(--nui-range-fill-enabled, #eff0f3);
                border-radius: 1rem;
            }

            &::-webkit-slider-thumb {
                width: var(--nui-range-slider-handle-width, 20px);
                height: var(--nui-range-slider-handle-height, 20px);
                border-radius: var(--nui-border-radius-full, 112px);
                border: 1px solid var(--nui-range-handle-border-default, #cdced7);
                background: var(--nui-range-handle-fill-default, #fff);
            }
        }

        &.md {
            input[type='range'] {
                &::-webkit-slider-thumb {
                    margin-top: -9px;
                }
            }
        }

        &.sm {
            input[type='range'] {
                &::-webkit-slider-thumb {
                    margin-top: -7px;
                }
            }
        }

        &.xs {
            input[type='range'] {
                &::-webkit-slider-thumb {
                    margin-top: -5px;
                }
            }
        }
    }

    &.tracklight {
        &.horizontal {
            input[type='range'] {
                &::-webkit-slider-runnable-track {
                    background: linear-gradient(
                        to right,
                        var(--nui-range-fill-value, #006cfd) var(--slider-fill-percentage),
                        var(--nui-range-fill-enabled, #eff0f3) var(--slider-fill-percentage)
                    );
                }

                &:hover::-webkit-slider-runnable-track {
                    background: linear-gradient(
                        to right,
                        var(--nui-range-fill-value, #006cfd) var(--slider-fill-percentage),
                        var(--nui-range-fill-hover, #b9bbc6) var(--slider-fill-percentage)
                    );
                }
            }

            &.disabled input[type='range'] {
                &::-webkit-slider-runnable-track {
                    background: linear-gradient(
                        to right,
                        var(--nui-range-fill-value-disabled, #b9bbc6) var(--slider-fill-percentage),
                        var(--nui-range-fill-disabled, #eff0f3) var(--slider-fill-percentage)
                    );
                }
            }
        }

        &.vertical {
            input[type='range'] {
                &::-webkit-slider-runnable-track {
                    background: linear-gradient(
                        to bottom,
                        var(--nui-range-fill-value, #006cfd) var(--slider-fill-percentage),
                        var(--nui-range-fill-enabled, #eff0f3) var(--slider-fill-percentage)
                    );
                }

                &:hover::-webkit-slider-runnable-track {
                    background: linear-gradient(
                        to bottom,
                        var(--nui-range-fill-value, #b9bbc6) var(--slider-fill-percentage),
                        var(--nui-range-fill-hover, #b9bbc6) var(--slider-fill-percentage)
                    );
                }
            }

            &.disabled input[type='range'] {
                &::-webkit-slider-runnable-track {
                    background: linear-gradient(
                        to bottom,
                        var(--nui-range-fill-value-disabled, #b9bbc6) var(--slider-fill-percentage),
                        var(--nui-range-fill-disabled, #eff0f3) var(--slider-fill-percentage)
                    );
                }
            }
        }
    }

    &.disabled {
        pointer-events: none;

        input[type='range'] {
            &::-webkit-slider-runnable-track {
                background: var(--nui-range-fill-disabled, #eff0f3);
            }
        }
    }
}
