import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { NUI_STORY_SETTINGS } from '../../storybook/constants';
import { UIDividerComponent } from './divider.component';

const meta: Meta<UIDividerComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Divider',
    component: UIDividerComponent,
    decorators: [moduleMetadata({ imports: [UIDividerComponent] })],
    argTypes: {},
    args: {},
    parameters: {
        controls: {
            include: []
        }
    }
};
export default meta;

type Story = StoryObj<UIDividerComponent>;

export const Playground: Story = {};
