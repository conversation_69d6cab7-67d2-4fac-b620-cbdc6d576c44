import { OverlayModule } from '@angular/cdk/overlay';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { expect, within } from '@storybook/test';
import { Observable, delay, of } from 'rxjs';
import { IUIPopoverConfig } from '../../../types';
import { UISVGIconComponent } from '../../icon';
import {
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverService,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective,
    UITooltipComponent,
    UITooltipDirective,
    UITooltipService
} from '../../popovers';
import { UIBreadcrumbComponent } from './breadcrumb.component';
import { UIBreadcrumbsComponent } from './breadcrumbs.component';

const meta: Meta<UIBreadcrumbsComponent> = {
    title: 'Components/Navigation/Breadcrumbs',
    component: UIBreadcrumbsComponent,
    decorators: [
        moduleMetadata({
            providers: [UIPopoverService, UITooltipService],
            imports: [
                UIBreadcrumbsComponent,
                UIBreadcrumbComponent,
                UISVGIconComponent,
                UIPopoverComponent,
                UIPopoverDirective,
                UIPopoverTargetDirective,
                UIPopoverTemplateDirective,
                UITooltipDirective,
                UITooltipComponent,
                BrowserAnimationsModule,
                OverlayModule
            ]
        })
    ],
    parameters: {
        actions: {
            handles: ['click ui-breadcrumb']
        }
    }
};
export default meta;
type Story = StoryObj<UIBreadcrumbComponent>;

export const Breadcrumbs: Story = {
    render: args => ({
        props: args,
        template: `
            <ui-breadcrumbs [maxItemWidth]="maxItemWidth">
                <ui-breadcrumb>First</ui-breadcrumb>
                <ui-breadcrumb>Second</ui-breadcrumb>
                <ui-breadcrumb>Third Very Loooooooooooooooong</ui-breadcrumb>
                <ui-breadcrumb>Fourth</ui-breadcrumb>
            </ui-breadcrumbs>
        `,
        styles: [
            `
            ui-breadcrumbs {
                margin-top: 4rem;
            }
            ui-breadcrumbs ::ng-deep ui-breadcrumb a {
                color: inherit;
                text-decoration: none;
            }
        `
        ]
    }),
    parameters: { controls: { include: ['maxItemWidth'] } },
    play: async ({ canvasElement }) => {
        const canvas = within(canvasElement);

        const breadcrumbs = await canvas.findByTestId('breadcrumbs-container');
        const first = await canvas.findByText('First');
        const second = await canvas.findByText('Second');
        const third = await canvas.findByText('Third Very Loooooooooooooooong');
        const fourth = await canvas.findByText('Fourth');

        expect(breadcrumbs).toContainElement(first);
        expect(breadcrumbs).toContainElement(second);
        expect(breadcrumbs).toContainElement(third);
        expect(breadcrumbs).toContainElement(fourth);
    }
};

export const NuiBreadcrumbs: Story = {
    tags: ['Nui'],
    globals: {
        rootAttributes: {
            'data-uinew': 'true'
        }
    },
    argTypes: {
        type: {
            control: 'select',
            options: ['primary', 'secondary'],
            defaultValue: 'primary',
            name: 'Type'
        },
        size: {
            control: 'select',
            options: ['md', 'sm'],
            defaultValue: 'md',
            name: 'Size'
        }
    },
    args: {
        type: 'secondary',
        size: 'md'
    },
    render: args => ({
        props: args,
        template: `
            <ui-breadcrumbs [maxItemWidth]="maxItemWidth" [type]="type" [size]="size">
                <ui-breadcrumb>First</ui-breadcrumb>
                <ui-breadcrumb>Second</ui-breadcrumb>
                <ui-breadcrumb>Third Very Loooooooooooooooong</ui-breadcrumb>
                <ui-breadcrumb>Fourth</ui-breadcrumb>
            </ui-breadcrumbs>
        `
    })
};

type FlexContainerStory = StoryObj<{
    breadcrumbs: string[];
    storyContainerDisplay: string;
    storyContainerWidth: number;
    storyBreadcrumbs: string[];
    popoverConfig: IUIPopoverConfig;
}>;
export const FlexContainer: FlexContainerStory = {
    render: args => ({
        props: {
            ...args,
            storyBreadcrumbs: Array(20)
                .fill('Crumb')
                .map((item, index) => item + index),
            popoverConfig: {
                arrowPosition: 'top',
                position: 'bottom',
                panelClass: 'ui-breadcrumbs-popover',
                width: '200px'
            }
        },
        styles: [
            `.container {
                margin: 0 auto;
                background: #fff;
                height: 400px;
            }`
        ],
        template: `
            <div class="container" style="display: {{storyContainerDisplay}}; width: {{storyContainerWidth}}px;">
                <!-- setting style flex:1 is needed inside a flex container for breadcrumbs to grow on resize -->
                <ui-breadcrumbs style="flex:1;" [popoverConfig]="popoverConfig" [maxItemWidth]="maxItemWidth">
                    <ui-breadcrumb *ngFor="let crumb of storyBreadcrumbs">
                        {{crumb}} {{i}}
                    </ui-breadcrumb>
                    <ui-breadcrumb>Last Item</ui-breadcrumb>
                </ui-breadcrumbs>
            </div>`
    }),
    parameters: {
        controls: {
            include: [
                'storyBreadcrumbs',
                'storyContainerDisplay',
                'storyContainerWidth',
                'maxItemWidth',
                'popoverConfig'
            ]
        }
    },
    argTypes: {
        storyContainerWidth: {
            control: { type: 'number' },
            type: 'number'
        },
        storyContainerDisplay: {
            options: ['flex', 'block', 'inline-block', 'inline-flex', 'none'],
            control: { type: 'select' },
            type: 'string'
        }
    },
    args: {
        storyContainerWidth: 700,
        storyContainerDisplay: 'flex'
    },
    play: async ({ canvasElement }) => {
        const canvas = within(canvasElement);

        const breadcrumbs = await canvas.findByTestId('breadcrumbs-container');
        const collapsedIndicator = await canvas.findByTestId('breadcrumbs-collapsed-indicator');
        expect(breadcrumbs).toContainElement(collapsedIndicator);
    }
};

type WithAsyncPipeStory = StoryObj<{
    breadcrumbs: string[];
    asyncCrumbs: Observable<string[]>;
}>;

const breadcrumbs = Array(20)
    .fill('Async')
    .map((item, index) => item + index);

export const WithAsyncPipe: WithAsyncPipeStory = {
    parameters: {
        controls: { include: ['breadcrumbs'] }
    },
    render: args => ({
        props: {
            ...args,
            breadcrumbs,
            asyncCrumbs: of(breadcrumbs).pipe(delay(1000))
        },
        template: `
            <ui-breadcrumbs>
                <ui-breadcrumb *ngFor="let crumb of asyncCrumbs | async">
                    {{crumb}}
                </ui-breadcrumb>
            </ui-breadcrumbs>
        `
    })
};
