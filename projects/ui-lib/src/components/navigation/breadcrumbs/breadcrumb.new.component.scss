:where(:root[data-uinew]) :host {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--nui-breadcrumbs-item-space-gap);
    border-radius: var(--nui-breadcrumbs-item-radius-crumb);

    &.hidden {
        display: none;
    }

    &.truncated,
    &.hidden,
    &:last-child {
        cursor: default;

        .content {
            font-family: var(--nui-label-bold-font-family);
            font-size: var(--nui-label-bold-font-size);
            font-style: normal;
            font-weight: var(--nui-label-bold-font-weight);
            line-height: var(--nui-label-bold-line-height);
            letter-spacing: var(--nui-label-bold-letter-spacing);
        }
    }

    span.content {
        display: flex;
        align-items: center;
        gap: var(--nui-space-100, 4px);
        border-radius: var(--nui-breadcrumbs-item-radius-crumb);
        font-family: var(--nui-label-regular-font-family);
        font-size: var(--nui-label-regular-font-size);
        font-style: normal;
        font-weight: var(--nui-label-regular-font-weight);
        line-height: var(--nui-label-regular-line-height);
        letter-spacing: var(--nui-label-regular-md-letter-spacing);
    }

    .separator {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &.primary {
        .content {
            padding: var(--nui-breadcrumbs-item-space-padding-vertical-crumb)
                var(--nui-breadcrumbs-item-space-padding-horizontal-crumb);
            background: var(--nui-breadcrumb-fill-primary-default);
            color: var(--nui-breadcrumb-text-primary-default);
        }

        .separator {
            color: var(--nui-breadcrumb-icon-chevron-primary-default);
        }

        &:hover {
            .content {
                background: var(--nui-breadcrumb-fill-primary-hover);
                color: var(--nui-breadcrumb-text-primary-hover);
            }

            .separator {
                color: var(--nui-breadcrumb-icon-chevron-primary-hover);
            }
        }

        &:last-child {
            .content {
                background: none;
                color: var(--nui-breadcrumb-text-primary-last);
            }

            .separator {
                display: none;
                color: var(--nui-breadcrumb-icon-chevron-primary-last);
            }
        }
    }

    &.secondary {
        .content {
            background: var(--nui-breadcrumb-fill-secondary-default);
            color: var(--nui-breadcrumb-text-secondary-default);
        }

        .separator {
            color: var(--nui-breadcrumb-icon-chevron-secondary-default);
        }

        &:hover {
            .content {
                background: var(--nui-breadcrumb-fill-secondary-hover);
                color: var(--nui-breadcrumb-text-secondary-hover);
            }

            .separator {
                color: var(--nui-breadcrumb-icon-chevron-secondary-hover);
            }
        }

        &:last-child {
            .content {
                background: none;
                color: var(--nui-breadcrumb-text-secondary-last);
            }

            .separator {
                display: none;
                color: var(--nui-breadcrumb-icon-chevron-secondary-last);
            }
        }
    }

    &.truncated,
    &.hidden {
        cursor: default;

        .content {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            background: none;
        }

        &:hover {
            .content {
                background: none;
            }
        }
    }
}
