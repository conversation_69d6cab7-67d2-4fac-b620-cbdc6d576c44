:where(:root:not([data-uinew])) {
    :host {
        --separator-icon-offset-bottom: 0;

        display: inline-flex;
        align-items: center;
        font-size: 2rem;
        color: var(--ui-color-text-second);
        line-height: var(--ui-line-height);
        transition: color 0.2s;
        cursor: pointer;

        &:hover {
            color: var(--ui-color-text);
        }

        &:last-child {
            font-weight: bold;
            color: var(--ui-color-text);
            cursor: default;

            .separator {
                display: none;
            }
        }

        &.truncated {
            .content {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .content {
        display: inline-block;
    }

    .separator {
        color: var(--ui-color-text-second);
        font-size: var(--font-size);
        bottom: var(--separator-icon-offset-bottom);
    }
}
