import { NgStyle } from '@angular/common';
import {
    Component,
    ElementRef,
    HostBinding,
    Input,
    TemplateRef,
    inject,
    input,
    viewChild
} from '@angular/core';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UISVGIconComponent } from '../../icon/svg-icon';
import { UITooltipDirective } from '../../popovers/tooltip/tooltip.directive';

@Component({
    imports: [NgStyle, UITooltipDirective, UISVGIconComponent],
    selector: 'ui-breadcrumb',
    templateUrl: './breadcrumb.component.html',
    styleUrls: ['./breadcrumb.component.scss', './breadcrumb.new.component.scss'],
    host: {
        '[class]': 'type()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIBreadcrumbComponent {
    type = input<'primary' | 'secondary'>('primary');
    size = input<'sm' | 'md'>('md');

    elementRef = inject<ElementRef<HTMLElement>>(ElementRef);

    readonly template = viewChild<TemplateRef<any>>('template');

    @Input() maxTruncationWidth = 200;

    @HostBinding('class.truncated') get truncated(): boolean {
        return this.elementRef.nativeElement.offsetWidth >= this.maxTruncationWidth;
    }
    collapsed = false;
}
