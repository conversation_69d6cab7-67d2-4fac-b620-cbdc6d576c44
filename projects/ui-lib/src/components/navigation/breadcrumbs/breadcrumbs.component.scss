:where(:root:not([data-uinew])) {
    :host {
        --font-size: 2rem;
        --color: var(--ui-color-text);
        --hover-color: var(--ui-color-text);
        --last-child-color: var(--ui-color-text);
        --separator-offset-bottom: -2px;

        display: block;
        position: relative;
        overflow: hidden;

        .breadcrumbs-container {
            display: inline;
            white-space: nowrap;
        }

        ui-breadcrumb {
            --separator-icon-offset-bottom: var(--separator-offset-bottom);

            font-size: var(--font-size);
            color: var(--color);

            &.hidden {
                display: none;
                width: 0;
            }

            &:hover {
                color: var(--hover-color);
            }

            &:last-child {
                color: var(--last-child-color);
            }
        }
    }

    // popopver :(
    ::ng-deep .ui-breadcrumbs-popover {
        .content {
            padding: 0.8rem !important;
            line-height: var(--ui-line-height);
        }

        .popover-breadcrumb {
            font-size: 1.4rem;
            padding: 0.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
