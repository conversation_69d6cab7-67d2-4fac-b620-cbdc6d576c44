import { NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ContentChildren,
    effect,
    ElementRef,
    inject,
    Input,
    input,
    OnD<PERSON>roy,
    QueryList,
    viewChild,
    viewChildren
} from '@angular/core';
import {
    animationFrames,
    debounceTime,
    delay,
    merge,
    Observable,
    Subject,
    take,
    takeUntil
} from 'rxjs';
import { DATASET_SIZE } from '../../../services';
import { IUIPopoverConfig } from '../../../types';
import {
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective
} from '../../popovers';
import { UIBreadcrumbComponent } from './breadcrumb.component';
/**
 * Auto collapsing breadcrumb container that works with content projection and supports async pipe.
 *
 * Takes @see `UIBreadcrumbComponent` as content children and displays them with ngFor
 * by using `<ng-template>` references and `*ngTemplateOutlet`
 *
 * inspired by {@link https://github.com/angular/components/tree/main/src/material/tabs angular material tabs}
 *
 * @usage
 * ```
 * <ui-breadcrumbs>
 *      <ui-breadcrumb *ngFor="let crumb of manyCrumbs">{{crumb}}
 *      </ui-breadcrumb>
 *  </ui-breadcrumbs>
 * ```
 *
 * @version v1
 *  - auto collapse by parent width
 *  - recalculate collapse on resize
 *  - display collapsed items in popover
 *  - children can truncate and show tooltip if collapsed
 */
@Component({
    imports: [
        NgTemplateOutlet,
        UIBreadcrumbComponent,
        UIPopoverTargetDirective,
        UIPopoverDirective,
        UIPopoverTemplateDirective
    ],
    selector: 'ui-breadcrumbs',
    templateUrl: './breadcrumbs.component.html',
    styleUrls: ['./breadcrumbs.component.scss', './breadcrumbs.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class]': 'type()',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIBreadcrumbsComponent implements AfterViewInit, OnDestroy {
    private elementRef = inject<ElementRef<HTMLElement>>(ElementRef);
    private cdRef = inject(ChangeDetectorRef);

    type = input<'primary' | 'secondary'>('primary');
    size = input<'sm' | 'md'>('md');

    // TODO: Skipped for migration because:
    //  There are references to this query that cannot be migrated automatically.
    @ContentChildren(UIBreadcrumbComponent)
    contentChildrenBreadcrumbs: QueryList<UIBreadcrumbComponent>;
    readonly viewChildrenBreadcrumbs = viewChildren(UIBreadcrumbComponent);
    readonly breadcrumbsContainer = viewChild('breadcrumbsContainer', {
        read: ElementRef<HTMLElement>
    });
    readonly breadcrumbsPopover = viewChild<UIPopoverComponent>('popover');

    @Input() maxItemWidth = 200;
    @Input() popoverConfig: IUIPopoverConfig = {
        arrowPosition: 'top',
        position: 'bottom',
        panelClass: 'ui-breadcrumbs-popover',
        width: '200px',
        openOnHover: true,
        size: 'sm'
    };

    isCollapsed = false;
    ngDestroy$ = new Subject<void>();

    constructor() {
        effect(() => {
            this.type();
            this.size();
            // TODO:  this is needed because we use ChangeDetectionStrategy.OnPush
            // Maybe remove it in the future
            this.cdRef.detectChanges();
        });
    }

    ngAfterViewInit(): void {
        merge(
            // trying to get measured width after layout is drawn
            animationFrames().pipe(take(1)),
            // subscribes to changes in ng-content children to work with async pipe
            this.contentChildrenBreadcrumbs.changes.pipe(delay(1)),
            // observes resize events on host element
            this.observeResize(this.elementRef.nativeElement).pipe(debounceTime(100))
        )
            .pipe(takeUntil(this.ngDestroy$))
            .subscribe(() => {
                this.tryCollapse();
            });
    }

    private observeResize(nativeElement: HTMLElement): Observable<HTMLElement> {
        return new Observable<HTMLElement>(subscriber => {
            const ro = new ResizeObserver(() => {
                subscriber.next(nativeElement);
            });

            ro.observe(nativeElement);

            return () => {
                ro.disconnect();
            };
        });
    }

    ngOnDestroy(): void {
        this.ngDestroy$.next();
        this.ngDestroy$.complete();
    }

    private tryCollapse(): void {
        // resets the collapsed state for expanding on resize
        this.resetCollapsed();
        this.cdRef.detach();

        const containerElement: HTMLElement | undefined = this.breadcrumbsContainer()?.nativeElement;
        if (!containerElement) {
            return;
        }
        const hostWidth = containerElement.offsetWidth;
        const parentElement = containerElement.parentElement;

        // if there is no parent element we can't auto collapse
        // if there are no collapsible elements we can't auto collapse
        // if we can't get host width we can't auto collapse
        if (!parentElement || this.contentChildrenBreadcrumbs.length === 0 || hostWidth === 0) {
            return;
        }

        // if host width is less then parent width than everything fits, no need to collapse
        const parentWidth = parentElement.clientWidth;
        if (hostWidth < parentWidth) {
            return;
        }

        // we injected the first breadcrumb twice but we only want to measure it once
        // we injected collapse indicator so we need to adjust the width
        let currentWidth = hostWidth;

        // loops through template references to measure how many items we should collapse until we fit into the parent
        this.contentChildrenBreadcrumbs.forEach((collapsible, index) => {
            // gets the actual view child element because we wrap our template refs with ui-breadcrumb
            const breadcrumbWidth = this.getBreadcrumbWidth(index);

            // if it is first or last breadcrumb we don't collapse
            const isFirstBreadcrumb = collapsible === this.contentChildrenBreadcrumbs.first;
            const isLastBreadcrumb = collapsible === this.contentChildrenBreadcrumbs.last;
            if (isFirstBreadcrumb || isLastBreadcrumb) {
                return;
            }

            if (currentWidth > parentWidth) {
                currentWidth = currentWidth - breadcrumbWidth;
                collapsible.collapsed = true;
            }
        });

        this.isCollapsed = this.contentChildrenBreadcrumbs.some(breadcrumb => breadcrumb.collapsed);

        this.cdRef.reattach();
        this.cdRef.detectChanges();
    }

    private getBreadcrumbWidth(index: number): number {
        const children = this.viewChildrenBreadcrumbs();
        return children?.[index]?.elementRef.nativeElement.offsetWidth || 0;
    }

    private resetCollapsed(): void {
        this.isCollapsed = false;
        this.contentChildrenBreadcrumbs.forEach(c => (c.collapsed = false));
        this.cdRef.detectChanges();
    }

    onBreadcrumbClicked(breadcrumb: UIBreadcrumbComponent): void {
        breadcrumb.elementRef.nativeElement.click();
        this.breadcrumbsPopover()?.close();
    }
}
