<div
    #breadcrumbsContainer
    class="breadcrumbs-container"
    data-testid="breadcrumbs-container">
    @if (contentChildrenBreadcrumbs?.first; as first) {
        <ui-breadcrumb
            [type]="type()"
            [size]="size()"
            [maxTruncationWidth]="maxItemWidth"
            data-testid="breadcrumb-0"
            (click)="onBreadcrumbClicked(first)">
            @if (first.template(); as firstTemplate) {
                <ng-container
                    inContainerView
                    *ngTemplateOutlet="firstTemplate"></ng-container>
            }
        </ui-breadcrumb>
    }

    @if ((contentChildrenBreadcrumbs?.length ?? 0) > 1) {
        <ui-breadcrumb
            [class.hidden]="!isCollapsed"
            [type]="type()"
            [size]="size()"
            data-testid="breadcrumbs-collapsed-indicator">
            <ng-container inContainerView>
                <div
                    ui-popover-target
                    #target="ui-popover-target"
                    (mouseover)="popover.open(target)">
                    ...
                </div>
            </ng-container>
        </ui-breadcrumb>
    }

    @for (breadcrumb of contentChildrenBreadcrumbs ?? []; track breadcrumb; let i = $index) {
        @if (!breadcrumb.collapsed && breadcrumb !== contentChildrenBreadcrumbs.first) {
            <ui-breadcrumb
                [type]="type()"
                [size]="size()"
                [maxTruncationWidth]="maxItemWidth"
                [attr.data-testid]="'breadcrumb-' + i"
                (click)="onBreadcrumbClicked(breadcrumb)">
                @if (breadcrumb.template(); as breadcrumbTemplate) {
                    <ng-container
                        inContainerView
                        *ngTemplateOutlet="breadcrumbTemplate"></ng-container>
                }
            </ui-breadcrumb>
        }
    }
</div>

<ui-popover
    #popover="ui-popover"
    [config]="popoverConfig">
    <ng-template ui-popover-template>
        @for (breadcrumb of contentChildrenBreadcrumbs ?? []; track breadcrumb; let i = $index) {
            @if (breadcrumb.collapsed) {
                <div
                    class="popover-breadcrumb"
                    data-testid="breadcrumbs-collapsed-container"
                    (click)="onBreadcrumbClicked(breadcrumb)">
                    @if (breadcrumb.template(); as breadcrumbTemplate) {
                        <ng-container *ngTemplateOutlet="breadcrumbTemplate"></ng-container>
                    }
                </div>
            }
        }
    </ng-template>
</ui-popover>
