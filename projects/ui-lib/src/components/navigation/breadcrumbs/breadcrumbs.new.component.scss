:where(:root[data-uinew]) {
    :host {
        display: block;
        position: relative;
        overflow: hidden;

        .breadcrumbs-container {
            display: flex;
            white-space: nowrap;
        }

        ui-breadcrumb .hidden {
            display: none;
            width: 0;
        }
    }
}

// popopver :(
::ng-deep :where(:root[data-uinew]) .ui-breadcrumbs-popover {
    .content {
        padding: var(--nui-space-100) var(--nui-space-200);
    }

    .popover-breadcrumb {
        font-size: var(--nui-label-regular-font-size);
        color: var(--nui-tooltip-text-default);
        word-wrap: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }
}
