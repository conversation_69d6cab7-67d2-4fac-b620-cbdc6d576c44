import { Component, input, output, signal, computed } from '@angular/core';
import { UISVGIconComponent } from '../../icon/svg-icon/svg-icon.component';
import { UILabelComponent } from '../../label/label.component';
import { UIButtonComponent } from '../../buttons/button/button.component';
import { UINUIIcon } from '../../../types/icons';

export interface SidebarNavigationItem {
    id: string;
    label: string;
    nuiIcon: UINUIIcon;
    active?: boolean;
    htmlId?: string; // Optional HTML id for tracking (e.g., Gainsight)
}

@Component({
    selector: 'ui-sidebar',
    imports: [UISVGIconComponent, UILabelComponent, UIButtonComponent],
    templateUrl: './sidebar.component.html',
    styleUrl: './sidebar.component.scss'
})
export class UISidebarComponent {
    /**
     * Navigation items to display
     */
    navigationItems = input<SidebarNavigationItem[]>([]);

    /**
     * Active item ID (for controlled mode)
     */
    activeItemId = input<string | undefined>(undefined);

    /**
     * Emitted when the toggle button is clicked
     */
    onExpand = output<boolean>();

    /**
     * Emitted when a navigation item is clicked
     */
    navigationItemClick = output<SidebarNavigationItem>();

    /**
     * Internal state for sidebar expansion
     */
    private internalExpanded = signal(false);

    /**
     * Internal state for active navigation item
     */
    private internalActiveItemId = signal<string | null>('creative-sets');

    /**
     * Current expanded state - uses internal state, can be overridden by input for initial state
     */
    isExpanded = computed(() => this.internalExpanded());

    /**
     * Computed property that determines the current active item ID
     * Uses controlled mode if activeItemId input is provided, otherwise uses internal state
     */
    currentActiveItemId = computed(() => {
        const controlledValue = this.activeItemId();
        return controlledValue !== undefined ? controlledValue : this.internalActiveItemId();
    });

    /**
     * Default navigation items
     */
    readonly defaultNavigationItems: SidebarNavigationItem[] = [
        {
            id: 'creative-sets',
            label: 'Creative sets',
            nuiIcon: 'dashboard',
            htmlId: 'interaction-navigate-to-creative-sets'
        },
        {
            id: 'campaigns',
            label: 'Campaigns',
            nuiIcon: 'rocket_launch',
            htmlId: 'interaction-navigate-to-campaign-list'
        },
        {
            id: 'analytics',
            label: 'Analytics',
            nuiIcon: 'monitoring',
            htmlId: 'interaction-navigate-to-analytics'
        },
        {
            id: 'feeds',
            label: 'Feeds',
            nuiIcon: 'rss_feed',
            htmlId: 'interaction-navigate-to-feeds'
        },
        {
            id: 'dynamic-ads',
            label: 'Dynamic advertising',
            nuiIcon: 'dynamic_feed',
            htmlId: 'interaction-navigate-to-social-dynamic-advertising'
        },
        {
            id: 'landing-pages',
            label: 'Landing pages',
            nuiIcon: 'web',
            htmlId: 'interaction-navigate-to-landing-pages'
        },
        {
            id: 'trash',
            label: 'Trash',
            nuiIcon: 'delete',
            htmlId: 'interaction-navigate-to-trash'
        }
    ];

    /**
     * Computed navigation items with active state applied
     */
    getNavigationItems = computed(() => {
        const navItems = this.navigationItems();
        const items = navItems && navItems.length > 0 ? navItems : this.defaultNavigationItems;
        const activeId = this.currentActiveItemId();

        return items.map(item => ({
            ...item,
            active: item.id === activeId
        }));
    });

    /**
     * Handle toggle button click
     */
    onToggleClick(): void {
        const currentState = this.isExpanded();
        const newExpandedState = !currentState;

        // Update internal state
        this.internalExpanded.set(newExpandedState);

        this.onExpand.emit(newExpandedState);
    }

    /**
     * Handle navigation item click
     */
    onNavigationItemClick(item: SidebarNavigationItem): void {
        // If this is uncontrolled mode (no activeItemId input provided), update internal state
        if (this.activeItemId() === undefined) {
            this.internalActiveItemId.set(item.id);
        }

        this.navigationItemClick.emit(item);
    }
}
