import type { Meta, StoryObj } from '@storybook/angular';
import { UISidebarComponent } from './sidebar.component';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UISidebarComponent> = {
    title: 'NUI/Sidebar',
    ...NUI_STORY_SETTINGS,
    component: UISidebarComponent,
    argTypes: {
        navigationItems: {
            control: 'object',
            description: 'Navigation items to display'
        }
    }
};

export default meta;
type Story = StoryObj<UISidebarComponent>;

export const Default: Story = {
    args: {
        // Starts collapsed by default
    },
    render: args => ({
        props: args,
        template: `
            <div style="height: 900px; display: flex; align-items: stretch; background: #8496b31a;">
                <ui-sidebar></ui-sidebar>
            </div>
        `
    })
};

export const CustomItemsWithActiveItem: Story = {
    args: {
        navigationItems: [
            {
                id: 'creative-sets',
                label: 'Creative Sets',
                nuiIcon: 'dashboard',
                active: true
            },
            {
                id: 'campaigns',
                label: 'Campaigns',
                nuiIcon: 'rocket_launch'
            },
            {
                id: 'analytics',
                label: 'Analytics',
                nuiIcon: 'monitoring'
            },
            {
                id: 'feeds',
                label: 'Feeds',
                nuiIcon: 'rss_feed'
            },
            {
                id: 'landing-pages',
                label: 'Landing pages',
                nuiIcon: 'web'
            },
            {
                id: 'trash',
                label: 'Trash',
                nuiIcon: 'delete'
            }
        ]
    },
    render: args => ({
        props: args,
        template: `
            <div style="height: 900px; display: flex; align-items: stretch; background: #8496b31a;">
                <ui-sidebar [navigationItems]="navigationItems"></ui-sidebar>
            </div>
        `
    })
};

export const CustomItems: Story = {
    args: {
        navigationItems: [
            {
                id: 'dashboard',
                label: 'Dashboard',
                nuiIcon: 'dashboard',
                htmlId: 'interaction-navigate-to-dashboard'
            },
            {
                id: 'projects',
                label: 'Projects',
                nuiIcon: 'folder',
                htmlId: 'interaction-navigate-to-projects'
            },
            {
                id: 'settings',
                label: 'Settings',
                nuiIcon: 'settings',
                htmlId: 'interaction-navigate-to-settings'
            }
        ]
    },
    render: args => ({
        props: args,
        template: `
            <div style="height: 900px; display: flex; align-items: stretch; background: #8496b31a;">
                <ui-sidebar [navigationItems]="navigationItems"></ui-sidebar>
            </div>
        `
    })
};

export const ConstrainedWidth: Story = {
    args: {
        // Shows how sidebar behaves in a fixed-width container
    },
    render: args => ({
        props: args,
        template: `
            <div style="height: 900px; width: 150px; display: flex; align-items: stretch; background: #8496b31a; border: 2px dashed #8496b3;">
                <ui-sidebar></ui-sidebar>
            </div>
        `
    })
};
