:where(:root[data-uinew]) :host {
  .sidebar {
    display: inline-flex;
    height: 100%; // Fill parent height
    padding: var(--nui-main-navigation-space-padding-vertical, 20px) var(--nui-main-navigation-space-padding-horizontal, 20px);
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    flex-shrink: 0;
    background: var(--nui-main-navigation-fill-default, rgba(255, 255, 255, 0%));
    transition: width 0.2s ease-in-out;
    width: auto;
    min-width: fit-content;

    &.expanded {
      width: auto;
      min-width: 192px;
      
      .navigation-item {
        padding: var(--nui-main-navigation-item-space-padding-vertical-expanded, 8px) var(--nui-main-navigation-item-space-padding-horizontal-collapsed, 8px);
        padding-right: var(--nui-main-navigation-item-space-padding-horizontal-expanded, 12px);
        justify-content: flex-start;
        gap: var(--nui-label-space-gap, 8px);
      
      }
    }

    .navigation-items {
      display: flex;
      flex-direction: column;
      gap: var(--nui-main-navigation-space-gap-top, 16px);
      width: 100%;
    }

    .navigation-item {
      display: flex;
      height: var(--nui-main-navigation-item-height, 40px);
      padding: var(--nui-main-navigation-item-space-padding-vertical-collapsed, 8px) var(--nui-main-navigation-item-space-padding-horizontal-collapsed, 8px);
      justify-content: center;
      align-items: center;
      border-radius: var(--nui-main-navigation-item-radius, var(--nui-border-radius-medium, 8px));
      background: var(--nui-main-navigation-item-fill-default, rgba(255, 255, 255, 0%));
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      border: none;
      width: 100%;

      &:hover {
        background: var(--nui-main-navigation-item-fill-hover);
      }

      &.active {
        background: var(--nui-main-navigation-item-fill-active);
        
        ui-svg-icon {
          color: var(--nui-main-navigation-item-icon-active);
        }

        ui-label {
          color: var(--nui-main-navigation-item-text-active);
        }
      }
    }

    .toggle-section {
      display: flex;
      width: 100%;
      justify-content: end;
    }

    .nowrap {
      text-wrap: nowrap;
    }
  }
}


