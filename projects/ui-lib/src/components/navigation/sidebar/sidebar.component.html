<div
    class="sidebar"
    [class.expanded]="isExpanded()">
    <!-- Main navigation items -->
    <div class="navigation-items">
        @for (item of getNavigationItems(); track item.id) {
            <button
                class="navigation-item"
                [class.active]="item.active"
                [id]="item.htmlId"
                (click)="onNavigationItemClick(item)"
                [title]="item.label">
                <ui-svg-icon
                    icon="none"
                    [nuiIcon]="item.nuiIcon"
                    size="md">
                </ui-svg-icon>
                @if (isExpanded()) {
                    <ui-label
                        weight="bold"
                        class="nowrap"
                        >{{ item.label }}</ui-label
                    >
                }
            </button>
        }
    </div>

    <!-- Toggle button -->
    <div class="toggle-section">
        <ui-button
            nuiType="ghost-secondary"
            [nuiSvgIcon]="isExpanded() ? 'arrow_back' : 'arrow_forward'"
            id="sidebar-expand-toggle"
            (click)="onToggleClick()"
            [title]="isExpanded() ? 'Collapse sidebar' : 'Expand sidebar'">
        </ui-button>
    </div>
</div>
