import { Injector } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { injectInjectorToProps } from '../../../storybook-decorators/inject-injector-to-props/inject-injector-to-props.decorator';
import { UIIframeComponent } from './iframe.component';

const meta: Meta<UIIframeComponent> = {
    title: 'Components/Layout/iframe',
    component: UIIframeComponent,
    decorators: [
        injectInjectorToProps,
        moduleMetadata({
            imports: [UIIframeComponent]
        })
    ],
    parameters: {
        injectInjectorToProps: true
    }
};
export default meta;

type Story = StoryObj<UIIframeComponent>;

export const Default: Story = {
    args: {
        url: 'https://example.com/'
    },
    render: args => ({
        ...args,
        props: {
            ...args,
            sanitizeUrl: (injector: Injector, url: string) => {
                const domSanitizer = injector.get(DomSanitizer);
                const safeUrl = domSanitizer.bypassSecurityTrustResourceUrl(url);
                return safeUrl;
            }
        },
        template: `
            <div style="height: 50vh;">
                <ui-iframe [url]="sanitizeUrl(injector,url)" />
            </div>`
    })
};
