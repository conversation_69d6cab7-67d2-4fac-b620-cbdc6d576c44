@use 'sass:math';
@use '../../../style/mixins';
@use '../../../style/animation';

:where(:root[data-uinew]) :host {
    display: block;
    position: relative;
    cursor: pointer;
    font-size: var(--nui-font-size-275);
    font-weight: var(--nui-font-font-weight-10000);
    line-height: var(--nui-font-line-height-400);
    font-style: normal;
    letter-spacing: 0.14px;
    height: 100%;

    .content {
        height: calc(100% - var(--nui-tabs-height));
    }

    &.hide-tabs {
        .header {
            display: none;
        }

        .content {
            height: 100%;
        }
    }

    .header {
        position: relative;
        align-items: center;
        display: inline-flex;
        height: var(--nui-tabs-height);
        border-radius: var(--nui-tabs-radius);

        .tab {
            position: relative;
            display: flex;
            height: var(--nui-tabs-item-height);
            padding: 0 var(--nui-tabs-item-space-padding-horizontal-text);
            justify-content: center;
            align-items: center;
            gap: var(--nui-space-200);
            border-radius: var(--nui-tabs-item-radius);

            &:has(ui-svg-icon) {
                padding: 0 var(--nui-tabs-item-space-padding-horizontal-icon);
            }

            &.selected {
                pointer-events: none;
                cursor: default;
            }
        }

        // from old ui keeping for now
        .dot {
            width: 7px;
            height: 7px;
            background: var(--core-system-dark-red-1000);
            border-radius: 50%;
            position: absolute;
            top: 5px;
            right: 5px;
        }

        .indicator {
            display: none;
        }
    }

    &.primary {
        .header {
            border: 1px solid var(--nui-tabs-border-primary);
            background-color: var(--nui-tabs-fill-primary);

            .tab {
                background-color: var(--nui-tabs-fill-primary);

                &:hover {
                    background-color: var(--nui-tabs-fill-primary-hover);
                    color: var(--nui-tabs-text-primary-hover);
                }

                &.selected {
                    background-color: var(--nui-tabs-fill-primary-selected);
                    color: var(--nui-tabs-text-primary-selected);
                }
            }
        }
    }

    &.secondary {
        .header {
            border: 1px solid var(--nui-tabs-border-secondary);
            background-color: var(--nui-tabs-fill-secondary);
            box-sizing: content-box;

            .tab {
                background-color: var(--nui-tabs-fill-secondary);

                &:hover {
                    background-color: var(--nui-tabs-fill-secondary-hover);
                    color: var(--nui-tabs-text-secondary-hover);
                }

                &.selected {
                    background-color: var(--nui-tabs-fill-secondary-selected);
                    color: var(--nui-tabs-text-secondary-selected);
                    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 4%);
                }
            }
        }
    }
}
