<div class="header">
    @for (tab of tabs; track tab) {
        <div
            [id]="tab?.headerId"
            class="tab"
            #tabHeader
            [ngClass]="{ selected: tab.selected }"
            (mouseenter)="hoverTab($event)"
            (mouseleave)="resetSelectionIndicator()"
            (click)="selectTab(tab)">
            @if (tab.icon) {
                <ui-svg-icon
                    [icon]="tab.icon"
                    [nuiIcon]="tab.nuiIcon" />
            }

            {{ tab.name }}
            @if (tab.dot) {
                <div class="dot"></div>
            }
        </div>
    }
    <div
        class="indicator"
        #indicator></div>
</div>
<div class="content">
    <ng-content></ng-content>
</div>
