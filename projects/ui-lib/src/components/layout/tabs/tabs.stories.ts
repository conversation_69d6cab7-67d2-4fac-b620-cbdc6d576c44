import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UIButtonComponent } from '../../buttons';
import { UITabComponent } from './tab.component';
import { UITabsComponent } from './tabs.component';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UITabsComponent> = {
    title: 'Components/Layout/Tabs',
    component: UITabsComponent,
    decorators: [
        moduleMetadata({
            imports: [UITabsComponent, UITabComponent, UIButtonComponent, UIComponentSizeDirective]
        })
    ]
};
export default meta;

type Story = StoryObj<UITabsComponent>;

export const Default: Story = {
    render: args => ({
        ...args,
        template: `
<ui-tabs #tabs>
    <ui-tab name="Tab #1" #tab1>
        <h3 [uiSize]="'sm'"> Content for 'Tab #1'</h3>
    </ui-tab>
    <ui-tab name="Tab #2" #tab2>
        <h3 [uiSize]="'sm'"> Content for 'Tab #2'</h3>
    </ui-tab>
    <ui-tab name="Tab #3" #tab3>
        <h3 [uiSize]="'sm'"> Content for 'Tab #3'</h3>
    </ui-tab>
</ui-tabs>
<hr/>
<h3 [uiSize]="'sm'"> This is outside the ui-tabs component </h3>
<ui-button text="Select tab #3" (click)="tabs.selectTab(tab3)"/>
<ui-button text="Select tab #1" (click)="tabs.selectTab(tab1)"/>
<ui-button text="Select tab #2" (click)="tabs.selectTab(tab2)"/>
<ui-button text="Select tab #3" (click)="tabs.selectTab(tab3)"/>
`
    })
};

export const Secondary: Story = {
    render: args => ({
        ...args,
        template: `
            <ui-tabs type="secondary">
                <ui-tab name="Tab Item" #tab1/>
                <ui-tab name="Tab Item" #tab2/>
                <ui-tab name="Tab Item" #tab3/>
            </ui-tabs>
            `
    })
};

export const WithIconsPrimary: Story = {
    render: args => ({
        ...args,
        template: `
            <ui-tabs>
                <ui-tab icon="columns-grid" #tab1/>
                <ui-tab icon="columns-grid" #tab2/>
                <ui-tab icon="columns-grid" #tab3/>
            </ui-tabs>
        `
    })
};

export const WithIconsSecondary: Story = {
    render: args => ({
        ...args,
        template: `
            <ui-tabs type="secondary">
                <ui-tab icon="columns-grid" #tab1/>
                <ui-tab icon="columns-grid" #tab2/>
                <ui-tab icon="columns-grid" #tab3/>
            </ui-tabs>
        `
    })
};
