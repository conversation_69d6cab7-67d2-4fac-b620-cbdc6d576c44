@use 'sass:math';
@use '../../../style/mixins';
@use '../../../style/animation';

@keyframes bfTabs__tabDot {
    0% {
        transform: scale(0%);
    }

    100% {
        transform: scale(100%);
    }
}

$headerHeight: 5rem;
$headerPadding: math.div(5, 2) * 1rem;
$dotSize: 0.7rem;

:where(:root:not([data-uinew])) :host {
    margin: 0;
    display: block;
    position: relative;

    .header {
        height: $headerHeight;
        position: relative;
        border-radius: var(--ui-border-radius) var(--ui-border-radius) 0 0;
        background-color: var(--ui-color-surface-second);
        border: solid 1px var(--ui-color-border);
        padding: 0 $headerPadding;

        .tab {
            position: relative;
            text-transform: uppercase;
            display: inline-block;
            line-height: $headerHeight - 0.2rem;
            white-space: nowrap;
            padding: 0 $headerPadding;
            transition: color 0.2s ease;
            cursor: pointer;
            user-select: none;
            font-weight: var(--ui-font-weight-normal);
            color: var(--ui-color-text-second);

            &.selected {
                pointer-events: none;
                color: var(--ui-color-active);
            }
        }

        .dot {
            width: $dotSize;
            height: $dotSize;
            background: var(--ui-color-alert);
            border-radius: 50%;
            position: absolute;
            top: math.div($headerHeight, 2) - 1.4rem;
            right: math.div($headerPadding, 2);
        }

        .indicator {
            position: absolute;
            bottom: -0.1rem;
            left: var(--ui-padding);
            width: 5rem;
            height: 0.2rem;
            background-color: var(--ui-color-active);
            transition:
                left 0.2s ease 0.1s,
                width 0.2s ease 0.1s,
                background-color 0.2s ease 0.1s;

            &.preview {
                background-color: var(--ui-color-text-second);
            }
        }
    }

    .content {
        padding: var(--ui-padding);
        border-radius: 0 0 var(--ui-border-radius) var(--ui-border-radius);
        background-color: var(--ui-color-surface);
        border: 1px solid var(--ui-color-border);
        border-top: none;
        visibility: var(--ui-tabs-content-visibility, visible);
    }
}
