import { NgClass } from '@angular/common';
import {
    AfterContentInit,
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ContentChildren,
    ElementRef,
    inject,
    input,
    On<PERSON><PERSON><PERSON>,
    QueryList,
    Renderer2,
    viewChild,
    viewChildren
} from '@angular/core';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UISVGIconComponent } from '../../icon';
import { UITabComponent } from './tab.component';

const PADDING = 20;
const PREVIEW_CLASS = 'preview';

/**
 * Tabs component. Meant to be use with one or multiple <ui-tab/> children.
 *
 * CSS API:
 * --ui-tabs-content-visibility: Handles the content visibility attribute
 */
@Component({
    imports: [NgClass, UISVGIconComponent],
    selector: 'ui-tabs',
    templateUrl: 'tabs.component.html',
    styleUrls: ['tabs.component.scss', 'tabs.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class]': `type()`,
        '[class.ui-tabs]': 'true',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UITabsComponent implements AfterContentInit, AfterViewInit, OnDestroy {
    private renderer = inject(Renderer2);
    private changeDetectorRef = inject(ChangeDetectorRef);

    type = input<'primary' | 'secondary'>('primary');
    size = input<'xs' | 'sm' | 'md'>('md');

    // TODO: HANDLE REMOVAL AND ADDITION OF TABS AS WELL AS SELECTIONS BY BINDING ON EACH TAB
    // TODO: Skipped for migration because:
    //  There are references to this query that cannot be migrated automatically.
    @ContentChildren(UITabComponent)
    tabs: QueryList<UITabComponent>;

    readonly tabHeaders = viewChildren<ElementRef>('tabHeader');

    readonly indicatorRef = viewChild<ElementRef>('indicator');

    private tabSubscriber: any;
    private tabChangeSubscribers: any[] = [];

    /**
     * When content is available
     */
    ngAfterContentInit(): void {
        // When a tab is added or removed
        this.tabSubscriber = this.tabs.changes.subscribe(() => {
            this.initalSelection();
            this.addTabSubscribers();
            this.changeDetectorRef.detectChanges();
        });
        this.addTabSubscribers();

        // Content cant be change during loop since it will cause "ExpressionChangedError"

        this.initalSelection();
    }

    // When viewchildren is available
    ngAfterViewInit(): void {
        // Content cant be change during loop since it will cause "ExpressionChangedError"
        setTimeout(() => {
            this.resetSelectionIndicator();
        });
    }

    /**
     * Select a tab
     * @param selectedTab
     */
    selectTab(selectedTab: UITabComponent): void {
        this.tabs.forEach(tab => {
            tab.selected = tab === selectedTab;
        });

        this.changeDetectorRef.detectChanges();
        this.resetSelectionIndicator();
    }

    /**
     * When mouse hovers tab header.
     * @param event
     */
    hoverTab(event: MouseEvent): void {
        if (event && event.target) {
            const target: HTMLDivElement = event.target as HTMLDivElement;
            this.moveIndicatorToElement(target, true);
        }
    }

    /**
     * Reset position of indicator to currently active tab
     */
    resetSelectionIndicator(): void {
        const target: HTMLDivElement = this.getTabHeader()!;
        this.moveIndicatorToElement(target);
    }

    /**
     * Move indicator to an element.
     * @param target
     * @param preview
     */
    private moveIndicatorToElement(target?: HTMLDivElement, preview?: boolean): void {
        const indicatorRef = this.indicatorRef();
        if (target && indicatorRef) {
            const indicator = indicatorRef.nativeElement;
            this.renderer.setStyle(indicator, 'left', `${target.offsetLeft + PADDING}px`);
            this.renderer.setStyle(indicator, 'width', `${target.offsetWidth - PADDING * 2}px`);

            if (preview) {
                this.renderer.addClass(indicator, PREVIEW_CLASS);
            } else {
                this.renderer.removeClass(indicator, PREVIEW_CLASS);
            }
        }
    }

    /**
     * When something in a child tab is changed from "outside"
     */
    private onTabChange = (): void => {
        this.changeDetectorRef.detectChanges();
    };

    /**
     * When having nothing selected -> Select first.
     */
    private initalSelection(): void {
        if (this.tabs && !this.getSelectedTab()) {
            this.selectTab(this.tabs.first);
        }
    }

    private getTabHeader(byIndex: number = this.getSelectedTabIndex()): HTMLDivElement | undefined {
        const tabHeaders = this.tabHeaders();
        if (byIndex === undefined || !tabHeaders) {
            return undefined;
        }
        return tabHeaders[byIndex].nativeElement as HTMLDivElement;
    }

    private getSelectedTab(): UITabComponent | undefined {
        return this.tabs.find(tab => tab.selected);
    }

    /**
     * Get index of selected tab
     */
    private getSelectedTabIndex(): number {
        return this.tabs.toArray().findIndex(tab => tab.selected);
    }

    /**
     * Add subscriptions to tabChange event (name/dot changes to children)
     */
    private addTabSubscribers(): void {
        this.removeTabSubscribers();

        this.tabChangeSubscribers = this.tabs.map((tab: UITabComponent) =>
            tab.tabChange.subscribe(this.onTabChange)
        );
    }

    /**
     * Remove all tabChange subscribers
     */
    private removeTabSubscribers(): void {
        if (this.tabChangeSubscribers) {
            while (this.tabChangeSubscribers.length) {
                this.tabChangeSubscribers.pop().unsubscribe();
            }
        }
    }

    /**
     * Cleanup component when destroyed
     */
    ngOnDestroy(): void {
        if (this.tabSubscriber) {
            this.tabSubscriber.unsubscribe();
        }
        this.changeDetectorRef.detach();
        this.removeTabSubscribers();
    }
}
