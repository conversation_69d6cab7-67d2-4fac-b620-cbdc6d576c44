import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { UIButtonComponent } from '../../buttons';

@Component({
    imports: [UIButtonComponent],
    selector: 'ui-panel',
    templateUrl: 'panel.component.html',
    styleUrls: ['panel.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.ui-panel]': 'true'
    }
})
export class UIPanelComponent {
    @Input() name: string;
    @Input() buttonIcon: string;
    @Output() buttonClick: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();
}
