@use 'sass:math';
@use '../../../style/mixins';

$headerHeight: 5rem;

:where(:root:not([data-uinew])) :host {
    display: block;
    position: relative;
    margin-bottom: var(--ui-margin);

    .header {
        display: flex;
        height: $headerHeight;
        position: relative;
        border-radius: var(--ui-border-radius) var(--ui-border-radius) 0 0;
        background-color: var(--header-background-color);
        border: 1px solid var(--border-color);
        padding: 0 var(--ui-padding-small);
        align-items: center;

        &__name {
            text-transform: uppercase;
            display: inline-block;
            white-space: nowrap;
            transition: color 0.2s ease;
            cursor: pointer;
            user-select: none;
            font-weight: 500;
        }

        &__button {
            position: absolute;
            right: math.div(($headerHeight - 3.4rem), 2);
            top: math.div(($headerHeight - 3.4rem), 2);
        }

        @include mixins.ui-clearfix;
    }

    .content {
        padding: var(--ui-padding-small);
        border-radius: 0 0 var(--ui-border-radius) var(--ui-border-radius);
        background-color: var(--background-color);
        border: solid 1px var(--border-color);
        border-top: none;
    }
}
