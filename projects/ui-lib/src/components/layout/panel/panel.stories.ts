import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIButtonComponent } from '../../buttons';
import { UIIconComponent } from '../../icon';
import { UIPanelComponent } from './panel.component';

const meta: Meta<UIPanelComponent> = {
    title: 'Components/Layout/Panel',
    component: UIPanelComponent,
    decorators: [
        moduleMetadata({
            imports: [UIPanelComponent, UIButtonComponent, UIIconComponent]
        })
    ]
};

export default meta;
type Story = StoryObj<UIPanelComponent>;

export const Panel: Story = {
    render: () => ({
        props: {
            name: 'PANEL',
            content: 'Fill this up with yor custom html'
        },
        template: `
            <ui-panel [name]="name" [buttonIcon]="buttonIcon">
                {{content}}
            </ui-panel>
        `
    })
};
export const PanelWithIcon: Story = {
    render: () => ({
        props: {
            name: 'PANEL',
            buttonIcon: 'analytics',
            content: 'Fill this up with yor custom html'
        },
        template: `
            <ui-panel [name]="name" [buttonIcon]="buttonIcon">
                {{content}}
            </ui-panel>
        `
    })
};
