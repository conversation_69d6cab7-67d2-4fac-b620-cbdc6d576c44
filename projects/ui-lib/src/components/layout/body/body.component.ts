import { CdkScrollableModule } from '@angular/cdk/scrolling';
import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    HostBinding,
    Input,
    inject
} from '@angular/core';
import { UIBodyRef } from './body-ref';

@Component({
    imports: [CdkScrollableModule],
    selector: 'ui-body',
    templateUrl: 'body.component.html',
    styleUrls: ['body.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UIBodyComponent {
    private bodyRef = inject(UIBodyRef);

    @Input()
    @HostBinding('class.full')
    full: boolean;

    @Input()
    @HostBinding('class.show-overflow')
    showOverflow: boolean;

    /**
     * Header is hidden, use that space
     */
    @Input()
    @HostBinding('class.no-header')
    noHeader: boolean;

    constructor() {
        const host = inject(ElementRef);

        this.bodyRef.host = host;
    }
}
