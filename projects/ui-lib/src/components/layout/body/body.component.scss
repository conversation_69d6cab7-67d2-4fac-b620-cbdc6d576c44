:host {
    --padding: 60px 25px 25px 25px;

    position: absolute;
    inset: var(--ui-view-header-height) 0 0 0;
    -webkit-overflow-scrolling: touch;
    padding: var(--padding);
    transition: background-color 0.2s ease;
    background-color: var(--ui-color-background);
    color: var(--ui-color-text);
}

.scroll-wrapper {
    position: absolute;
    inset: 0;
    overflow: auto;
}

.content {
    position: relative;
    margin: 0 auto;
    width: 100%;
    max-width: var(--ui-view-body-max-width);
}

:host(.full) {
    padding: 0;

    .content {
        max-width: none;
        margin: 0;
        height: 100%;
    }
}

:host(.no-header) {
    top: 0;
}

:host(.show-overflow) {
    .scroll-wrapper {
        overflow: unset;
    }
}
