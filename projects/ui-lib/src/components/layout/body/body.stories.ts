import { CdkScrollableModule } from '@angular/cdk/scrolling';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UIBodyComponent } from './body.component';

const meta: Meta<UIBodyComponent> = {
    title: 'Components/Layout/Body',
    component: UIBodyComponent,
    decorators: [
        moduleMetadata({
            imports: [CdkScrollableModule]
        })
    ]
};
export default meta;

type Story = StoryObj<UIBodyComponent>;

export const Default: Story = {
    args: {}
};
