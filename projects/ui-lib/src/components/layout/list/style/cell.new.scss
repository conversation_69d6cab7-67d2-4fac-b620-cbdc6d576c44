/** Common style for all kinds of cells */
:where(:root[data-uinew] :host) {
    .cell {
        display: flex;
        align-items: center;
        height: var(--nui-table-item-height);

        ui-label {
            display: flex;
            align-items: center;
            gap: var(--nui-space-200);
        }

        &:not(.folder.first) {
            // !important needed because padding is set inline via cdkTreeNodePadding and cdkTreeNodePaddingIndent
            padding: 0 var(--nui-table-item-padding-horizontal) !important;
        }

        &.full-width.align-center {
            text-align: center;
            justify-content: center;
        }

        &.full-width {
            grid-column: var(--full-width-cell);
        }

        &.hidden {
            visibility: hidden;
            display: none;
        }
    }
}
