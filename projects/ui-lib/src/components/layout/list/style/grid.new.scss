:where(:root[data-uinew] :host) {
    .list-body.grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(var(--grid-size), 1fr));
        grid-auto-rows: 1fr;

        &::before {
            content: '';
            width: 0;
            padding-bottom: 100%;
            grid-row: 1 / 1;
            grid-column: 1 / 1;
        }

        & > *:first-child {
            grid-row: 1 / 1;
            grid-column: 1 / 1;
        }

        .box {
            position: relative;
            align-items: center;
            justify-content: center;
            display: flex;
            transition: all 0.2s ease-in-out;
        }
    }
}
