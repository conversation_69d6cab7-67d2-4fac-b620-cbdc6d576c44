:where(:root:not([data-uinew]) :host) {
    .list-body.grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(var(--grid-size), 1fr));
        grid-auto-rows: 1fr;
        grid-gap: 10px;
        padding: 10px;

        &::before {
            content: '';
            width: 0;
            padding-bottom: 100%;
            grid-row: 1 / 1;
            grid-column: 1 / 1;
        }

        & > *:first-child {
            grid-row: 1 / 1;
            grid-column: 1 / 1;
        }

        .box {
            position: relative;
            align-items: center;
            justify-content: center;
            display: flex;
            border-radius: var(--ui-border-radius);
            transition: all 0.2s ease-in-out;

            &.selected {
                box-shadow: 0 0 0 1px var(--ui-color-selection);
            }

            &:not(.selected) {
                &:hover {
                    box-shadow:
                        0 0 0 1px #fff,
                        0 1px 8px 0 rgba(0, 0, 0, 20%);
                }
            }
        }
    }
}
