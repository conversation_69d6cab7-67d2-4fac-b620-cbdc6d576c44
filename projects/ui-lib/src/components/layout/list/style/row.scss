:where(:root:not([data-uinew]) :host) {
    .row {
        position: relative;
        transition: background-color 0.2s ease;
        width: 100%;
        min-height: var(--row-height);
        background: var(--background);
        height: auto;
        align-items: center;
        display: grid;
        grid-template-columns: var(--grid);

        &:nth-child(odd) {
            background: var(--background-alt);
        }
    }
}
