/** Common style for all kinds of cells */
:where(:root:not([data-uinew]) :host) {
    .cell {
        position: relative;
        padding: 0 var(--cell-padding);
        border-collapse: collapse;
        align-items: center;

        &:not(.indent) {
            padding-left: var(--cell-padding) !important;
        }

        &.indent {
            border-left: var(--cell-padding) solid transparent;
        }

        &:last-of-type {
            border-right: 0 solid transparent;
        }

        &.align-right {
            text-align: right;
            justify-content: right;
        }

        &.align-center {
            text-align: center;
            justify-content: center;
        }

        &:not(.multiline) {
            display: flex;
            overflow: hidden;
        }

        &.hidden {
            visibility: hidden;
            display: none;
        }

        &.full-width {
            grid-column: var(--full-width-cell);
        }

        ui-radio {
            display: flex;
            justify-content: center;
        }

        &.back {
            cursor: pointer;
            height: 100%;

            .icon {
                margin-left: 20px;
                font-size: var(--icon-font-size);
                padding-right: 0.5rem;
            }
        }

        &.empty {
            .text {
                font-style: italic;
                opacity: 0.5;
            }
        }
    }
}
