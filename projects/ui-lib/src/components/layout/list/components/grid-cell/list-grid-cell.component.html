@if (grid?.cellTemplate) {
    <ng-container
        *ngTemplateOutlet="
            grid.cellTemplate ? grid.cellTemplate : null;
            context: {
                $implicit: value,
                value: value,
                displayValue: displayValue,
                data: data,
                grid: grid
            }
        "></ng-container>
}
@if (!grid?.cellTemplate && type !== 'image') {
    {{ displayValue !== undefined ? displayValue : '' }}
    Type: {{ type }}
}
@if (!grid?.cellTemplate && type === 'image' && value) {
    <div
        class="image"
        [style.background-image]="'url(' + value + ')'"></div>
}
