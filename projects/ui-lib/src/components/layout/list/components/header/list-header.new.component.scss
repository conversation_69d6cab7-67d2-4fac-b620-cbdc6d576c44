@use '../../style/cell.new';

:where(:root[data-uinew]) :host {
    .header-cell {
        user-select: none;
        height: var(--nui-table-item-height);

        // !important needed because padding is set inline via cdkTreeNodePadding in the old UI
        padding: 0 var(--nui-table-item-padding-horizontal) !important;
        background: var(--nui-table-fill-even);
    }

    .checkbox-cell,
    .radio-cell {
        display: flex;
        align-items: center;

        ui-checkbox {
            display: inline-block;
            pointer-events: none;
        }
    }
}
