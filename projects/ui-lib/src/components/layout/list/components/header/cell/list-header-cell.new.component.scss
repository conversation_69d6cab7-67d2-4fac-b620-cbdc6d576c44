:where(:root[data-uinew]) {
    :host(.cell) {
        display: flex;
        height: var(--nui-table-item-height, 48px);
        padding: 0 var(--nui-table-item-padding-horizontal, 16px);
        align-items: center;

        &.sortable {
            cursor: pointer;

            &:hover .sort-icon {
                opacity: 1;
            }

            &.sort-asc .sort-icon {
                transform: rotate(0deg);
            }

            &.sort-desc,
            &.sort-asc {
                .sort-icon {
                    opacity: 1;
                }
            }
        }
    }

    :host {
        .sort-icon {
            margin-left: var(--nui-table-item-gap, 8px);
            opacity: 0;
            transition:
                transform 0.2s ease,
                opacity 0.2s ease;
            transform: rotate(-180deg);
        }
    }
}
