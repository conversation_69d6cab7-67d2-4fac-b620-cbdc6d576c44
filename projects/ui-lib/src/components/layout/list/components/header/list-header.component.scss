@use '../../style/cell';

:where(:root:not([data-uinew])) {
    :host(.row) {
        height: var(--header-height);
        background: var(--ui-color-surface-second);
        padding: 0;
        min-height: 0;
        border-bottom: 1px solid var(--ui-color-border);
    }

    :host {
        .header-cell {
            transition: opacity 0.2s ease;
            opacity: 0;
            user-select: none;
        }

        .checkbox-cell {
            display: flex;
            border-right: 1px solid var(--ui-color-border);
            height: var(--header-height);
            padding: 0 var(--cell-padding);
            align-items: center;
            justify-content: center;
            cursor: pointer;

            ui-checkbox {
                pointer-events: none;
            }
        }

        .radio-cell {
            border-right: 1px solid var(--ui-color-border);
            height: var(--header-height);
            line-height: calc(var(--header-height) - 1px);
            padding: 0 var(--cell-padding);
            text-align: center;
            justify-content: center;
            cursor: pointer;

            ui-radio {
                pointer-events: none;
            }
        }
    }

    :host(.initialized) {
        .header-cell {
            opacity: 1;
        }
    }
}
