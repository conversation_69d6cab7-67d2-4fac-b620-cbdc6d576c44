:where(:root:not([data-uinew])) {
    :host(.cell) {
        --sort-icon-width: 15px;

        border-right: 1px solid var(--ui-color-border);
        height: unset;
        line-height: calc(var(--header-height) - 1px);
        padding: 0 var(--cell-padding);

        &:first-of-type {
            // Calc doesn't seem to work with 0
            // padding-left: @css { calc(var(--row-padding) + var(--cell-padding)) };
            padding-left: var(--cell-padding);
        }

        &:last-of-type {
            border-right: none;

            // Calc doesn't seem to work with 0
            // padding-right: @css { calc(var(--row-padding) + var(--cell-padding)) };
            padding-right: var(--cell-padding);
        }

        &.no-border {
            border-right: 1px solid transparent;
        }

        &.sortable {
            cursor: pointer;

            // Rotate
            &.sort-asc .sort-icon ::ng-deep svg {
                transform: rotate(0deg);
            }

            // Active
            &.sort-desc,
            &.sort-asc {
                padding-right: calc(var(--cell-padding) * 2 + var(--sort-icon-width)) !important;

                .sort-icon {
                    opacity: 1;
                }

                &.align-center {
                    padding-left: calc(var(--cell-padding) * 2 + var(--sort-icon-width)) !important;
                }
            }
        }

        &:hover {
            .description-icon {
                opacity: 0.5;
            }
        }
    }

    :host {
        .sort-icon {
            font-size: 1.4rem;
            opacity: 0;
            transform: translateY(-50%);
            transition:
                opacity 0.2s ease,
                transform 0.2s ease;
            position: absolute;
            width: var(--sort-icon-width);
            right: var(--cell-padding);
            text-align: left;
            top: 50%;
            pointer-events: none;

            ::ng-deep svg {
                transition: transform 0.2s ease;
                transform: rotate(-180deg);
            }
        }

        .description-icon {
            opacity: 0.3;
            transition: opacity 0.2s ease;
        }
    }
}
