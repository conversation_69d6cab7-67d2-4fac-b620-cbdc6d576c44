@switch (selectionType) {
    @case ('checkbox') {
        <div
            class="header-cell checkbox-cell align-center"
            (click)="toggleAll()">
            <ui-checkbox
                [selected]="allSelected"
                [indeterminate]="indeterminate">
            </ui-checkbox>
        </div>
    }
    @case ('radio') {
        <div class="header-cell radio-cell align-center"></div>
    }
}

@for (column of columns; track column) {
    <ui-list-header-cell
        class="header-cell"
        [column]="column"
        [ngClass]="{ 'no-border': !column.border }"
        [uiTooltip]="column.description"
        [uiTooltipDisabled]="!column.description"
        [uiTooltipWidth]="200"
        (click)="onSortClick(column)">
    </ui-list-header-cell>
}

<!-- <col width=100><col width=100><col width=100> -->
