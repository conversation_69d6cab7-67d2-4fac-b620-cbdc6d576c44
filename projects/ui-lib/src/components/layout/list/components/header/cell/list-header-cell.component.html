@if (column.headerTemplate) {
    <ng-container
        *ngTemplateOutlet="column.headerTemplate; context: { $implicit: column }"></ng-container>
}

@if (!column.headerTemplate && column.name) {
    @if (isNewUI()) {
        <ui-label [weight]="'bold'">
            {{ column.name }}
        </ui-label>

        @if (column.description) {
            <ui-icon
                class="description-icon"
                icon="none"
                nuiIcon="help">
            </ui-icon>
        }

        <ui-button
            class="sort-icon"
            type="plain-secondary"
            nuiSvgIcon="arrow_downward">
        </ui-button>
    } @else {
        {{ column.name }}

        @if (column.description) {
            <ui-icon
                class="description-icon"
                icon="help-tooltip">
            </ui-icon>
        }
        <ui-svg-icon
            class="sort-icon"
            icon="arrow-down">
        </ui-svg-icon>
    }
}
