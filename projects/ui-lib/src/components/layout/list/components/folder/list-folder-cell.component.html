@if (isDrillDown()) {
    <div>
        @if (first && expandable) {
            <ui-svg-icon
                class="toggle-icon"
                (click)="onToggleClick($event)"
                [icon]="'arrow-right'"
                nuiIcon="keyboard_arrow_right">
            </ui-svg-icon>
        }
    </div>
}

@if (column?.folderTemplate) {
    <ng-container
        *ngTemplateOutlet="
            column.folderTemplate ? column.folderTemplate : null;
            context: {
                $implicit: value,
                value: value,
                displayValue: displayValue,
                data: data,
                column: column
            }
        "></ng-container>
} @else {
    @if (isNewUI()) {
        @if (first && expandable) {
            <ui-svg-icon
                (click)="onToggleClick($event)"
                [icon]="'none'"
                nuiIcon="keyboard_arrow_right">
            </ui-svg-icon>
        }

        <ui-label
            [leadingIcon]="first ? 'folder' : undefined"
            (click)="onNameClick($event)"
            >{{ displayValue || '' }}
        </ui-label>
    } @else {
        @if (first && expandable) {
            <ui-svg-icon
                class="toggle-icon"
                (click)="onToggleClick($event)"
                icon="arrow-right">
            </ui-svg-icon>
        }
        @if (first) {
            <ui-svg-icon
                class="folder-icon"
                icon="folder">
            </ui-svg-icon>
        }
        <span
            (click)="onNameClick($event)"
            class="name"
            >{{ displayValue || '' }}</span
        >
    }
}
