@use '../../../../../style/mixins';

:where(:root:not([data-uinew])) :host {
    &.last:not(.first) {
        .name {
            min-width: calc(100% + 10px);
        }
    }

    .toggle-icon {
        display: inline-block;
        transition: opacity 0.2s ease;
        cursor: pointer;
        color: var(--ui-color-text);
        opacity: 0.2;
        text-align: left;
        width: var(--toogle-icon-size);
        font-size: var(--icon-font-size);
        margin-right: 6px;

        ::ng-deep {
            i {
                transition: transform 0.2s ease;
                transform: rotate(-90deg);
            }

            svg {
                transition: transform 0.2s ease;
            }
        }

        &:hover {
            opacity: 0.4;
        }
    }

    .folder-icon {
        color: var(--ui-color-text);
        opacity: var(--folder-opacity);
        display: inline-block;
        margin-right: 0.7rem;
        font-size: var(--icon-font-size);
        text-align: center;
    }

    .name {
        cursor: pointer;

        @include mixins.ui-ellipsis;

        width: 1px;
        min-width: calc(100% - 40px);

        &:hover {
            text-decoration: underline;
        }
    }

    :host-context([aria-expanded='true']) {
        .toggle-icon {
            ::ng-deep {
                i {
                    transform: rotate(0deg);
                }

                svg {
                    transform: rotate(90deg);
                }
            }
        }
    }
}
