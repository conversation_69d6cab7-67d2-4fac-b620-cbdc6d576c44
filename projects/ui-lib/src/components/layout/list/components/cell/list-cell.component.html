@if (column?.cellTemplate && !editable) {
    @if (isNewUI()) {
        <ui-label>
            <ng-container
                *ngTemplateOutlet="
                    column.cellTemplate ? column.cellTemplate : null;
                    context: {
                        $implicit: value,
                        value: value,
                        displayValue: displayValue,
                        data: data,
                        column: column
                    }
                "></ng-container>
        </ui-label>
    } @else {
        <ng-container
            *ngTemplateOutlet="
                column.cellTemplate ? column.cellTemplate : null;
                context: {
                    $implicit: value,
                    value: value,
                    displayValue: displayValue,
                    data: data,
                    column: column
                }
            "></ng-container>
    }
}
@if (!column?.cellTemplate && type !== 'image' && !editable) {
    @if (isNewUI()) {
        <ui-label>{{ displayValue || '' }}</ui-label>
    } @else {
        {{ displayValue || '' }}
    }
}
@if (!column?.cellTemplate && type === 'image' && value && !editable) {
    <div
        class="image"
        [style.background-image]="'url(' + value + ')'"></div>
}
@if (editable) {
    <div class="list-input">
        <ui-input
            #editInput
            [disabled]="!editing || data.disabled"
            value="{{ displayValue || value }}"
            (click)="enableEdit(data.disabled)"
            (focusout)="inputFocusOut()"
            (keyup)="keyUpListener($event)">
        </ui-input>
    </div>
}
