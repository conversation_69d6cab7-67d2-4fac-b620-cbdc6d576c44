:where(:root:not([data-uinew])) {
    :host-context(.expandable) {
        &.indent {
            border-left: 20px solid transparent;
        }
    }

    .image {
        display: block;
        width: calc(var(--row-height) - (var(--cell-padding) * 2));
        height: calc(var(--row-height) - (var(--cell-padding) * 2));
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 50% 50%;
    }

    .list-input {
        ui-input {
            &.input {
                ::ng-deep {
                    input {
                        cursor: text;
                        height: 3rem;
                    }
                }

                &.disabled {
                    ::ng-deep {
                        input {
                            color: var(--ui-color-text);
                            background-color: transparent;
                            border: 1px solid transparent;
                            user-select: none;
                            cursor: default;
                        }

                        &:hover {
                            border: 1px solid var(--ui-color-border);
                        }
                    }
                }
            }
        }
    }
}
