import { CommonModule } from '@angular/common';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UIListComponent } from './list.component';
import { UIListDataSource } from './models/list-data-source';
import { UIListCellTemplateDirective, UIListColumnDirective } from './templates';
import { UIDraggableDirective, UIDropZoneDirective } from '../../../directives';
import { UISVGIconComponent } from '../../icon';
import { UICheckboxComponent, UIRadioComponent, UIToggleSwitchComponent } from '../../inputs';
import { UILoaderComponent } from '../../loader';
import { UIPillComponent } from '../../pill';
import {
    UIListFolderCellComponent,
    UIListGridCellComponent,
    UIListCellComponent,
    UIListHeaderComponent
} from './components';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const TREE_DATA = [
    {
        id: 1,
        title: 'Not a folder',
        disabled: true
    },
    {
        id: 1,
        title: 'This folder has the longest name in the world, This folder has the longest name in the world, This folder has the longest name in the world, This folder has the longest name in the world',
        children: [
            {
                id: 2,
                title: 'Sub Folder 1'
            },
            {
                id: 3,
                title: 'Sub Folder 2'
            }
        ]
    },
    {
        id: 4,
        title: 'Folder 2',
        children: []
    },
    {
        id: 5,
        title: 'Folder 3',
        children: [
            {
                id: 6,
                title: 'Subfolder 3',
                children: [
                    {
                        id: 7,
                        title: 'Subfolder 4',
                        children: [
                            {
                                id: 9,
                                title: 'Not a folder'
                            }
                        ]
                    }
                ]
            }
        ]
    },
    {
        id: 8,
        title: 'Load more, empty from beginning',
        totalCount: 120,
        children: []
    }
];

const EXPANDABLE_CHECKBOX_DATA = [
    {
        id: 1,
        title: 'I have two children',
        children: [
            { id: 998, title: 'child one', disabled: true },
            { id: 999, title: 'child two' }
        ]
    },
    {
        id: 2,
        title: 'I have one child',
        children: [{ id: 2, title: 'B 1' }]
    },
    {
        id: 3,
        title: 'I have empty children arr',
        children: []
    },
    {
        id: 4,
        title: 'I have not even children arr',
        disabled: true
    }
];

const PHOTO_DATA = [
    {
        albumId: 1,
        id: 1,
        title: 'accusamus beatae ad facilis cum similique qui sunt',
        url: 'https://via.placeholder.com/600/92c952',
        thumbnailUrl: 'https://via.placeholder.com/150/92c952'
    },
    {
        albumId: 1,
        id: 2,
        title: 'reprehenderit est deserunt velit ipsam',
        url: 'https://via.placeholder.com/600/771796',
        thumbnailUrl: 'https://via.placeholder.com/150/771796'
    },
    {
        albumId: 1,
        id: 3,
        title: 'officia porro iure quia iusto qui ipsa ut modi',
        url: 'https://via.placeholder.com/600/24f355',
        thumbnailUrl: 'https://via.placeholder.com/150/24f355'
    },
    {
        albumId: 1,
        id: 4,
        title: 'culpa odio esse rerum omnis laboriosam voluptate repudiandae',
        url: 'https://via.placeholder.com/600/d32776',
        thumbnailUrl: 'https://via.placeholder.com/150/d32776'
    },
    {
        albumId: 1,
        id: 5,
        title: 'natus nisi omnis corporis facere molestiae rerum in',
        url: 'https://via.placeholder.com/600/f66b97',
        thumbnailUrl: 'https://via.placeholder.com/150/f66b97'
    },
    {
        albumId: 1,
        id: 6,
        title: 'accusamus ea aliquid et amet sequi nemo',
        url: 'https://via.placeholder.com/600/56a8c2',
        thumbnailUrl: 'https://via.placeholder.com/150/56a8c2'
    },
    {
        albumId: 1,
        id: 7,
        title: 'officia delectus consequatur vero aut veniam explicabo molestias',
        url: 'https://via.placeholder.com/600/b0f7cc',
        thumbnailUrl: 'https://via.placeholder.com/150/b0f7cc'
    },
    {
        albumId: 1,
        id: 8,
        title: 'aut porro officiis laborum odit ea laudantium corporis',
        url: 'https://via.placeholder.com/600/54176f',
        thumbnailUrl: 'https://via.placeholder.com/150/54176f'
    },
    {
        albumId: 1,
        id: 9,
        title: 'qui eius qui autem sed',
        url: 'https://via.placeholder.com/600/51aa97',
        thumbnailUrl: 'https://via.placeholder.com/150/51aa97'
    },
    {
        albumId: 1,
        id: 10,
        title: 'beatae et provident et ut vel',
        url: 'https://via.placeholder.com/600/810b14',
        thumbnailUrl: 'https://via.placeholder.com/150/810b14'
    },
    {
        albumId: 1,
        id: 11,
        title: 'nihil at amet non hic quia qui',
        url: 'https://via.placeholder.com/600/1ee8a4',
        thumbnailUrl: 'https://via.placeholder.com/150/1ee8a4'
    },
    {
        albumId: 1,
        id: 12,
        title: 'mollitia soluta ut rerum eos aliquam consequatur perspiciatis maiores',
        url: 'https://via.placeholder.com/600/66b7d2',
        thumbnailUrl: 'https://via.placeholder.com/150/66b7d2'
    },
    {
        albumId: 1,
        id: 13,
        title: 'repudiandae iusto deleniti rerum',
        url: 'https://via.placeholder.com/600/197d29',
        thumbnailUrl: 'https://via.placeholder.com/150/197d29'
    },
    {
        albumId: 1,
        id: 14,
        title: 'est necessitatibus architecto ut laborum',
        url: 'https://via.placeholder.com/600/61a65',
        thumbnailUrl: 'https://via.placeholder.com/150/61a65'
    },
    {
        albumId: 1,
        id: 15,
        title: 'harum dicta similique quis dolore earum ex qui',
        url: 'https://via.placeholder.com/600/f9cee5',
        thumbnailUrl: 'https://via.placeholder.com/150/f9cee5'
    },
    {
        albumId: 1,
        id: 16,
        title: 'iusto sunt nobis quasi veritatis quas expedita voluptatum deserunt',
        url: 'https://via.placeholder.com/600/fdf73e',
        thumbnailUrl: 'https://via.placeholder.com/150/fdf73e'
    },
    {
        albumId: 1,
        id: 17,
        title: 'natus doloribus necessitatibus ipsa',
        url: 'https://via.placeholder.com/600/9c184f',
        thumbnailUrl: 'https://via.placeholder.com/150/9c184f'
    },
    {
        albumId: 1,
        id: 18,
        title: 'laboriosam odit nam necessitatibus et illum dolores reiciendis',
        url: 'https://via.placeholder.com/600/1fe46f',
        thumbnailUrl: 'https://via.placeholder.com/150/1fe46f'
    },
    {
        albumId: 1,
        id: 19,
        title: 'perferendis nesciunt eveniet et optio a',
        url: 'https://via.placeholder.com/600/56acb2',
        thumbnailUrl: 'https://via.placeholder.com/150/56acb2'
    }
];

const meta: Meta<UIListComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/List',
    component: UIListComponent,
    decorators: [
        moduleMetadata({
            imports: [
                CommonModule,
                UIListComponent,
                UIListColumnDirective,
                UIListCellTemplateDirective,
                UILoaderComponent,
                UIListFolderCellComponent,
                UIListGridCellComponent,
                UIListCellComponent,
                UIListHeaderComponent,
                UIDraggableDirective,
                UISVGIconComponent,
                UIDropZoneDirective,
                UICheckboxComponent,
                UIRadioComponent,
                UIPillComponent,
                UIToggleSwitchComponent
            ]
        })
    ],
    argTypes: {
        selectionChange: { action: 'selectionChange' },
        selectionType: { control: 'inline-radio', options: ['checkbox', 'radio'] }
    },
    parameters: {
        controls: {
            include: ['expandable', 'multiSelect', 'dataSource', 'selectionType', 'selectionChange']
        }
    }
};

export default meta;

type Story = StoryObj<UIListComponent>;

export const FolderTreeList: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        layout: 'list',
        expandable: true,
        dragAndDrop: true,
        multiSelect: false
    },
    render: args => ({
        props: {
            ...args,
            dataSource: new UIListDataSource(TREE_DATA)
        },
        template: `
            <ui-list
                [layout]="layout"
                [dataSource]="dataSource"
                [expandable]="expandable"
                [dragAndDrop]="dragAndDrop"
                [multiSelect]="multiSelect"
                (drop)="drop($event)"
                (load)="load($event)"
                (selectionChange)="selectionChange($event)"
                (editData)="editData($event)">

                <ui-list-column name="Title" property="title" [sortable]="true">
                    <ng-template let-data="data" let-value="value" ui-list-cell-template>
                        {{ value }}
                    </ng-template>
                </ui-list-column>
            </ui-list>
        `
    })
};

export const MultiSelectList: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        layout: 'list',
        expandable: false,
        multiSelect: true,
        selectionType: 'checkbox'
    },
    render: args => ({
        props: {
            ...args,
            dataSource: new UIListDataSource(PHOTO_DATA)
        },
        template: `
            <ui-list
                [layout]="layout"
                [dataSource]="dataSource"
                [expandable]="expandable"
                [multiSelect]="multiSelect"
                [selectionType]="selectionType"
                (selectionChange)="selectionChange($event)">

                <ui-list-column name="Title" property="title">
                    <ng-template let-data="data" ui-list-cell-template>
                        {{ data.title }}
                    </ng-template>
                </ui-list-column>
                <ui-list-column name="Url" property="url">
                    <ng-template let-data="data" ui-list-cell-template>
                        <a [href]="data.url" class="ui-link">{{ data.url }}</a>
                    </ng-template>
                </ui-list-column>
            </ui-list>
        `
    })
};

export const RadioList: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        layout: 'list',
        expandable: false,
        multiSelect: false,
        selectionType: 'radio'
    },
    render: args => ({
        props: {
            ...args,
            dataSource: new UIListDataSource(PHOTO_DATA)
        },
        template: `
            <ui-list
                [layout]="layout"
                [dataSource]="dataSource"
                [expandable]="expandable"
                [selectionType]="selectionType"
                (selectionChange)="selectionChange($event)">

                <ui-list-column name="Title" property="title">
                    <ng-template let-data="data" ui-list-cell-template>
                        {{ data.title }}
                    </ng-template>
                </ui-list-column>
                <ui-list-column name="Url" property="url">
                    <ng-template let-data="data" ui-list-cell-template>
                        <a [href]="data.url" class="ui-link">{{ data.url }}</a>
                    </ng-template>
                </ui-list-column>
            </ui-list>
        `
    })
};

export const DrillDownList: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        layout: 'list',
        expandable: true,
        multiSelect: true,
        selectionType: 'checkbox'
    },
    render: args => ({
        props: {
            ...args,
            dataSource: new UIListDataSource(EXPANDABLE_CHECKBOX_DATA)
        },
        template: `
            <ui-list
                [layout]="layout"
                [dataSource]="dataSource"
                [expandable]="expandable"
                [multiSelect]="multiSelect"
                [selectionType]="selectionType"
                (drop)="drop($event)"
                (load)="load($event)"
                (selectionChange)="selectionChange($event)"
                (editData)="editData($event)">

                <ui-list-column name="Name" property="title" [sortable]="true">
                    <ng-template let-data="data" ui-list-cell-template>
                        {{ data.title }}
                    </ng-template>
                </ui-list-column>
                <ui-list-column name="ID" property="id" [sortable]="true" align="center">
                    <ng-template let-data="data" ui-list-cell-template>
                        {{ data.id }}
                    </ng-template>
                </ui-list-column>
                <ui-list-column name="Disabled" property="disabled" align="center">
                    <ng-template let-data="data" ui-list-cell-template>
                        {{ data.disabled }}
                    </ng-template>
                </ui-list-column>
            </ui-list>
        `
    })
};

export const CustomCellTemplates: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        layout: 'list',
        selectionType: 'single'
    },
    render: args => ({
        props: {
            ...args,
            dataSource: new UIListDataSource(PHOTO_DATA.slice(0, 8))
        },
        template: `
            <ui-list
                [dataSource]="dataSource"
                [expandable]="false"
                [multiSelect]="false">

                <ui-list-column 
                    name="Link"
                    property="disabled"
                    [sortable]="false"
         >
                    <ng-template
                        let-data="data"
                        let-value="value"
                        ui-list-cell-template>
                        <div style="display: flex; align-items: center; gap: 8px; width: 100%;">
                            <ui-toggle-switch
                                size="sm"
                                [selected]="!data.disabled"
                                (selectedChange)="toggleEnabled($event, data.showcaseKey)">
                            </ui-toggle-switch>
                            <ui-svg-icon
                                icon="none"
                                size="sm"
                                nuiIcon="file_copy"/>
                            <a
                                [href]="data.url"
                                class="ui-link ui-truncate"
                                target="_blank">{{ data.url }}</a>
                        </div>
                    </ng-template>
                </ui-list-column>

                <ui-list-column
                    name="Title"
                    property="title"
                    width="350px">
                    <ng-template
                        let-value="value"
                        let-data="data"
                        ui-list-cell-template>
                        <div class="ui-truncate">
                            {{data.title}}
                        </div>
                    </ng-template>
                </ui-list-column>

                <ui-list-column name="Allowed" property="id"  [sortable]="false" width="175px">
                    <ng-template let-data="data" ui-list-cell-template>
                        <div style="display: flex; align-items: center; gap:8px;">
                            <ui-svg-icon icon="none" nuiIcon="chat_bubble" size="sm" />
                            <ui-svg-icon icon="none" nuiIcon="check_circle" size="sm" />
                            <ui-svg-icon icon="none" nuiIcon="edit" size="sm" />
                            <ui-svg-icon icon="none" nuiIcon="format_size" size="sm" />
                            <ui-svg-icon icon="none" nuiIcon="output" size="sm" />
                        </div>

                    </ng-template>
                </ui-list-column>
            </ui-list>
        `
    })
};
