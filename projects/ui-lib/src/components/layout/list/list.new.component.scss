@use 'sass:meta';

:where(:root[data-uinew]) :host {
    display: block;
    width: 100%;

    --grid: 1fr;
    --full-width-cell: 1 / 1;
    --grid-size: 100px;

    &.selectable {
        user-select: none;
    }

    .list-body {
        display: block;
        position: relative;
    }

    .sticky {
        position: sticky !important;
        top: 0;
        z-index: 1;
    }
}

@include meta.load-css('style/grid.new');
@include meta.load-css('style/row.new');
@include meta.load-css('style/cell.new');
