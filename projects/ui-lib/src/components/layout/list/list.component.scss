@use 'sass:meta';
@use '../../../style/mixins/index';

:where(:root:not([data-uinew])) :host {
    display: block;
    width: 100%;
    border-radius: 2px;
    border: 1px solid var(--ui-color-border);

    --grid: 1fr;
    --full-width-cell: 1 / 1;
    --grid-size: 100px;

    &.selectable {
        user-select: none;
    }

    .list-body {
        display: block;
        width: 100%;
        position: relative;
        transition: opacity 0.2s ease;
        opacity: 0;

        &.visible {
            opacity: 1;
        }
    }

    .sticky {
        position: sticky !important;
        top: 0;
        z-index: 1;
    }

    .list-cell {
        cursor: pointer;
    }

    .non-folder + .folder {
        box-shadow: 0 -1px 0 var(--ui-color-border-second);
    }
}

@include meta.load-css('style/grid');
@include meta.load-css('style/row');
@include meta.load-css('style/cell');
