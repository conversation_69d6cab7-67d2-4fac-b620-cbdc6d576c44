import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    inject,
    input,
    Input,
    Output
} from '@angular/core';
import { UILogoComponent } from '../../logo/logo.component';
import { UINewThemeService } from '../../../services';

export type UIHeaderType = 'solid' | 'transparent';

/**
 * UIHeaderComponent is meant to be used as a universal topbar wrapper.
 *
 * NUI: NUI introduced space three component placeholder, "left", "middle" and "right"
 * NUI removed logos and the "full" option
 * NUI introduced "type", which changes the background color of the header
 *
 * To keep backwards compatibility, we'll use a fallback too.
 *
 * Usage:
 * ```html
 * <ui-header>
 *   <div left> <ui-logo/></div>
 *   <div middle> <span> My Awesome app </span> </div>
 *   <div right> <ui-svg-icon icon="close"/> </div>
 * </ui-header>
 * ```
 *
 * Nui Styel params:
 * `--grid-template-columns`: Sets the `grid-template-columns` for `.content`
 */
@Component({
    imports: [UILogoComponent],
    selector: 'ui-header',
    templateUrl: 'header.component.html',
    styleUrls: ['header.component.scss', './header.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.ui-header]': 'true',
        '[class.full]': 'full',
        '[class]': 'type()'
    }
})
export class UIHeaderComponent {
    private nuiThemeService = inject(UINewThemeService);

    // @deprecated with NUI
    @Output()
    logoClick: EventEmitter<MouseEvent> = new EventEmitter();

    // @deprecated with NUI
    @Input() full: boolean;

    // @deprecated with NUI
    @Input() showLogo = true;

    type = input<UIHeaderType>('solid');

    isNewUI = this.nuiThemeService.isNewThemeEnabled;

    public onLogoClick(event: MouseEvent): void {
        this.logoClick.emit(event);
    }
}
