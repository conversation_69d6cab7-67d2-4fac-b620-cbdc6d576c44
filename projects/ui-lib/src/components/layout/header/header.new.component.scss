:where(:root[data-uinew]) {
    :host {
        --grid-template-columns: 1fr 1fr 1fr;

        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        width: 100%;
        padding: var(--nui-header-space-padding-vertical, 12px) var(--nui-header-space-padding-horizontal, 20px);
        justify-content: space-between;
        align-items: center;
        border-bottom: var(--nui-border-width-small, 1px) solid var(--nui-header-fill-transparent-default);

        .content {
            width: inherit;
            justify-content: inherit;
            align-items: inherit;
            z-index: 1;
            display: grid;
            grid-template-columns: var(--grid-template-columns);
            grid-template-rows: 1fr;
            background-color: transparent;

            .left {
                grid-column: 1;
                display: flex;
                align-items: center;
            }

            .middle {
                grid-column: 2;
                width: auto;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .right {
                grid-column: 3;
                width: auto;
                display: flex;
                justify-content: flex-end;
                align-items: center;
            }
        }

        &.auto {
            .content {
                grid-template-columns: auto auto auto;
            }
        }

        &.transparent {
            background-color: var(--nui-header-fill-transparent-default, rgba(255, 255, 255, 0%));
            border-color: var(--nui-header-fill-transparent-default, rgba(255, 255, 255, 0%));
        }

        &.solid {
            border-color: var(--nui-header-border-solid-default, #c1cbd9);
            background-color: var(--nui-header-fill-solid-default, #fff);
        }
    }
}
