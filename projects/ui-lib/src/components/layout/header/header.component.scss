:where(:root:not([data-uinew])) {
    :host {
        position: absolute;
        height: var(--ui-view-header-height);
        left: 0;
        right: 0;
        top: 0;
        overflow: visible;
        padding: 0 25px;
        z-index: 100;
        box-shadow: 0 1px 0 0 var(--ui-color-border);
        transition: background-color 0.2s ease;
        background-color: var(--background-color);
        color: var(--text-color);

        .logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            height: var(--logo-height, 2.8rem);
            width: calc(var(--logo-width, 2.8rem) / 0.25);
            cursor: pointer;

            &.logo-large {
                display: none;

                @media only screen and (width >=768px) {
                    display: block;
                }
            }

            &.logo-small {
                @media only screen and (width >=768px) {
                    display: none;
                }
            }
        }

        .content {
            margin: 0 auto;
            overflow: visible;
            max-width: 1250px;
            height: 100%;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr;
            background-color: transparent;

            .left {
                grid-column: 1;
                grid-row: 1;
                display: flex;
                align-items: center;
                gap: var(--nui-space-300, 12px);
            }

            .middle {
                grid-column: 2;
                grid-row: 1;
                width: auto;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .right {
                grid-column: 3;
                grid-row: 1;
                width: auto;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: var(--nui-header-space-gap-right, 16px);
            }

            .fallback {
                grid-column: 1 / 4;
                grid-row: 1;
                width: inherit;
                display: block;
            }
        }
    }

    :host(.full) {
        padding: 0;

        .content {
            max-width: none;
            margin: 0;
        }
    }

    :host-context([ui-theme*='minimal']) {
        :host {
            box-shadow: 0 1px 0 0 rgba(0, 0, 0, 5%);
        }
    }
}
