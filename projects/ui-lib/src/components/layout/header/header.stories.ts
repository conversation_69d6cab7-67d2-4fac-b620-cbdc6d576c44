import { CdkScrollableModule } from '@angular/cdk/scrolling';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UISVGIconComponent } from '../../icon';
import { UILogoComponent } from '../../logo';
import { UIHeaderComponent } from './header.component';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';

const meta: Meta<UIHeaderComponent> = {
    title: 'Components/Layout/Header',
    component: UIHeaderComponent,
    decorators: [
        moduleMetadata({
            imports: [CdkScrollableModule, UILogoComponent, UISVGIconComponent]
        })
    ]
};
export default meta;

type Story = StoryObj<UIHeaderComponent>;

export const Default: Story = {
    args: {}
};

export const NuiDefault: Story = {
    ...NUI_STORY_SETTINGS,
    argTypes: {
        type: {
            control: 'select',
            options: ['transparent', 'solid'],
            defaultValue: 'solid',
            name: 'Type'
        }
    },
    args: {},
    render: args => ({
        args,
        template: `
 <ui-header [showLogo]="false" [full]="true">
   <div left> <ui-logo [style.height]="'12px'" [style.width]="'100px'"/></div>
   <div middle> <span> My Awesome app </span> </div>
   <div right>
        <ui-svg-icon [icon]="'kebab'" [nuiIcon]="'settings'"/>
        <ui-svg-icon icon="close" nuiIcon="close"/>
    </div>
 </ui-header>
`
    })
};
