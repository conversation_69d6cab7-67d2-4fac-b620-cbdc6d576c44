<div class="content">
    @if (!isNewUI() && showLogo) {
        <ui-logo
            class="logo logo-small"
            [small]="true"
            (click)="onLogoClick($event)">
        </ui-logo>
        <ui-logo
            class="logo logo-large"
            (click)="onLogoClick($event)">
        </ui-logo>
    }
    <div class="left">
        <ng-content select="[left]"></ng-content>
    </div>
    <div class="middle">
        <ng-content select="[middle]"></ng-content>
    </div>
    <div class="right">
        <ng-content select="[right]"></ng-content>
    </div>
    <!-- fallback -->
    <div class="fallback">
        <ng-content></ng-content>
    </div>
</div>
