@use '../../../style/mixins';

.cdk-table-sticky {
    border-bottom: 0.1rem solid var(--ui-color-border);
    border-top: 0.1rem solid var(--ui-color-border);
    background-color: var(--ui-color-surface-second);
}

:where(:root:not([data-uinew])) :host {
    --table-border-collapse: collapse;
    --table-border-right: 0.1rem solid var(--ui-color-border);
    --table-border-left: 0.1rem solid var(--ui-color-border);
    --table-border-top: 0.1rem solid var(--ui-color-border);
    --table-border-bottom: 0.1rem solid var(--ui-color-border);

    table {
        width: 100%;
        border-spacing: 0;
        border-collapse: var(--table-border-collapse);
        border-right: var(--table-border-right);
        border-left: var(--table-border-left);
        border-top: var(--table-border-top);
        border-bottom: var(--table-border-bottom);
    }

    .row-header {
        height: 3rem;
        background-color: var(--ui-color-surface);
        border-bottom: 0.1rem solid var(--default-color-grey);
    }

    // ng-deep to be able to reuse this class outside this component if you send in custom html
    ::ng-deep .cell-header {
        text-align: left;
        padding: 0 1rem;
        border-right: 0.1rem solid var(--default-color-grey);

        &:last-child {
            border-right: none;
        }

        &.selectable {
            padding-top: 0.5rem;
            text-align: center;
            width: 4.2rem;
            flex: none;
        }

        &.kebab {
            width: 4.2rem;
            flex: none;
        }

        .cell-header-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    // ng-deep to be able to reuse this class outside this component if you send in custom html
    ::ng-deep .text {
        color: var(--ui-color-text);
        font-weight: var(--ui-font-weight);
    }

    .row {
        height: 46px;
        background-color: var(--ui-color-surface);

        &.odd {
            background-color: var(--ui-color-surface-table);
        }

        &:hover {
            background-color: var(--ui-color-surface-highlight);
            cursor: pointer;
        }

        &.selected {
            background-color: var(--ui-color-surface-row-selected);
        }
    }

    ::ng-deep .mat-sort-header-content,
    .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 0;
    }

    // ng-deep to be able to reuse this class outside this component if you send in custom html
    ::ng-deep .cell {
        padding-left: 1rem;

        &.selectable {
            padding-left: 0;
            padding-top: 5px;
            text-align: center;
            flex: none;
        }

        &.kebab {
            padding-left: 0;
            text-align: center;
            flex: none;
            color: var(--ui-color-text);
        }
    }

    .sort {
        margin-right: 1rem;
    }

    .no-data {
        font-style: italic;
        text-align: center;
    }

    .ui-dropzone-allowed:hover {
        cursor: grabbing;
        background-color: var(--ui-color-surface-dropzone);
    }

    .ui-dropzone-not-allowed:hover {
        cursor: not-allowed;
        background-color: var(--ui-color-surface-highlight);
    }
}
