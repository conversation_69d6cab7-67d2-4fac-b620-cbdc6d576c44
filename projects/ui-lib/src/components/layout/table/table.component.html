<table
    cdk-table
    [dataSource]="dataSource"
    aria-label="table"
    fixedLayout>
    <!-- Select START -->
    @if (selection) {
        <ng-container cdkColumnDef="select">
            <th
                cdk-header-cell
                *cdkHeaderCellDef
                class="cell-header selectable">
                @if (!loading) {
                    <ui-checkbox
                        (click)="masterToggle()"
                        data-testid="master-toggle"
                        [selected]="masterSelected"
                        [indeterminate]="indeterminate"
                        [emitOnInputValueChange]="false">
                    </ui-checkbox>
                }
            </th>
            <td
                cdk-cell
                *cdkCellDef="let row; let i = index"
                class="cell selectable">
                @if (!loading) {
                    <ui-checkbox
                        (click)="$event.stopPropagation()"
                        [attr.data-testid]="'toggle-' + i"
                        [selected]="selection.isSelected(row)"
                        (selectedChange)="toggleSelection(row)"
                        [disabled]="isRowDisabled(row)">
                    </ui-checkbox>
                }
            </td>
        </ng-container>
    }
    <!-- Select END -->

    <!-- Custom html projection START -->
    <!-- See example of how to use this at the bottom of this file  -->
    <ng-content></ng-content>
    <!-- Custom html projection END -->

    @for (column of columnNames; track column.columnDef) {
        <ng-container [cdkColumnDef]="column.columnDef">
            <!-- Angular does not support adding directives dynamically, so unfortunately we need duplicated code #notSortable-->
            @if (column.sortable) {
                <th
                    cdk-header-cell
                    *cdkHeaderCellDef
                    [mat-sort-header]="column.columnDef"
                    class="cell-header text"
                    [ngStyle]="{ width: column.width }">
                    @if (isNewUI()) {
                        <ui-label
                            [weight]="'bold'"
                            class="ellipsis"
                            [truncate]="true"
                            [uiTooltip]="column.name"
                            [uiTooltipOnlyWhenTruncated]="true">
                            {{ column.name }}
                        </ui-label>
                    } @else {
                        <div
                            class="ellipsis"
                            [uiTooltip]="column.name"
                            [uiTooltipOnlyWhenTruncated]="true">
                            {{ column.name }}
                        </div>
                    }
                </th>
            } @else {
                <th
                    cdk-header-cell
                    *cdkHeaderCellDef
                    class="cell-header text"
                    [ngStyle]="{ width: column.width }">
                    @if (isNewUI()) {
                        <ui-label
                            [weight]="'bold'"
                            class="ellipsis"
                            [truncate]="true"
                            [uiTooltip]="column.name"
                            [uiTooltipOnlyWhenTruncated]="true">
                            {{ column.name }}
                        </ui-label>
                    } @else {
                        <div
                            class="ellipsis"
                            [uiTooltip]="column.name"
                            [uiTooltipOnlyWhenTruncated]="true">
                            {{ column.name }}
                        </div>
                    }
                </th>
            }
            <ng-template #notSortable>
                <th
                    cdk-header-cell
                    *cdkHeaderCellDef
                    class="cell-header text"
                    [ngStyle]="{ width: column.width }">
                    @if (isNewUI()) {
                        <ui-label
                            [weight]="'bold'"
                            class="ellipsis"
                            [truncate]="true"
                            [uiTooltip]="column.name"
                            [uiTooltipOnlyWhenTruncated]="true">
                            {{ column.name }}
                        </ui-label>
                    } @else {
                        <div
                            class="ellipsis"
                            [uiTooltip]="column.name"
                            [uiTooltipOnlyWhenTruncated]="true">
                            {{ column.name }}
                        </div>
                    }
                </th>
            </ng-template>
            <td
                cdk-cell
                *cdkCellDef="let item"
                class="cell text ellipsis"
                [uiTooltip]="item[column.columnDef]"
                [uiTooltipOnlyWhenTruncated]="true">
                @if (loading) {
                    <ui-skeleton-cell></ui-skeleton-cell>
                } @else {
                    <ui-label
                        class="ellipsis"
                        [truncate]="true"
                        [uiTooltip]="column.name"
                        [uiTooltipOnlyWhenTruncated]="true"
                        [type]="isRowDisabled(item) ? 'disabled' : 'primary'">
                        {{ item[column.columnDef] }}
                    </ui-label>
                }
                <ng-template #text>
                    <ui-label
                        class="ellipsis"
                        [truncate]="true"
                        [uiTooltip]="column.name"
                        [uiTooltipOnlyWhenTruncated]="true"
                        [type]="isRowDisabled(item) ? 'disabled' : 'primary'">
                        {{ item[column.columnDef] }}
                    </ui-label>
                </ng-template>
            </td>
        </ng-container>
    }

    <!-- Actions START -->
    @if (rowActions?.length) {
        @for (action of rowActions; track $index; let i = $index) {
            <ng-container [cdkColumnDef]="getActionColumnDef(i)">
                <th
                    cdk-header-cell
                    *cdkHeaderCellDef
                    class="cell-header actions"></th>
                <td
                    cdk-cell
                    *cdkCellDef="let row"
                    class="cell actions"
                    (click)="$event.stopPropagation()">
                    <div class="action-button-wrapper">
                        <ui-button
                            [nuiType]="'plain-primary'"
                            [nuiSvgIcon]="action.nuiIcon"
                            [uiTooltip]="action.tooltip"
                            [disabled]="isActionDisabled(action, row)"
                            (click)="
                                !isActionDisabled(action, row) && action.action(row);
                                $event.stopPropagation()
                            ">
                        </ui-button>
                    </div>
                </td>
            </ng-container>
        }
    }
    <!-- Actions END -->

    <!-- Kebabmenu START -->
    @if (menuConfig) {
        <ng-container cdkColumnDef="kebabmenu">
            <th
                cdk-header-cell
                *cdkHeaderCellDef
                class="cell-header kebab"></th>
            <td
                cdk-cell
                *cdkCellDef="let row"
                class="cell kebab"
                (click)="$event.stopPropagation()">
                @if (isNewUI()) {
                    <ui-button
                        nuiType="plain-primary"
                        nuiSvgIcon="more_vert"
                        [disabled]="isRowDisabled(row)"
                        [uiDropdownTarget]="isRowDisabled(row) ? undefined : optionsDropdown">
                    </ui-button>
                } @else {
                    <ui-svg-icon
                        icon="kebab"
                        [uiDropdownTarget]="isRowDisabled(row) ? undefined : optionsDropdown">
                    </ui-svg-icon>
                }

                <ui-dropdown
                    theme="default"
                    [header]="row[menuConfig.headerTextProp]"
                    #optionsDropdown>
                    @if (!loading) {
                        @for (option of menuConfig.options; track option.displayText) {
                            <ui-dropdown-item
                                [id]="'interaction-kebab-' + option.displayText"
                                [svgIcon]="option.svgIcon ? option.svgIcon : ''"
                                [disabled]="option.disabled || isRowDisabled(row)"
                                (click)="emitMenuOption(option, row)">
                                {{ option.displayText }}
                            </ui-dropdown-item>
                        }
                    }
                </ui-dropdown>
            </td>
        </ng-container>
    }
    <!-- Kebabmenu END -->

    <tr
        cdk-header-row
        *cdkHeaderRowDef="columnDefs; sticky: config.tableHeaderSticky"
        class="row-header"></tr>

    <!-- Until we can check if an ng-content is empty without wrapping it in a obsolete div
  https://github.com/angular/angular/issues/12530
  or we can send in a customrow as templateoutlet, we unfortunately have to do it this way 😢 -->
    @if (config.dragConfig) {
        <tr
            cdk-row
            *cdkRowDef="let row; columns: columnDefs; let i = index"
            [attr.data-testid]="'ui-table-row-' + i"
            class="row"
            [ngClass]="{
                odd: i % 2 === 0,
                selected: selection && selection.isSelected(row),
                disabled: isRowDisabled(row)
            }"
            (click)="!loading && !isRowDisabled(row) && emitRow($event, row)"
            uiDragDrop
            [disabled]="isRowDisabled(row)"
            [customComponent]="{
                props: {
                    text: selection.isSelected(row)
                        ? 'Move ' + selection.selected.length + ' elements'
                        : 'Move element'
                }
            }"
            [dragData]="selection.isSelected(row) ? selection.selected : [row]"
            [dropData]="row"
            (itemsDropped)="itemsDropped.emit($event)"
            [isAllowedToDrop]="config.dragConfig.isAllowedToDrop"></tr>
    } @else {
        <tr
            cdk-row
            *cdkRowDef="let row; columns: columnDefs; let i = index"
            [attr.data-testid]="'ui-table-row-' + i"
            class="row"
            [ngClass]="{
                odd: i % 2 === 0,
                selected: selection && selection.isSelected(row),
                disabled: isRowDisabled(row)
            }"
            (click)="!loading && !isRowDisabled(row) && emitRow($event, row)"></tr>
    }

    <ng-container *cdkNoDataRow>
        <tr class="row">
            @if (loading) {
                @for (column of columnDefs; track column) {
                    @if (column === 'select' || column === 'kebabmenu') {
                        <td class="cell text"></td>
                    } @else {
                        <td class="cell text"><ui-skeleton-cell></ui-skeleton-cell></td>
                    }
                }
            } @else {
                <td
                    class="no-data"
                    [attr.colspan]="columnDefs.length"
                    [innerHTML]="noDataText"></td>
            }
        </tr>
    </ng-container>
</table>

<!-- EXAMPLE CUSTOM HTML USING NG-PROJECTION  -->
<!--  Dont forget to add your cdkColumnDef to columnDef input with isCustom flag ¯\_(ツ)_/¯ -->

<!-- <ui-table style="width: 100%;"[dataSource]="data" [columnNames]="columnNames" [selection]="selectionModel" [menuOptions]="menuOptions">

<ng-container cdkColumnDef="random">

  <th cdk-header-cell *cdkHeaderCellDef class="cell-header text">
    <div class="cell-header-wrapper">
      <div>Random</div>
    </div>
  </th>
  <td cdk-cell *cdkCellDef="let row;" class="cell text">
    <p style="color: red;">
      {{ row.random }}
    </p>
  </td>
</ng-container>

<ng-container cdkColumnDef="anotherone">

  <th cdk-header-cell *cdkHeaderCellDef class="cell-header text">
    <div class="cell-header-wrapper">
      <div>Another one</div>
    </div>
  </th>
  <td cdk-cell *cdkCellDef="let row;" class="cell text">
    <ui-input></ui-input>
  </td>
</ng-container>

</ui-table> -->
