import { SelectionModel } from '@angular/cdk/collections';
import { OverlayModule } from '@angular/cdk/overlay';
import { CdkTableModule } from '@angular/cdk/table';
import { CommonModule, Ng<PERSON>lass, NgF<PERSON>, <PERSON>If, <PERSON><PERSON><PERSON>le, NgTemplateOutlet } from '@angular/common';
import { MatSortModule, Sort } from '@angular/material/sort';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { expect, userEvent, within } from '@storybook/test';
import { UIComponentSizeDirective, UIDragDropDirective } from '../../../directives';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIButtonComponent } from '../../buttons';
import { UIIconComponent, UISVGIconComponent } from '../../icon';
import { UICheckboxComponent, UIInlineEditComponent } from '../../inputs';
import { UILabelComponent } from '../../label';
import { UIPillComponent, UIPillDirective } from '../../pill';
import {
    UIDropdownComponent,
    UIDropdownItemComponent,
    UIDropdownTargetDirective,
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverTemplateDirective,
    UITooltipComponent,
    UITooltipDirective
} from '../../popovers';
import { UISkeletonCellComponent } from './skeleton-cell';
import { MenuAction, UITableComponent } from './table.component';
import { UITableDataSource } from './table.datasource';

interface MockTest {
    id: string;
    name: string;
    createdBy: string;
    created: string;
    creatives: string;
    isFolder?: boolean;
    pill?: string;
    priority?: number;
    editableNotes?: string;
}

const meta: Meta<UITableComponent<MockTest>> = {
    title: 'Components/Layout/Table',
    component: UITableComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIButtonComponent,
                UIIconComponent,
                UISVGIconComponent,
                UICheckboxComponent,
                UITableComponent,
                UIInlineEditComponent,
                UIDropdownComponent,
                UIPopoverDirective,
                UIDropdownTargetDirective,
                UIPopoverComponent,
                UIPopoverTemplateDirective,
                UIDropdownItemComponent,
                UITooltipDirective,
                UITooltipComponent,
                CommonModule,
                OverlayModule,
                BrowserAnimationsModule,
                NgIf,
                NgFor,
                NgTemplateOutlet,
                NgStyle,
                NgClass,
                CdkTableModule,
                MatSortModule,
                UISkeletonCellComponent,
                UIDragDropDirective,
                UILabelComponent,
                UIPillDirective,
                UIPillComponent,
                UIComponentSizeDirective
            ]
        })
    ]
};
export default meta;

type Story = StoryObj<UITableComponent<MockTest>>;

// --- Empty table
const emptyData = new UITableDataSource<MockTest>();

emptyData.setData([]);

export const NoDataStory: Story = {
    args: {
        dataSource: emptyData,
        selection: new SelectionModel<MockTest>(true, []),
        menuConfig: {
            headerTextProp: 'name',
            options: [
                { displayText: 'Delete', menuActions: MenuAction.DELETE, svgIcon: 'delete' },
                { displayText: 'Duplicate', menuActions: MenuAction.DUPLICATE, svgIcon: 'duplicate-s' }
            ]
        },
        columnNames: [
            { name: 'Name', columnDef: 'name' },
            { name: 'Created by', columnDef: 'createdBy' },
            { name: 'Created', columnDef: 'created' },
            { name: 'Creatives', columnDef: 'creatives' }
        ]
    }
};

// --- Creativesets table
const rows: MockTest[] = [
    {
        id: '1',
        name: 'Duobam',
        creatives: '13',
        createdBy: 'Durant Chezelle',
        created: '7/20/2021',
        isFolder: true
    },
    {
        id: '2',
        name: 'Y-Solowarm',
        creatives: '88',
        createdBy: 'Napoleon Spring',
        created: '4/13/2022'
    },
    {
        id: '3',
        name: 'Latlux',
        creatives: '88',
        createdBy: 'Aili Antrum',
        created: '7/19/2021'
    },
    {
        id: '4',
        name: 'Tres-Zap',
        creatives: '52',
        createdBy: 'Mathian Sunley',
        created: '9/3/2021'
    },
    {
        id: '5',
        name: 'Zathin',
        creatives: '71',
        createdBy: 'Winna Tomaskov',
        created: '5/18/2021'
    }
];

const creativesetsData = new UITableDataSource<MockTest>();
creativesetsData.setData(rows);

export const Creativesets: Story = {
    args: {
        dataSource: creativesetsData,
        selection: new SelectionModel<MockTest>(true, []),
        menuConfig: {
            headerTextProp: 'name',
            options: [
                { displayText: 'Delete', menuActions: MenuAction.DELETE, svgIcon: 'delete' },
                { displayText: 'Duplicate', menuActions: MenuAction.DUPLICATE, svgIcon: 'duplicate-s' }
            ]
        },
        columnNames: [
            { name: 'Name', columnDef: 'name' },
            { name: 'Created by', columnDef: 'createdBy' },
            { name: 'Created', columnDef: 'created' },
            { name: 'Creatives', columnDef: 'creatives' }
        ]
    },
    play: async ({ canvasElement }) => {
        const canvas = within(canvasElement);

        const masterToggle = await canvas.findByTestId('master-toggle');
        const firstToggle = await canvas.findByTestId('toggle-0');
        await userEvent.click(masterToggle);
        await userEvent.click(firstToggle);
        const toggleIcon = within(masterToggle).getByTestId('svg-icon');

        expect(toggleIcon).toHaveClass('icon-indeterminate-checkmark');
    }
};

const mockData: MockTest[] = [
    {
        id: '1',
        name: 'Marketing',
        creatives: '13',
        createdBy: 'John Smith',
        created: '2024-01-15',
        isFolder: true,
        pill: 'info'
    },
    {
        id: '2',
        name: 'Other',
        creatives: '45',
        createdBy: 'Bob Wilson',
        created: '2024-01-13',
        isFolder: true,
        pill: 'success'
    },
    {
        id: '3',
        name: 'Creativeset1',
        creatives: '88',
        createdBy: 'Alice Johnson',
        created: '2024-01-14',
        pill: 'info'
    },
    {
        id: '4',
        name: 'Creativeset2',
        creatives: '52',
        createdBy: 'Emma Davis',
        created: '2024-01-12',
        pill: 'warning'
    },
    {
        id: '5',
        name: 'Creativeset3',
        creatives: '71',
        createdBy: 'Mike Brown',
        created: '2024-01-11',
        pill: 'error'
    },
    {
        id: '6',
        name: 'Creativeset4',
        creatives: '31',
        createdBy: 'John Doe',
        created: '2024-01-21',
        pill: 'error'
    },
    {
        id: '7',
        name: 'Creativeset5',
        creatives: '31',
        createdBy: 'John Doe',
        created: '2024-01-21',
        pill: 'success'
    }
];

const dataSource = new UITableDataSource<MockTest>();
dataSource.setData(mockData);

export const NUITable: Story = {
    ...NUI_STORY_SETTINGS,
    args: {
        dataSource,
        selection: new SelectionModel<MockTest>(true),
        config: {
            dragConfig: {
                isAllowedToDrop: (dropTarget: MockTest | string) =>
                    typeof dropTarget === 'object' && dropTarget.isFolder === true
            },
            isRowDisabled: (row: MockTest) => ['4', '3'].includes(row.id)
        },
        menuConfig: {
            headerTextProp: 'name',
            options: [
                { displayText: 'Delete', menuActions: MenuAction.DELETE, svgIcon: 'delete' },
                { displayText: 'Duplicate', menuActions: MenuAction.DUPLICATE, svgIcon: 'duplicate-s' },
                { displayText: 'Move', menuActions: MenuAction.MOVE, svgIcon: 'folder-move' },
                { displayText: 'Rename', menuActions: MenuAction.RENAME, svgIcon: 'edit' }
            ]
        },
        rowActions: [
            {
                nuiIcon: 'backup',
                tooltip: 'Backup',
                action: (row: MockTest) => alert(`Backup action on ${row.name}`)
            },
            {
                nuiIcon: 'preview',
                tooltip: 'Preview',
                action: (row: MockTest) => alert(`Preview action on ${row.name}`)
            },
            {
                nuiIcon: 'history',
                tooltip: 'History',
                action: (row: MockTest) => alert(`History action on ${row.name}`),
                disabled: (row: MockTest) => !!row.isFolder
            }
        ],
        columnNames: [
            { name: 'Editable Name', columnDef: 'name', sortable: true, isCustom: true },
            { name: 'Created by', columnDef: 'createdBy', sortable: true },
            { name: 'Created', columnDef: 'created', sortable: true },
            { name: 'Pill', columnDef: 'pill', isCustom: true, sortable: true }
        ]
    },
    render: args => {
        const changeSorting = (sort: Sort) => {
            if (!sort.active || sort.direction === '') {
                args.dataSource.setData([...mockData]);
                return;
            }
            const sortedData = [...mockData].sort((a, b) => {
                const prop = sort.active as keyof MockTest;
                return (
                    String(a[prop]).localeCompare(String(b[prop])) * (sort.direction === 'asc' ? 1 : -1)
                );
            });
            args.dataSource.setData(sortedData);
        };

        return {
            props: {
                ...args,
                changeSorting,
                onItemsDropped: (event: { droppedItems: MockTest[]; dropTarget: MockTest }) => {
                    alert(
                        `Dropped ${event.droppedItems?.length} item(s) into folder: ${event.dropTarget.name}`
                    );
                },
                onMenuOptionClicked: (option: any) => {
                    alert(`Clicked ${option.displayText} on item: ${option.data.name}`);
                },
                onNotesSaved: (event: any, row: MockTest) => {
                    const oldName = row.name;
                    row.name = event.newValue;
                    alert(`Name for item (ID: ${row.id}) saved: '${row.name}' (Old: '${oldName}')`);
                }
            },
            template: `
            <div style="padding: 20px;">
                <h3 [uiSize]="'sm'">
                    This story showcases the various features of the <code>ui-table</code> component.
                </h3>
                <ul style="margin-bottom: 20px;">
                    <li><b>Multiple Selection:</b> Click checkboxes to select single or multiple rows. Use the header checkbox to select/deselect all.</li>
                    <li><b>Sortable Columns:</b> Make a column sortable by setting <code>sortable: true</code> in its definition. The parent component implements the logic using the <code>matSort</code> directive and the <code>(matSortChange)</code> event.</li>
                    <li><b>Drag & Drop:</b> Drag and drop items onto folders.</li>
                    <li><b>Row Actions:</b> Hover over a row to see action icons for "Backup", "Preview", and "History" (in this example "History" action is disabled for folders).</li>
                    <li><b>Custom Column Rendering:</b> The "Editable Name" column uses inline editing, and the "Pill" column uses the <code>ui-pill</code> directive for custom styling.</li>
                    <li><b>Context Menu:</b> Click the kebab menu on the right to see more options like "Delete", "Duplicate", etc.</li>
                    <li><b>Disabled Rows:</b> Some rows are disabled and cannot be interacted with.</li>
                    <li><b>Sizes:</b> The table can be displayed in different sizes: <code>md</code>, <code>sm</code>, and <code>xs</code>. <strong style="color: red;">(the data in this story is shared for all sizes so if any change is made in one size, it will be reflected in the other sizes)</strong></li>
                </ul>

                <h3 [uiSize]="'sm'"> Medium size</h3>
                <ui-table
                    #table
                    [dataSource]="dataSource"
                    [columnNames]="columnNames"
                    [selection]="selection"
                    [config]="config"
                    [menuConfig]="menuConfig"
                    [rowActions]="rowActions"
                    [size]="'md'"
                    matSort
                    (matSortChange)="changeSorting($event)"
                    (itemsDropped)="onItemsDropped($event)"
                    (menuOptionClicked)="onMenuOptionClicked($event)">

                    <!-- Custom column names with inline edit -->
                    <ng-container cdkColumnDef="name">
                        <th cdk-header-cell *cdkHeaderCellDef [mat-sort-header]="'name'" class="cell-header text">
                            <ui-label [weight]="'bold'">Editable Name</ui-label>
                        </th>
                        <td cdk-cell *cdkCellDef="let row" class="cell text">
                            <ui-inline-edit
                                [value]="row.name"
                                [leadingIcon]="row.isFolder ? 'folder' : undefined"
                                [size]="'md'"
                                [disabled]="table.isRowDisabled(row)"
                                (save)="onNotesSaved($event, row)">
                            </ui-inline-edit>
                        </td>
                    </ng-container>

                    <!-- Pill Column -->
                    <ng-container cdkColumnDef="pill">
                        <th cdk-header-cell *cdkHeaderCellDef [mat-sort-header]="'pill'" class="cell-header text">
                            <ui-label [weight]="'bold'">Pill</ui-label>
                        </th>
                        <td cdk-cell *cdkCellDef="let row" class="cell text">
                            <ui-pill [variant]="row.pill" [type]="'secondary'" [size]="'md'">
                                {{ row.pill }}
                            </ui-pill>
                        </td>
                    </ng-container>
                </ui-table>

                <h3 [uiSize]="'sm'"> Small size</h3>
                <ui-table
                    #table
                    [dataSource]="dataSource"
                    [columnNames]="columnNames"
                    [selection]="selection"
                    [config]="config"
                    [menuConfig]="menuConfig"
                    [rowActions]="rowActions"
                    [size]="'sm'"
                    matSort
                    (matSortChange)="changeSorting($event)"
                    (itemsDropped)="onItemsDropped($event)"
                    (menuOptionClicked)="onMenuOptionClicked($event)">

                    <!-- Custom column names with inline edit -->
                    <ng-container cdkColumnDef="name">
                        <th cdk-header-cell *cdkHeaderCellDef [mat-sort-header]="'name'" class="cell-header text">
                            <ui-label [weight]="'bold'">Editable Name</ui-label>
                        </th>
                        <td cdk-cell *cdkCellDef="let row" class="cell text">
                            <ui-inline-edit
                                [value]="row.name"
                                [leadingIcon]="row.isFolder ? 'folder' : undefined"
                                [size]="'sm'"
                                [disabled]="table.isRowDisabled(row)"
                                (save)="onNotesSaved($event, row)">
                            </ui-inline-edit>
                        </td>
                    </ng-container>

                    <!-- Pill Column -->
                    <ng-container cdkColumnDef="pill">
                        <th cdk-header-cell *cdkHeaderCellDef [mat-sort-header]="'pill'" class="cell-header text">
                            <ui-label [weight]="'bold'">Pill</ui-label>
                        </th>
                        <td cdk-cell *cdkCellDef="let row" class="cell text">
                            <ui-pill [variant]="row.pill" [type]="'secondary'" [size]="'sm'">
                                {{ row.pill }}
                            </ui-pill>
                        </td>
                    </ng-container>
                </ui-table>

                <h3 [uiSize]="'sm'"> Extra small size</h3>
                <ui-table
                    #table
                    [dataSource]="dataSource"
                    [columnNames]="columnNames"
                    [selection]="selection"
                    [config]="config"
                    [menuConfig]="menuConfig"
                    [rowActions]="rowActions"
                    [size]="'xs'"
                    matSort
                    (matSortChange)="changeSorting($event)"
                    (itemsDropped)="onItemsDropped($event)"
                    (menuOptionClicked)="onMenuOptionClicked($event)">

                    <!-- Custom column names with inline edit -->
                    <ng-container cdkColumnDef="name">
                        <th cdk-header-cell *cdkHeaderCellDef [mat-sort-header]="'name'" class="cell-header text">
                            <ui-label [weight]="'bold'">Editable Name</ui-label>
                        </th>
                        <td cdk-cell *cdkCellDef="let row" class="cell text">
                            <ui-inline-edit
                                [value]="row.name"
                                [leadingIcon]="row.isFolder ? 'folder' : undefined"
                                [size]="'xs'"
                                [disabled]="table.isRowDisabled(row)"
                                (save)="onNotesSaved($event, row)">
                            </ui-inline-edit>
                        </td>
                    </ng-container>

                    <!-- Pill Column -->
                    <ng-container cdkColumnDef="pill">
                        <th cdk-header-cell *cdkHeaderCellDef [mat-sort-header]="'pill'" class="cell-header text">
                            <ui-label [weight]="'bold'">Pill</ui-label>
                        </th>
                        <td cdk-cell *cdkCellDef="let row" class="cell text">
                            <ui-pill [variant]="row.pill" [type]="'secondary'" [size]="'xs'">
                                {{ row.pill }}
                            </ui-pill>
                        </td>
                    </ng-container>
                </ui-table>

            </div>
        `
        };
    }
};

export const LoadingState: Story = {
    args: {
        dataSource,
        loading: true,
        columnNames: [
            { name: 'Name', columnDef: 'name' },
            { name: 'Created by', columnDef: 'createdBy' },
            { name: 'Created', columnDef: 'created' },
            { name: 'Pill', columnDef: 'pill' }
        ]
    }
};
