:where(:root[data-uinew]) :host {
    table {
        width: 100%;
        border-spacing: 0;
    }

    // ng-deep to be able to reuse this class outside this component if you send in custom html
    ::ng-deep .cell-header,
    ::ng-deep .cell {
        height: var(--nui-table-item-height);
        padding: 0 var(--nui-table-item-padding-horizontal);

        &.selectable {
            width: var(--nui-table-item-height);
            height: var(--nui-table-item-height);
            padding: 0;
            text-align: center;
            vertical-align: middle;

            ui-checkbox {
                display: inline-block;
            }
        }

        &.actions {
            padding: 0;
            width: var(--nui-table-item-height);

            .action-button-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
            }
        }

        &.kebab {
            padding-left: 0;
            text-align: center;
            color: var(--nui-label-text-neutral);
            width: var(--nui-table-item-height);
        }
    }

    // ng-deep to be able to reuse this class outside this component if you send in custom html
    ::ng-deep .cell-header {
        background: var(--nui-table-fill-even);
        height: var(--nui-table-item-height);

        .cell-header-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .row {
        height: var(--nui-table-row-height);
        background: var(--nui-table-fill-even);

        &:not(.disabled) {
            &:hover {
                background: var(--nui-table-fill-hover);
                cursor: pointer;
            }

            &.selected {
                background: var(--nui-table-fill-selected);
            }

            &:active {
                background: var(--nui-table-fill-pressed);
            }
        }

        &.odd {
            background: var(--nui-table-fill-odd);
        }

        &.disabled {
            cursor: not-allowed;
        }
    }

    .no-data {
        font-style: italic;
        text-align: center;
    }

    .ui-dropzone-allowed:hover {
        cursor: grabbing;
        background-color: var(--ui-color-surface-dropzone);
    }

    .ui-dropzone-not-allowed:hover {
        cursor: not-allowed;
        background-color: var(--ui-color-surface-highlight);
    }
}
