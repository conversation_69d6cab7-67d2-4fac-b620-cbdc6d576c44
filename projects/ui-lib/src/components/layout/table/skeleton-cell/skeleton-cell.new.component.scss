:where(:root[data-uinew]) :host {
    .skeleton-row {
        width: 30%;
        padding: var(--nui-space-200);
        background: var(--nui-skeleton-fill-default);
        background: linear-gradient(
            110deg,
            var(--nui-fill-transparent) 0%,
            var(--nui-skeleton-fill-default) 55%,
            var(--nui-fill-transparent) 100%
        );
        background-size: 200% 100%;
        animation: 1.5s shine linear infinite;
    }

    @keyframes shine {
        to {
            background-position-x: -200%;
        }
    }
}
