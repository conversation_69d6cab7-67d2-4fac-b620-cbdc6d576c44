:where(:root:not([data-uinew])) :host {
    .skeleton-row {
        width: 30%;
        padding: 8px;
        background: var(--ui-color-text-third);
        background: linear-gradient(
            110deg,
            var(--ui-color-text-third) 8%,
            var(--ui-color-surface-hovered) 18%,
            var(--ui-color-text-third) 33%
        );
        border-radius: 2px;
        background-size: 200% 100%;
        animation: 1.5s shine linear infinite;
    }

    @keyframes shine {
        to {
            background-position-x: -200%;
        }
    }
}
