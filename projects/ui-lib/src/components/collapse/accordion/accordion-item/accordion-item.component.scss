:where(:root:not([data-uinew])) :host {
    .icon {
        font-size: 16px;
        line-height: 20px;
        vertical-align: top;
        color: var(--ui-color-text-second);
    }

    .text {
        line-height: 20px;
        vertical-align: top;
        margin-left: 10px;
    }

    .header {
        background-color: var(--header-background-color);
        display: inline-block;
        align-items: center;
        color: var(--text-color);
        cursor: pointer;
        padding: var(--padding);
        width: 100%;
        border: none solid var(--ui-color-border);
        text-align: left;
        outline: none;
        font-size: var(--font-size);
        transition: all 0.2s ease;
        text-transform: uppercase;

        &.custom {
            display: flex;
            justify-content: space-between;
            text-transform: none;
        }

        &.left-side-caret {
            display: flex;
            padding-left: 0.4rem;
            flex-direction: row-reverse;
            justify-content: flex-end;

            .caret {
                float: left;
                margin-right: 5px;
            }
        }

        &:hover {
            background-color: var(--ui-color-surface-second);
        }
    }

    .caret {
        float: right;
        margin-left: 5px;
        line-height: 18px;
        transition: all 0.2s ease;
        opacity: 0.6;
    }

    .action {
        float: right;
        margin-right: 5px;
        line-height: 18px;
        transition: all 0.2s ease;
        opacity: 0.6;

        &:hover {
            background-color: var(--ui-color-surface-second);
        }
    }

    .content {
        background-color: var(--background-color);
        max-height: 0;
        overflow: hidden;
        transition: all 0.2s ease;

        .expanded {
            max-height: 200px;
        }

        .content-is-not-animated {
            transition: unset;
        }
    }

    .highlight {
        color: var(--ui-color-primary);
    }

    .error {
        color: var(--ui-color-alert) !important;
    }

    .expanded {
        border-bottom: 1px solid var(--ui-color-border);

        & > .caret {
            transform: rotate(180deg);
        }
    }

    .warningIcon {
        vertical-align: top;
        font-size: 1.6rem;
    }
}
