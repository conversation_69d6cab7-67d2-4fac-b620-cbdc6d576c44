import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    AfterContentChecked,
    Component,
    ContentChild,
    ElementRef,
    EventEmitter,
    inject,
    input,
    Input,
    Output,
    TemplateRef,
    viewChild
} from '@angular/core';
import { UIComponentSizeDirective } from '../../../../directives/component-size/component-size.directive';
import { DATASET_SIZE, UINewThemeService } from '../../../../services/uinew-theme.service';
import { UINUIIcon } from '../../../../types';
import { UIPillType, UIPillVariant } from '../../../../types/pill';
import { UIButtonComponent } from '../../../buttons/button/button.component';
import { Icon, UIIconComponent, UISVGIconComponent } from '../../../icon';
import { UICheckboxComponent } from '../../../inputs/checkbox/checkbox.component';
import { UIPillDirective } from '../../../pill/pill.directive';
import { UIAccordionHeaderTemplateDirective } from '../templates/ui-accordion-header-template.directive';

@Component({
    imports: [
        NgClass,
        NgTemplateOutlet,
        UIIconComponent,
        UISVGIconComponent,
        UICheckboxComponent,
        UIPillDirective,
        UIButtonComponent,
        UIComponentSizeDirective
    ],
    selector: 'ui-accordion-item',
    templateUrl: 'accordion-item.component.html',
    host: {
        '[class.simple]': 'simple',
        '[class.inset]': 'inset()',
        '[class.rich]': '!simple',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    },
    styleUrls: ['accordion-item.component.scss', 'accordion-item.new.component.scss']
})
export class UIAccordionItemComponent implements AfterContentChecked {
    private uiNewThemeService = inject(UINewThemeService);
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    /**
     * Text for the accordion header.
     *
     */
    @Input() header: string;

    /**
     * When true the accordion header is highlighted, set to false by default.
     *
     */
    @Input() highlightHeader: boolean;

    /**
     * When true the header is displayed as an error, set to false by default.
     *
     */
    @Input() highlightErrorHeader: boolean;

    /**
     * Prepend the Caret by reversing Order of the header content: Caret & Custom header.
     *
     */
    @Input() leftSideCaret: boolean;

    /**
     * controls whether content is animated by css transition property. check the style file.
     *
     */
    @Input() isContentAnimated = true;

    /**
     * Sets an icon beside the accordion header.
     *
     */
    @Input() icon: string;

    @Input() svgIcon: Icon;
    nuiSvgIcon = input<UINUIIcon>();

    @Input() actionIcon: string;
    nuiActionIcon = input<UINUIIcon>();

    @Input() expanded = false;

    // newUI stuff

    inset = input<boolean>(true);

    /**
     * Array of pills to display in the accordion header
     */
    @Input() pills: {
        text: string;
        variant?: UIPillVariant;
        type?: UIPillType;
        size?: 'xs' | 'sm' | 'md';
    }[];

    /**
     * Displays smaller text above the main header text to provide additional context
     */
    @Input() subHeadline: string;

    /**
     * When true, displays a checkbox at the start of the accordion header
     */
    @Input() checkbox: boolean;

    /**
     * Controls whether the header checkbox is checked or unchecked
     */
    @Input() checked: boolean;

    /**
     * Controls whether the header checkbox is indeterminate
     */
    indeterminate = input<boolean>(false);

    /**
     * When true, the accordion item is displayed as a simple accordion item
     */
    @Input() simple: boolean;

    readonly accordionItemContent = viewChild<ElementRef>('accordionItemContent');

    @Output('toggleChange') public toggleChange: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output('checkboxChecked') public checkboxChecked: EventEmitter<boolean> =
        new EventEmitter<boolean>();

    @Output('actionCallback') public actionCallback: EventEmitter<any> = new EventEmitter<any>();

    // TODO: Skipped for migration because:
    //  This query is used in a control flow expression (e.g. `@if` or `*ngIf`)
    //  and migrating would break narrowing currently.
    @ContentChild(UIAccordionHeaderTemplateDirective, { read: TemplateRef })
    headerTemplate?: TemplateRef<any>;

    contentHeight = '0px';

    isActionHovered: boolean;

    size = input<'xs' | 'sm' | 'md'>('md');

    toggleCheckbox(event: Event): void {
        event.stopPropagation();
        this.checked = !this.checked;
        this.checkboxChecked.emit(this.checked);
    }

    toggleAccordion(): void {
        if (!this.isActionHovered) {
            this.expanded = !this.expanded;
            this.toggleChange.emit(this.expanded);
        }
    }

    actionClicked(): void {
        this.actionCallback.emit();
    }

    ngAfterContentChecked(): void {
        this.checkContent();
    }

    checkContent(): void {
        const scrollHeight = `${this.accordionItemContent()?.nativeElement.scrollHeight || 0}px`;
        if (this.expanded) {
            this.contentHeight = scrollHeight;
        } else {
            this.contentHeight = '0px';
        }
    }
}
