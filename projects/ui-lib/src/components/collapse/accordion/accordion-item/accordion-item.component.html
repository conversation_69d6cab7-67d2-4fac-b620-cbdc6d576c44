<div class="container">
    @if (headerTemplate) {
        <div
            [ngClass]="{ expanded: expanded, 'left-side-caret': leftSideCaret }"
            class="header custom"
            (click)="toggleAccordion()">
            <ng-container
                *ngTemplateOutlet="
                    headerTemplate;
                    context: { $implicit: headerTemplate }
                "></ng-container>
            @if (!isNewUI()) {
                <div class="caret">
                    <ui-icon icon="arrow-down" />
                </div>
            }
        </div>
    }

    @if (!headerTemplate) {
        @if (isNewUI()) {
            <div
                [ngClass]="{
                    expanded: expanded,
                    highlight: highlightHeader,
                    error: highlightErrorHeader
                }"
                class="header"
                (click)="toggleAccordion()">
                <div class="left-side">
                    @if (nuiSvgIcon() && simple) {
                        <ui-svg-icon
                            [size]="size()"
                            [icon]="'none'"
                            [nuiIcon]="nuiSvgIcon()"></ui-svg-icon>
                    }
                    @if (checkbox) {
                        <ui-checkbox
                            [selected]="checked"
                            [indeterminate]="indeterminate()"
                            (click)="toggleCheckbox($event)" />
                    }

                    <span
                        class="text"
                        [uiSize]="size() === 'md' ? 'md' : 'sm'">
                        @if (subHeadline) {
                            <div
                                class="sub-headline"
                                uiSize="sm">
                                {{ subHeadline }}
                            </div>
                        }

                        {{ header }}
                    </span>
                </div>

                <div class="right-side">
                    @for (pill of pills; track pill.text) {
                        <div
                            uiPill
                            [ui-pill-variant]="pill.variant ?? 'info'"
                            [ui-pill-type]="pill.type ?? 'secondary'"
                            [ui-pill-size]="pill.size ?? 'md'">
                            {{ pill.text }}
                        </div>
                    }

                    @if (nuiActionIcon()) {
                        <ui-button
                            [type]="'plain-primary'"
                            [size]="size()"
                            (click)="actionClicked()"
                            (mouseover)="isActionHovered = true"
                            (mouseleave)="isActionHovered = false"
                            [icon]="'none'"
                            [nuiSvgIcon]="nuiActionIcon()"></ui-button>
                    }

                    <ui-button
                        class="caret"
                        [type]="'plain-primary'"
                        [size]="size()"
                        [icon]="'none'"
                        [nuiSvgIcon]="'keyboard_arrow_down'"></ui-button>
                </div>
            </div>
        } @else {
            <div
                [ngClass]="{
                    expanded: expanded,
                    highlight: highlightHeader,
                    error: highlightErrorHeader
                }"
                class="header"
                (click)="toggleAccordion()">
                <ui-icon
                    [icon]="icon"
                    class="icon"></ui-icon>
                <ui-svg-icon
                    [icon]="svgIcon"
                    class="icon"></ui-svg-icon>
                <span class="text">
                    @if (highlightErrorHeader) {
                        <ui-icon
                            icon="alert"
                            class="warningIcon"></ui-icon>
                    }
                    {{ header }}
                </span>

                <div class="caret">
                    <ui-icon icon="arrow-down"></ui-icon>
                </div>

                @if (actionIcon) {
                    <div class="action">
                        <ui-icon
                            (click)="actionClicked()"
                            (mouseover)="isActionHovered = true"
                            (mouseleave)="isActionHovered = false"
                            [icon]="actionIcon"></ui-icon>
                    </div>
                }
            </div>
        }
    }
    <div
        #accordionItemContent
        [style.max-height]="contentHeight"
        class="content"
        [uiSize]="simple ? 'sm' : 'md'"
        [ngClass]="{ expanded: expanded, 'content-is-not-animated': !isContentAnimated }">
        <ng-content [style]="!isNewUI() ? 'padding: 5%' : ''"></ng-content>
    </div>
</div>
