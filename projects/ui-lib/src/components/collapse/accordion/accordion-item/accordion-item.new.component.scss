:where(:root[data-uinew]) :host {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .container {
        width: 100%;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        cursor: pointer;
        text-align: left;
        font-family: var(--nui-title-font-family);
        font-size: var(--nui-title-font-size);
        font-weight: var(--nui-title-font-weight);
        line-height: var(--nui-title-line-height);
        letter-spacing: var(--nui-title-letter-spacing);

        &.custom {
            display: flex;
            justify-content: space-between;
            text-transform: none;
        }

        .left-side,
        .right-side {
            display: flex;
            align-items: center;
        }

        &.expanded {
            .caret {
                transform: rotate(180deg);
            }
        }

        .caret {
            transition: all 0.2s ease;
        }
    }

    .content {
        max-height: 0;
        overflow: hidden;
        transition: all 0.2s ease;
        font-size: var(--nui-body-regular-font-size);
        font-weight: var(--nui-body-regular-font-weight);
        line-height: var(--nui-body-regular-line-height);
        letter-spacing: var(--nui-body-regular-letter-spacing);
    }

    /* ===============simple accordion=============== */

    &[data-uisize='xs'] {
        &.simple {
            .header {
                font-size: var(--nui-label-bold-font-size);
                font-weight: var(--nui-label-bold-font-weight);
                line-height: var(--nui-label-bold-line-height);
                letter-spacing: var(--nui-body-bold-letter-spacing);
            }
        }
    }

    &.simple {
        color: var(--nui-accordion-simple-text-headline);
        border: transparent;
        background: var(--nui-accordion-simple-fill-default);
        gap: var(--nui-accordion-simple-space-gap);

        &.inset {
            padding: var(--nui-accordion-simple-space-padding-vertical)
                var(--nui-accordion-simple-space-padding-horizontal);
        }
        
        &:hover {
            background: transparent;
            border: transparent;
        }

        &:has(.header.expanded) {
            background: transparent;
            box-shadow: none;
            border: transparent;

            &:hover {
                background: transparent;
                border: transparent;
            }
        }

        .header {
            color: var(--nui-accordion-simple-text-headline);

            &.expanded {
                padding-bottom: var(--nui-accordion-simple-space-gap);
            }

            .left-side {
                gap: var(--nui-accordion-simple-space-gap);
            }

            .right-side {
                gap: var(--nui-accordion-simple-space-gap-actions);
            }
        }

        .content {
            color: var(--nui-accordion-simple-text-body);

            &.expanded {
                border-top: none;
            }
        }
    }

    /* ===============rich accordion=============== */

    &.rich {
        color: var(--nui-accordion-rich-text-headline);
        border: var(--nui-accordion-border, 1px) solid var(--nui-accordion-rich-border-default);
        border-radius: var(--nui-accordion-radius, 4px);
        background: var(--nui-accordion-rich-fill-default);
        gap: var(--nui-accordion-rich-space-gap);

        &.inset {
            padding: var(--nui-accordion-rich-space-padding-vertical)
            var(--nui-accordion-rich-space-padding-horizontal);
        }

        &:hover {
            border: var(--nui-accordion-border) solid var(--nui-accordion-rich-border-hover);
            background: var(--nui-accordion-rich-fill-hover);
        }

        &:has(.header.expanded) {
            border: var(--nui-accordion-border) solid var(--nui-accordion-rich-border-open);
            background: var(--nui-accordion-rich-fill-selected);

            &:hover {
                border: var(--nui-accordion-border) solid var(--nui-accordion-rich-border-hover);
                background: var(--nui-accordion-rich-fill-hover);
            }
        }

        .header {
            color: var(--nui-accordion-rich-text-headline);

            &.expanded {
                padding-bottom: var(--nui-accordion-rich-space-padding-horizontal);
            }

            .left-side {
                gap: var(--nui-accordion-rich-space-gap-left);
            }

            .right-side {
                gap: var(--nui-accordion-rich-space-gap-actions);
            }

            .sub-headline {
                color: var(--nui-accordion-rich-text-subheadline);
                font-size: var(--nui-label-regular-font-size);
                font-weight: var(--nui-label-regular-font-weight);
                line-height: var(--nui-label-regular-line-height);
                letter-spacing: var(--nui-label-regular-letter-spacing);
            }
        }

        .content {
            color: var(--nui-accordion-rich-text-body);

            &.expanded {
                border-top: 1px solid var(--nui-accordion-rich-fill-divider);
                padding-top: var(--nui-accordion-rich-space-padding-top-body);
            }
        }
    }
}
