import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIButtonComponent } from '../../buttons';
import { UIIconComponent, UISVGIconComponent, UIFlagComponent } from '../../icon';
import { UIAccordionItemComponent } from './accordion-item';
import { UIAccordionComponent } from './accordion.component';
import { UIAccordionHeaderTemplateDirective } from './templates';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UIAccordionComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Collapse/Accordion/Rich',
    component: UIAccordionComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIAccordionComponent,
                UIAccordionItemComponent,
                UIIconComponent,
                UISVGIconComponent,
                UIFlagComponent,
                UIButtonComponent,
                UIAccordionHeaderTemplateDirective,
                CommonModule,
                FormsModule,
                BrowserModule,
                UIComponentSizeDirective
            ]
        })
    ]
};
export default meta;

type CustomTemplate = {
    allowMultipleExpanded: boolean;
    accordions: {
        id: number;
        size: string;
    }[];
    header: string;
    subHeadline?: string;
    content: string;
    size: string;
    expanded: boolean;
    pills?: { text: string; size: string }[];
    checkbox?: boolean;
    checked?: boolean;
    nuiActionIcon?: string;
};

type Story = StoryObj<UIAccordionComponent | CustomTemplate>;

const renderFn: Story['render'] = args => ({
    props: args,
    template: `
        <ui-accordion [allowMultipleExpanded]="allowMultipleExpanded">
            @for (elem of accordions; track elem.id) {
                <h2 [uiSize]="'md'">{{elem.size}}</h2>

                <ui-accordion-item
                    [header]="header"
                    [subHeadline]="subHeadline"
                    [expanded]="expanded"
                    [size]="elem.size"
                    [pills]="pills"
                    [checkbox]="checkbox"
                    [checked]="checked"
                    [simple]="false"
                    [nuiActionIcon]="nuiActionIcon">
                    {{ content }}
                </ui-accordion-item>
            }
        </ui-accordion>
    `
});

export const NUIRichAccordion: Story = {
    args: {
        accordions: [
            {
                id: 1,
                size: 'md'
            }
        ],
        allowMultipleExpanded: false,
        header: 'Accordion',
        subHeadline: 'Sub-headline',
        content: 'Body',
        size: 'md',
        expanded: false,
        pills: [{ text: 'Pill', size: 'md' }],
        checkbox: true,
        checked: false,
        nuiActionIcon: 'open_in_new'
    },
    render: renderFn
};
