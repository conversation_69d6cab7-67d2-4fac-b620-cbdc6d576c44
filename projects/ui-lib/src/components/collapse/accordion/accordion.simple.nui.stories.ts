import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIButtonComponent } from '../../buttons';
import { UISVGIconComponent, UIFlagComponent } from '../../icon';
import { UIAccordionItemComponent } from './accordion-item';
import { UIAccordionComponent } from './accordion.component';
import { UIAccordionHeaderTemplateDirective } from './templates';
import { NUI_STORY_SETTINGS } from '../../../storybook/constants';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta<UIAccordionComponent> = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Collapse/Accordion/Simple',
    component: UIAccordionComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIAccordionComponent,
                UIAccordionItemComponent,
                UISVGIconComponent,
                UIFlagComponent,
                UIButtonComponent,
                UIAccordionHeaderTemplateDirective,
                CommonModule,
                FormsModule,
                BrowserModule,
                UIComponentSizeDirective
            ]
        })
    ]
};
export default meta;

type CustomTemplate = {
    allowMultipleExpanded: boolean;
    accordions: {
        id: number;
        size: string;
        inset: boolean;
    }[];
    header: string;
    content: string;
    size: string;
    expanded: boolean;
    nuiSvgIcon?: string;
    nuiActionIcon?: string;
};
type Story = StoryObj<UIAccordionComponent | CustomTemplate>;

const renderFn: Story['render'] = args => ({
    props: args,
    template: `
        <ui-accordion [allowMultipleExpanded]="allowMultipleExpanded">
            @for (elem of accordions; track elem.id) {
                <h5 [uiSize]="'lg'">{{elem.size}}</h5>

                <ui-accordion-item
                    [header]="header"
                    [expanded]="expanded"
                    [size]="elem.size"
                    [simple]="true"
                    [nuiSvgIcon]="nuiSvgIcon"
                    [nuiActionIcon]="nuiActionIcon"
                    [inset]="elem.inset">
                    {{ content }}
                </ui-accordion-item>
            }
        </ui-accordion>
    `
});

export const NUISimpleAccordion: Story = {
    args: {
        accordions: [
            {
                id: 1,
                size: 'md',
                inset: true
            },
            {
                id: 2,
                size: 'sm',
                inset: false
            },
            {
                id: 3,
                size: 'xs',
                inset: true
            }
        ],
        allowMultipleExpanded: false,
        header: 'Accordion',
        content: 'Body',
        size: 'md',
        expanded: false,
        nuiSvgIcon: '',
        nuiActionIcon: ''
    },
    render: renderFn
};
