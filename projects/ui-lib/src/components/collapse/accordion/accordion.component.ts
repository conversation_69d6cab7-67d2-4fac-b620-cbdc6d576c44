import {
    AfterContentInit,
    ChangeDetectionStrategy,
    Component,
    Input,
    OnDestroy,
    contentChildren,
    input
} from '@angular/core';
import { Subscription } from 'rxjs';
import { DATASET_SIZE } from '../../../services/uinew-theme.service';
import { UIAccordionItemComponent } from './accordion-item/accordion-item.component';

@Component({
    imports: [],
    selector: 'ui-accordion',
    templateUrl: 'accordion.component.html',
    styleUrls: ['accordion.component.scss', 'accordion.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.panel]': 'true',
        [`[attr.data-${DATASET_SIZE}]`]: 'size()'
    }
})
export class UIAccordionComponent implements AfterContentInit, OnDestroy {
    /**
     * When false only one accordion is expanded per time, if true multiple accordions can be expanded.
     *
     */
    @Input() allowMultipleExpanded = true;

    /**
     * Size
     */
    size = input<'xs' | 'sm' | 'md'>('md');

    readonly accordionItems = contentChildren(UIAccordionItemComponent);
    subscriptions: Subscription[] = [];

    ngAfterContentInit(): void {
        this.subscribe();
    }

    subscribe(): void {
        const accordionItems = this.accordionItems();
        if (accordionItems) {
            accordionItems.forEach(item => {
                this.subscriptions.push(
                    item.toggleChange.subscribe((expanded: boolean) => {
                        // If you only want one expanded at a time, set all other accordions to expanded false.
                        if (!this.allowMultipleExpanded && expanded) {
                            this.accordionItems().forEach(accordion => {
                                if (accordion !== item) {
                                    accordion.expanded = false;
                                }
                            });
                        }
                    })
                );
            });
        }
    }

    ngOnDestroy(): void {
        for (const subscription of this.subscriptions) {
            subscription.unsubscribe();
        }
    }
}
