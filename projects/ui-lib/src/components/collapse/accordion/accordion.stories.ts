import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIButtonComponent } from '../../buttons';
import { UIIconComponent, UISVGIconComponent, UIFlagComponent } from '../../icon';
import { UIAccordionItemComponent } from './accordion-item';
import { UIAccordionComponent } from './accordion.component';
import { UIAccordionHeaderTemplateDirective } from './templates';

const meta: Meta<UIAccordionComponent> = {
    title: 'Components/Collapse/Accordion',
    component: UIAccordionComponent,
    decorators: [
        moduleMetadata({
            imports: [
                UIAccordionComponent,
                UIAccordionItemComponent,
                UIIconComponent,
                UISVGIconComponent,
                UIFlagComponent,
                UIIconComponent,
                UIButtonComponent,
                UIAccordionHeaderTemplateDirective,
                CommonModule,
                FormsModule,
                BrowserModule
            ]
        })
    ]
};
export default meta;

type CustomTemplate = {
    allowMultipleExpanded: boolean;
    arrayOfAccordions: any[];
    icon: string;
    expanded: boolean;
    actionIcon: string;
    culture: string;
    customIcon: string;
    header: string;
    theme: string;
    isLeftSideCaret: boolean;
    isContentAnimated: boolean;
    text: string;
    isCustom: boolean;
    size: string;
    pills?: { text: string; size: string }[];
    subHeadline?: string;
    checkbox?: boolean;
    checked?: boolean;
    simple?: boolean;
};
type Story = StoryObj<UIAccordionComponent | CustomTemplate>;

const arrayOfAccordions = [
    {
        id: 1,
        title: 'Accordion1',
        content: 'html1'
    },
    {
        id: 2,
        title: 'Accordion2',
        content: 'html2'
    },
    {
        id: 3,
        title: 'Accordion3',
        content: 'html3'
    }
];

const renderFn: Story['render'] = args => ({
    props: args,
    template: `
        <ui-accordion [allowMultipleExpanded]="allowMultipleExpanded" [attr.ui-theme]="theme">
            <ui-accordion-item
                *ngFor="let elem of arrayOfAccordions"
                [icon]="icon"
                header="{{elem.title}}"
                [expanded]="expanded"
                [leftSideCaret]="isLeftSideCaret"
                [isContentAnimated]="isContentAnimated"
                [actionIcon]="actionIcon"
                [pills]="pills"
                [subHeadline]="subHeadline"
                [checkbox]="checkbox"
                [checked]="checked"
                [size]="size"
                [leftSideCaret]="isLeftSideCaret"
                [simple]="simple"
                >
                <div *ngIf='isCustom'>
                <ng-template ui-accordion-header-template>
                    <div class="custom" [ngClass]="{'left-side-caret-content-example': isLeftSideCaret}">
                        <ui-flag [culture]="culture" [size]="size"></ui-flag> &nbsp;
                        <span class="text">{{header}}</span>
                        <ui-icon [icon]="customIcon"></ui-icon>
                        <span class="text">{{text}}</span>
                    </div>
                </ng-template>
                </div>
                {{ elem.content }}
            </ui-accordion-item>
        </ui-accordion>
    `
});

export const Accordion: Story = {
    render: renderFn,
    args: {
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        icon: 'schedule',
        expanded: false
    }
};

export const WithActionIcon: Story = {
    render: renderFn,
    args: {
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        icon: 'schedule',
        actionIcon: 'delete',
        expanded: false
    }
};

export const WithCustomHeaderContent: Story = {
    render: renderFn,
    args: {
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        expanded: false,
        culture: 'se',
        customIcon: 'loop',
        header: 'Custom header',
        text: 'template',
        isCustom: true,
        size: 'tiny'
    }
};

export const AccordionWithSmallTheme: Story = {
    render: renderFn,
    args: {
        theme: 'small',
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        icon: 'schedule',
        actionIcon: 'delete',
        expanded: false,
        text: 'please click me'
    }
};

export const AccordionWithMinimalTheme: Story = {
    render: renderFn,
    args: {
        theme: 'minimal',
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        icon: 'schedule',
        actionIcon: 'delete',
        expanded: false,
        text: 'please click me'
    }
};

export const AccordionWithTinyThemeWithCustomHeaderContent: Story = {
    render: renderFn,
    args: {
        theme: 'tiny',
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        expanded: false,
        culture: 'se',
        customIcon: 'loop',
        header: 'Custom header',
        text: 'template',
        isCustom: true,
        size: 'tiny'
    }
};

export const AdditionalExampleLeftSideCaret: Story = {
    render: renderFn,
    args: {
        allowMultipleExpanded: false,
        arrayOfAccordions: arrayOfAccordions,
        isLeftSideCaret: true,
        isContentAnimated: false,
        expanded: false,
        culture: 'en-gb',
        customIcon: 'loop',
        header: 'Custom header',
        text: 'template',
        isCustom: true,
        size: 'tiny'
    }
};
