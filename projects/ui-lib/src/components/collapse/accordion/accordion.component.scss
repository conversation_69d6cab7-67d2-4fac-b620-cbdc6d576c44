:where(:root:not([data-uinew])) :host ::ng-deep ui-accordion-item {
    & > .container {
        border-right: 1px solid var(--ui-color-border);
        border-left: 1px solid var(--ui-color-border);
        border-top: 1px solid var(--ui-color-border);
    }

    &:first-child > .container {
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
    }

    &:last-child > .container {
        border-bottom: 1px solid var(--ui-color-border);
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 2px;
    }
}
