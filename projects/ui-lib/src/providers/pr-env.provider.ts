import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { EnvironmentProviders, Provider } from '@angular/core';
import { uiPREnvInterceptor } from '../interceptors';
import { UIPREnvService } from '../services/pr-env.service';
import { PREnvToken, PREnvTokenConfig } from '../types';

export function providePREnvPicker(
    reposConfigs: PREnvTokenConfig
): (Provider | EnvironmentProviders)[] {
    return [
        provideHttpClient(withInterceptors([uiPREnvInterceptor])),
        {
            provide: PREnvToken,
            useValue: reposConfigs
        },
        UIPREnvService
    ];
}
