:where(:root[data-uinew]) {
    @mixin text-base() {
        margin: 0;
        padding: 0;
        font-style: normal;
        color: var(--nui-text-primary);
        font-feature-settings:
            'clig' off,
            'liga' off;
    }

    // Heading Classes
    .nui-heading {
        @include text-base;

        font-family: var(--nui-heading-font-family);
        font-size: var(--nui-heading-font-size);
        line-height: var(--nui-heading-line-height);
        letter-spacing: var(--nui-heading-letter-spacing);
        font-weight: var(--nui-heading-font-weight);
    }

    // Title Classes
    .nui-title {
        @include text-base;

        font-family: var(--nui-title-font-family);
        font-size: var(--nui-title-font-size);
        line-height: var(--nui-title-line-height);
        letter-spacing: var(--nui-title-letter-spacing);
        font-weight: var(--nui-title-font-weight);
    }

    // Body Text Classes
    .nui-body {
        @include text-base;

        font-family: var(--nui-body-regular-font-family);
        font-size: var(--nui-body-regular-font-size);
        line-height: var(--nui-body-regular-line-height);
        letter-spacing: var(--nui-body-regular-letter-spacing);
        font-weight: var(--nui-body-regular-font-weight);
    }

    .nui-bold { font-weight: var(--nui-body-bold-font-weight)}

    // Semantic HTML
    h1, h2, h3, h4 { @extend .nui-heading;}
    h5,h6 { @extend .nui-title;}
    p, small { @extend .nui-body;}
    strong, b { @extend .nui-body; @extend .nui-bold}

}
