@mixin scrollbar($color: rgba(0, 0, 0, 0.25)) {
    &::-webkit-scrollbar {
        height: 12px;
        width: 5px;
        margin-right: 5px;
        background: transparent;

        &:horizontal {
            height: 5px;
            width: 12px;
        }
    }

    &::-webkit-scrollbar-thumb {
        background: $color;
        border-radius: 1ex;
    }

    &::-webkit-scrollbar-corner {
        background: transparent;
    }

    @document url-prefix() {
        scrollbar-width: thin;
    }

    [data-uinew] & {
        &::-webkit-scrollbar {
            width: calc(var(--nui-scroll-width, 8px) + var(--nui-scroll-space-padding-horizontal) * 2);
            height: calc(var(--nui-scroll-width, 8px) + var(--nui-scroll-space-padding-vertical) * 2);
        }

        &::-webkit-scrollbar-track {
            border-radius: var(--nui-scroll-radius, 112px);
            background: var(--nui-scroll-fill-track-primary-enabled);
        }

        &::-webkit-scrollbar-track:hover {
            background: var(--nui-scroll-fill-track-primary-hover);
        }

        &::-webkit-scrollbar-track:active {
            background: var(--nui-scroll-fill-track-primary-pressed);
        }

        &::-webkit-scrollbar-thumb {
            background: var(--nui-scroll-fill-thumb-primary-enabled);
            border: var(--nui-scroll-space-padding-horizontal) solid transparent;
            background-clip: content-box;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: var(--nui-scroll-fill-thumb-primary-hover);
            background-clip: content-box;
        }

        &::-webkit-scrollbar-thumb:active {
            background: var(--nui-scroll-fill-thumb-primary-pressed);
            background-clip: content-box;
        }
    }
}
