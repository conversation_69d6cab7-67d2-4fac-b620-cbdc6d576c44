:where(:root[data-uinew]) {
	--core-font-family-primary: Roboto Flex;
	--core-font-numeric-0010: 0.4000000059604645px;
	--core-font-numeric-0012: 0.47999998927116394px;
	--core-font-numeric-0035: 0.14000000059604645px;
	--core-font-numeric-0045: 0.18000000715255737px;
	--core-font-numeric-0050: 0.20000000298023224px;
	--core-font-numeric-0060: 0.23999999463558197px;
	--core-font-numeric-0070: 0.2800000011920929px;
	--core-font-numeric-1000: 40px;
	--core-font-numeric-10000: 400;
	--core-font-numeric-1200: 48px;
	--core-font-numeric-12500: 500;
	--core-font-numeric-15000: 600;
	--core-font-numeric-250: 10px;
	--core-font-numeric-300: 12px;
	--core-font-numeric-350: 14px;
	--core-font-numeric-400: 16px;
	--core-font-numeric-500: 20px;
	--core-font-numeric-525: 21px;
	--core-font-numeric-600: 24px;
	--core-font-numeric-700: 28px;
	--core-font-numeric-800: 32px;
	--core-numeric-0125: 0.5px;
	--core-numeric-025: 1px;
	--core-numeric-050: 2px;
	--core-numeric-100: 4px;
	--core-numeric-1000: 40px;
	--core-numeric-1200: 48px;
	--core-numeric-1600: 64px;
	--core-numeric-200: 8px;
	--core-numeric-2800: 112px;
	--core-numeric-300: 12px;
	--core-numeric-400: 16px;
	--core-numeric-500: 20px;
	--core-numeric-600: 24px;
	--core-numeric-700: 28px;
	--core-numeric-800: 32px;
	--nui-body-padding-card-layout: var(--core-numeric-300);
	--nui-body-padding-floating-layout: var(--core-numeric-200);
	--nui-body-padding-mixed-layout: var(--core-numeric-500);
	--nui-border-radius-full: var(--core-numeric-2800);
	--nui-border-radius-huge: var(--core-numeric-400);
	--nui-border-radius-large: var(--core-numeric-300);
	--nui-border-radius-medium: var(--core-numeric-200);
	--nui-border-radius-small: var(--core-numeric-100);
	--nui-border-radius-tiny: var(--core-numeric-050);
	--nui-border-width-medium: var(--core-numeric-050);
	--nui-border-width-small: var(--core-numeric-025);
	--nui-border-width-tiny: var(--core-numeric-0125);
	--nui-font-letter-spacing-014: var(--core-font-numeric-0035);
	--nui-font-letter-spacing-018: var(--core-font-numeric-0045);
	--nui-font-letter-spacing-020: var(--core-font-numeric-0050);
	--nui-font-letter-spacing-024: var(--core-font-numeric-0060);
	--nui-font-letter-spacing-028: var(--core-font-numeric-0070);
	--nui-font-letter-spacing-040: var(--core-font-numeric-0010);
	--nui-font-letter-spacing-048: var(--core-font-numeric-0012);
	--nui-font-line-height-1000: var(--core-font-numeric-1000);
	--nui-font-line-height-1200: var(--core-font-numeric-1200);
	--nui-font-line-height-275: var(--core-font-numeric-300);
	--nui-font-line-height-350: var(--core-font-numeric-350);
	--nui-font-line-height-400: var(--core-font-numeric-400);
	--nui-font-line-height-500: var(--core-font-numeric-500);
	--nui-font-line-height-525: var(--core-font-numeric-525);
	--nui-font-line-height-600: var(--core-font-numeric-600);
	--nui-font-line-height-700: var(--core-font-numeric-700);
	--nui-font-line-height-800: var(--core-font-numeric-800);
	--nui-font-size-1000: var(--core-font-numeric-1000);
	--nui-font-size-250: var(--core-font-numeric-250);
	--nui-font-size-275: var(--core-font-numeric-300);
	--nui-font-size-350: var(--core-font-numeric-350);
	--nui-font-size-400: var(--core-font-numeric-400);
	--nui-font-size-500: var(--core-font-numeric-500);
	--nui-font-size-600: var(--core-font-numeric-600);
	--nui-font-size-800: var(--core-font-numeric-800);
	--nui-font-weight-10000: var(--core-font-numeric-10000);
	--nui-font-weight-12500: var(--core-font-numeric-12500);
	--nui-font-weight-15000: var(--core-font-numeric-15000);
	--nui-height-medium: var(--core-numeric-1000);
	--nui-height-small: var(--core-numeric-800);
	--nui-height-tiny: var(--core-numeric-600);
	--nui-min-width-large: 264px;
	--nui-min-width-medium: 164px;
	--nui-min-width-small: var(--core-numeric-2800);
	--nui-min-width-tiny: var(--core-numeric-1600);
	--nui-shadow-bold-blur: 20px;
	--nui-shadow-bold-spread: 0px;
	--nui-shadow-bold-x: 0px;
	--nui-shadow-bold-y: 5px;
	--nui-shadow-subtle-blur: 10px;
	--nui-shadow-subtle-spread: 0px;
	--nui-shadow-subtle-x: 0px;
	--nui-shadow-subtle-y: 3px;
	--nui-shadow-subtler-blur: 6px;
	--nui-shadow-subtler-spread: 0px;
	--nui-shadow-subtler-x: 0px;
	--nui-shadow-subtler-y: 2px;
	--nui-shadow-subtlest-blur: 8px;
	--nui-shadow-subtlest-spread: 0px;
	--nui-shadow-subtlest-x: 0px;
	--nui-shadow-subtlest-y: 4px;
	--nui-space-000: 0px;
	--nui-space-050: var(--core-numeric-050);
	--nui-space-100: var(--core-numeric-100);
	--nui-space-1000: var(--core-numeric-1000);
	--nui-space-1200: var(--core-numeric-1200);
	--nui-space-200: var(--core-numeric-200);
	--nui-space-300: var(--core-numeric-300);
	--nui-space-400: var(--core-numeric-400);
	--nui-space-500: var(--core-numeric-500);
	--nui-space-600: var(--core-numeric-600);
	--nui-space-700: var(--core-numeric-700);
	--nui-space-800: var(--core-numeric-800);


	[data-uitheme="light"], &[data-uitheme="light"],
	&:not([data-uitheme="dark"]) {
		--core-brand-metal-1: rgba(132, 150, 179, 100%);
		--core-brand-metal-2: rgba(132, 150, 179, 20%);
		--core-brand-metal-3: rgba(132, 150, 179, 10%);
		--core-brand-solid-1: rgba(252, 253, 255, 100%);
		--core-brand-solid-10: rgba(0, 94, 235, 100%);
		--core-brand-solid-11: rgba(0, 98, 240, 100%);
		--core-brand-solid-12: rgba(8, 47, 108, 100%);
		--core-brand-solid-2: rgba(245, 249, 255, 100%);
		--core-brand-solid-3: rgba(233, 243, 255, 100%);
		--core-brand-solid-4: rgba(217, 235, 255, 100%);
		--core-brand-solid-5: rgba(199, 225, 255, 100%);
		--core-brand-solid-6: rgba(178, 212, 255, 100%);
		--core-brand-solid-7: rgba(152, 194, 255, 100%);
		--core-brand-solid-8: rgba(113, 168, 255, 100%);
		--core-brand-solid-9: rgba(0, 108, 253, 100%);
		--core-bw-white: rgba(255, 255, 255, 100%);
		--core-categories-green-solid-1: rgba(251, 254, 252, 100%);
		--core-categories-green-solid-10: rgba(43, 154, 102, 100%);
		--core-categories-green-solid-11: rgba(33, 131, 88, 100%);
		--core-categories-green-solid-12: rgba(25, 59, 45, 100%);
		--core-categories-green-solid-2: rgba(244, 251, 246, 100%);
		--core-categories-green-solid-3: rgba(230, 246, 235, 100%);
		--core-categories-green-solid-4: rgba(214, 241, 223, 100%);
		--core-categories-green-solid-5: rgba(196, 232, 209, 100%);
		--core-categories-green-solid-6: rgba(173, 221, 192, 100%);
		--core-categories-green-solid-7: rgba(142, 206, 170, 100%);
		--core-categories-green-solid-8: rgba(91, 185, 139, 100%);
		--core-categories-green-solid-9: rgba(48, 164, 108, 100%);
		--core-categories-indigo-solid-1: rgba(253, 253, 254, 100%);
		--core-categories-indigo-solid-10: rgba(51, 88, 212, 100%);
		--core-categories-indigo-solid-11: rgba(58, 91, 199, 100%);
		--core-categories-indigo-solid-12: rgba(31, 45, 92, 100%);
		--core-categories-indigo-solid-2: rgba(247, 249, 255, 100%);
		--core-categories-indigo-solid-3: rgba(237, 242, 254, 100%);
		--core-categories-indigo-solid-4: rgba(225, 233, 255, 100%);
		--core-categories-indigo-solid-5: rgba(210, 222, 255, 100%);
		--core-categories-indigo-solid-6: rgba(193, 208, 255, 100%);
		--core-categories-indigo-solid-7: rgba(171, 189, 249, 100%);
		--core-categories-indigo-solid-8: rgba(141, 164, 239, 100%);
		--core-categories-indigo-solid-9: rgba(62, 99, 221, 100%);
		--core-categories-orange-solid-1: rgba(254, 252, 251, 100%);
		--core-categories-orange-solid-10: rgba(239, 95, 0, 100%);
		--core-categories-orange-solid-11: rgba(204, 78, 0, 100%);
		--core-categories-orange-solid-12: rgba(88, 45, 29, 100%);
		--core-categories-orange-solid-2: rgba(255, 247, 237, 100%);
		--core-categories-orange-solid-3: rgba(255, 239, 214, 100%);
		--core-categories-orange-solid-4: rgba(255, 223, 181, 100%);
		--core-categories-orange-solid-5: rgba(255, 209, 154, 100%);
		--core-categories-orange-solid-6: rgba(255, 193, 130, 100%);
		--core-categories-orange-solid-7: rgba(245, 174, 115, 100%);
		--core-categories-orange-solid-8: rgba(236, 148, 85, 100%);
		--core-categories-orange-solid-9: rgba(247, 107, 21, 100%);
		--core-categories-pink-solid-1: rgba(255, 252, 254, 100%);
		--core-categories-pink-solid-10: rgba(207, 56, 151, 100%);
		--core-categories-pink-solid-11: rgba(194, 41, 138, 100%);
		--core-categories-pink-solid-12: rgba(101, 18, 73, 100%);
		--core-categories-pink-solid-2: rgba(254, 247, 251, 100%);
		--core-categories-pink-solid-3: rgba(254, 233, 245, 100%);
		--core-categories-pink-solid-4: rgba(251, 220, 239, 100%);
		--core-categories-pink-solid-5: rgba(246, 206, 231, 100%);
		--core-categories-pink-solid-6: rgba(239, 191, 221, 100%);
		--core-categories-pink-solid-7: rgba(231, 172, 208, 100%);
		--core-categories-pink-solid-8: rgba(221, 147, 194, 100%);
		--core-categories-pink-solid-9: rgba(214, 64, 159, 100%);
		--core-categories-teal-solid-1: rgba(250, 254, 253, 100%);
		--core-categories-teal-solid-10: rgba(13, 155, 138, 100%);
		--core-categories-teal-solid-11: rgba(0, 133, 115, 100%);
		--core-categories-teal-solid-12: rgba(13, 61, 56, 100%);
		--core-categories-teal-solid-2: rgba(243, 251, 249, 100%);
		--core-categories-teal-solid-3: rgba(224, 248, 243, 100%);
		--core-categories-teal-solid-4: rgba(204, 243, 234, 100%);
		--core-categories-teal-solid-5: rgba(184, 234, 224, 100%);
		--core-categories-teal-solid-6: rgba(161, 222, 210, 100%);
		--core-categories-teal-solid-7: rgba(131, 205, 193, 100%);
		--core-categories-teal-solid-8: rgba(83, 185, 171, 100%);
		--core-categories-teal-solid-9: rgba(18, 165, 148, 100%);
		--core-categories-tomato-solid-1: rgba(255, 252, 252, 100%);
		--core-categories-tomato-solid-10: rgba(221, 68, 37, 100%);
		--core-categories-tomato-solid-11: rgba(209, 52, 21, 100%);
		--core-categories-tomato-solid-12: rgba(92, 39, 31, 100%);
		--core-categories-tomato-solid-2: rgba(255, 248, 247, 100%);
		--core-categories-tomato-solid-3: rgba(254, 235, 231, 100%);
		--core-categories-tomato-solid-4: rgba(255, 220, 211, 100%);
		--core-categories-tomato-solid-5: rgba(255, 205, 194, 100%);
		--core-categories-tomato-solid-6: rgba(253, 189, 175, 100%);
		--core-categories-tomato-solid-7: rgba(245, 168, 152, 100%);
		--core-categories-tomato-solid-8: rgba(236, 142, 123, 100%);
		--core-categories-tomato-solid-9: rgba(229, 77, 46, 100%);
		--core-neutral-alpha-1: rgba(0, 0, 85, 1%);
		--core-neutral-alpha-10: rgba(0, 7, 27, 50%);
		--core-neutral-alpha-11: rgba(0, 7, 20, 62%);
		--core-neutral-alpha-12: rgba(0, 5, 9, 89%);
		--core-neutral-alpha-2: rgba(0, 0, 85, 2%);
		--core-neutral-alpha-3: rgba(0, 0, 51, 6%);
		--core-neutral-alpha-4: rgba(0, 0, 45, 9%);
		--core-neutral-alpha-5: rgba(0, 9, 50, 12%);
		--core-neutral-alpha-6: rgba(0, 0, 47, 15%);
		--core-neutral-alpha-7: rgba(0, 6, 46, 20%);
		--core-neutral-alpha-8: rgba(0, 8, 48, 27%);
		--core-neutral-alpha-9: rgba(0, 5, 29, 45%);
		--core-neutral-solid-1: rgba(252, 252, 253, 100%);
		--core-neutral-solid-10: rgba(128, 130, 141, 100%);
		--core-neutral-solid-11: rgba(98, 99, 108, 100%);
		--core-neutral-solid-12: rgba(30, 31, 36, 100%);
		--core-neutral-solid-2: rgba(249, 249, 251, 100%);
		--core-neutral-solid-3: rgba(239, 240, 243, 100%);
		--core-neutral-solid-4: rgba(231, 232, 236, 100%);
		--core-neutral-solid-5: rgba(224, 225, 230, 100%);
		--core-neutral-solid-6: rgba(216, 217, 224, 100%);
		--core-neutral-solid-7: rgba(205, 206, 215, 100%);
		--core-neutral-solid-8: rgba(185, 187, 198, 100%);
		--core-neutral-solid-9: rgba(139, 141, 152, 100%);
		--core-system-error-solid-1: rgba(255, 252, 252, 100%);
		--core-system-error-solid-10: rgba(199, 43, 71, 100%);
		--core-system-error-solid-11: rgba(200, 46, 73, 100%);
		--core-system-error-solid-12: rgba(99, 25, 36, 100%);
		--core-system-error-solid-2: rgba(255, 247, 248, 100%);
		--core-system-error-solid-3: rgba(254, 235, 235, 100%);
		--core-system-error-solid-4: rgba(255, 221, 221, 100%);
		--core-system-error-solid-5: rgba(255, 207, 208, 100%);
		--core-system-error-solid-6: rgba(249, 192, 193, 100%);
		--core-system-error-solid-7: rgba(240, 172, 175, 100%);
		--core-system-error-solid-8: rgba(230, 147, 151, 100%);
		--core-system-error-solid-9: rgba(214, 61, 84, 100%);
		--core-system-info-solid-1: rgba(251, 253, 255, 100%);
		--core-system-info-solid-10: rgba(0, 131, 235, 100%);
		--core-system-info-solid-11: rgba(0, 115, 221, 100%);
		--core-system-info-solid-12: rgba(0, 52, 99, 100%);
		--core-system-info-solid-2: rgba(244, 250, 255, 100%);
		--core-system-info-solid-3: rgba(233, 243, 255, 100%);
		--core-system-info-solid-4: rgba(217, 237, 255, 100%);
		--core-system-info-solid-5: rgba(199, 227, 255, 100%);
		--core-system-info-solid-6: rgba(178, 214, 255, 100%);
		--core-system-info-solid-7: rgba(151, 197, 249, 100%);
		--core-system-info-solid-8: rgba(110, 173, 243, 100%);
		--core-system-info-solid-9: rgba(0, 143, 254, 100%);
		--core-system-success-solid-1: rgba(250, 254, 253, 100%);
		--core-system-success-solid-10: rgba(80, 180, 155, 100%);
		--core-system-success-solid-11: rgba(0, 124, 102, 100%);
		--core-system-success-solid-12: rgba(28, 61, 53, 100%);
		--core-system-success-solid-2: rgba(243, 251, 248, 100%);
		--core-system-success-solid-3: rgba(226, 247, 241, 100%);
		--core-system-success-solid-4: rgba(209, 242, 231, 100%);
		--core-system-success-solid-5: rgba(190, 234, 220, 100%);
		--core-system-success-solid-6: rgba(167, 222, 206, 100%);
		--core-system-success-solid-7: rgba(135, 206, 186, 100%);
		--core-system-success-solid-8: rgba(86, 186, 161, 100%);
		--core-system-success-solid-9: rgba(92, 191, 166, 100%);
		--core-system-warning-solid-1: rgba(254, 253, 251, 100%);
		--core-system-warning-solid-10: rgba(220, 156, 0, 100%);
		--core-system-warning-solid-11: rgba(163, 112, 0, 100%);
		--core-system-warning-solid-12: rgba(74, 56, 21, 100%);
		--core-system-warning-solid-2: rgba(255, 248, 236, 100%);
		--core-system-warning-solid-3: rgba(255, 240, 206, 100%);
		--core-system-warning-solid-4: rgba(255, 228, 166, 100%);
		--core-system-warning-solid-5: rgba(255, 216, 135, 100%);
		--core-system-warning-solid-6: rgba(248, 205, 127, 100%);
		--core-system-warning-solid-7: rgba(231, 188, 109, 100%);
		--core-system-warning-solid-8: rgba(215, 165, 66, 100%);
		--core-system-warning-solid-9: rgba(231, 167, 0, 100%);
		--nui-accordion-rich-border-default: var(--nui-border-neutral-subtle);
		--nui-accordion-rich-border-hover: var(--nui-border-neutral-secondary-bold);
		--nui-accordion-rich-border-open: var(--nui-border-neutral-secondary-bold);
		--nui-accordion-rich-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-accordion-rich-fill-divider: var(--nui-border-neutral-secondary-bold);
		--nui-accordion-rich-fill-hover: var(--nui-surface-neutral-subtlest);
		--nui-accordion-rich-fill-selected: var(--nui-surface-neutral-subtlest);
		--nui-accordion-rich-text-body: var(--nui-text-primary);
		--nui-accordion-rich-text-headline: var(--nui-text-primary);
		--nui-accordion-rich-text-subheadline: var(--nui-text-secondary);
		--nui-accordion-simple-fill-default: var(--nui-fill-transparent);
		--nui-accordion-simple-text-body: var(--nui-text-primary);
		--nui-accordion-simple-text-headline: var(--nui-text-primary);
		--nui-active: var(--nui-fill-brand-primary-boldest);
		--nui-banner-fill-error: var(--nui-fill-system-danger-subtler);
		--nui-banner-fill-info: var(--nui-fill-system-info-subtler);
		--nui-banner-fill-neutral: var(--nui-fill-brand-secondary-subtler);
		--nui-banner-fill-success: var(--nui-fill-system-success-subtler);
		--nui-banner-fill-warning: var(--nui-fill-system-warning-subtler);
		--nui-banner-icon-error: var(--nui-icon-error-boldest);
		--nui-banner-icon-info: var(--nui-icon-info-boldest);
		--nui-banner-icon-neutral: var(--nui-icon-primary);
		--nui-banner-icon-success: var(--nui-icon-success-boldest);
		--nui-banner-icon-warning: var(--nui-icon-warning-bold);
		--nui-banner-text-error: var(--nui-text-error-boldest);
		--nui-banner-text-info: var(--nui-text-info-boldest);
		--nui-banner-text-neutral: var(--nui-text-primary);
		--nui-banner-text-success: var(--nui-text-success-boldest);
		--nui-banner-text-warning: var(--nui-text-warning-boldest);
		--nui-border-brand-primary-boldest: var(--core-brand-solid-10);
		--nui-border-brand-primary-boldest-hover: var(--core-brand-solid-8);
		--nui-border-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-border-categories-green-bolder: var(--core-categories-green-solid-11);
		--nui-border-categories-green-subtle: var(--core-categories-green-solid-6);
		--nui-border-categories-green-subtler: var(--core-categories-green-solid-3);
		--nui-border-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-border-categories-indigo-bolder: var(--core-categories-indigo-solid-11);
		--nui-border-categories-indigo-subtle: var(--core-categories-indigo-solid-6);
		--nui-border-categories-indigo-subtler: var(--core-categories-indigo-solid-3);
		--nui-border-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-border-categories-orange-bolder: var(--core-categories-orange-solid-11);
		--nui-border-categories-orange-subtle: var(--core-categories-orange-solid-6);
		--nui-border-categories-orange-subtler: var(--core-categories-orange-solid-3);
		--nui-border-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-border-categories-pink-bolder: var(--core-categories-pink-solid-11);
		--nui-border-categories-pink-subtle: var(--core-categories-pink-solid-6);
		--nui-border-categories-pink-subtler: var(--core-categories-pink-solid-3);
		--nui-border-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-border-categories-teal-bolder: var(--core-categories-teal-solid-11);
		--nui-border-categories-teal-subtle: var(--core-categories-teal-solid-6);
		--nui-border-categories-teal-subtler: var(--core-categories-teal-solid-3);
		--nui-border-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-border-categories-tomato-bolder: var(--core-categories-tomato-solid-11);
		--nui-border-categories-tomato-subtle: var(--core-categories-tomato-solid-6);
		--nui-border-categories-tomato-subtler: var(--core-categories-tomato-solid-3);
		--nui-border-disabled: var(--core-neutral-solid-8);
		--nui-border-neutral-bolder: var(--core-neutral-solid-11);
		--nui-border-neutral-secondary-bold: var(--core-neutral-solid-7);
		--nui-border-neutral-secondary-boldest: var(--core-neutral-solid-10);
		--nui-border-neutral-subtle: var(--core-neutral-solid-3);
		--nui-border-system-danger-bold: var(--core-system-error-solid-8);
		--nui-border-system-danger-boldest: var(--core-system-error-solid-10);
		--nui-border-system-focus: var(--core-brand-solid-5);
		--nui-border-system-info-boldest: var(--core-system-info-solid-9);
		--nui-border-system-success-boldest: var(--core-system-success-solid-9);
		--nui-border-system-warning-boldest: var(--core-system-warning-solid-9);
		--nui-border-transparent: rgba(255, 255, 255, 0%);
		--nui-breadcrumb-fill-primary-default: var(--nui-fill-brand-secondary-subtler);
		--nui-breadcrumb-fill-primary-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-breadcrumb-fill-secondary-default: var(--nui-fill-transparent);
		--nui-breadcrumb-fill-secondary-hover: var(--nui-fill-transparent);
		--nui-breadcrumb-icon-chevron-primary-default: var(--nui-icon-brand);
		--nui-breadcrumb-icon-chevron-primary-hover: var(--nui-icon-brand);
		--nui-breadcrumb-icon-chevron-primary-last: var(--nui-icon-brand);
		--nui-breadcrumb-icon-chevron-secondary-default: var(--nui-icon-primary);
		--nui-breadcrumb-icon-chevron-secondary-hover: var(--nui-icon-primary);
		--nui-breadcrumb-icon-chevron-secondary-last: var(--nui-icon-primary);
		--nui-breadcrumb-icon-left-primary-default: var(--nui-icon-brand);
		--nui-breadcrumb-icon-left-primary-hover: var(--nui-icon-brand);
		--nui-breadcrumb-icon-left-primary-last: var(--nui-icon-brand);
		--nui-breadcrumb-icon-left-secondary-default: var(--nui-icon-secondary);
		--nui-breadcrumb-icon-left-secondary-hover: var(--nui-icon-primary);
		--nui-breadcrumb-icon-left-secondary-last: var(--nui-icon-primary);
		--nui-breadcrumb-text-primary-default: var(--nui-text-brand);
		--nui-breadcrumb-text-primary-hover: var(--nui-text-brand);
		--nui-breadcrumb-text-primary-last: var(--nui-text-brand);
		--nui-breadcrumb-text-secondary-default: var(--nui-text-secondary);
		--nui-breadcrumb-text-secondary-hover: var(--nui-text-primary);
		--nui-breadcrumb-text-secondary-last: var(--nui-text-primary);
		--nui-button-border-secondary-destructive: var(--nui-border-system-danger-boldest);
		--nui-button-fill-primary: var(--nui-fill-brand-primary-boldest);
		--nui-button-fill-primary-destructive: var(--nui-fill-system-danger-bold);
		--nui-button-fill-primary-destructive-hover: var(--nui-fill-neutral-subtlest);
		--nui-button-fill-primary-disabled: var(--nui-fill-brand-secondary-subtler);
		--nui-button-fill-primary-hover: var(--nui-fill-brand-primary-boldest-hover);
		--nui-button-fill-primary-pressed: var(--nui-fill-brand-primary-boldest);
		--nui-button-fill-secondary: var(--nui-fill-brand-secondary-subtler);
		--nui-button-fill-secondary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-button-fill-secondary-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-button-fill-secondary-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-button-fill-transparent: var(--nui-fill-transparent);
		--nui-button-group-border-default: var(--nui-border-neutral-subtle);
		--nui-button-group-fill-default: var(--nui-fill-brand-secondary-subtler);
		--nui-button-group-item-fill-disabled: var(--nui-fill-transparent);
		--nui-button-group-item-fill-enabled: var(--nui-fill-transparent);
		--nui-button-group-item-fill-selected: var(--nui-fill-neutral-subtlest);
		--nui-button-group-item-icon-disabled: var(--nui-icon-disabled);
		--nui-button-group-item-icon-enabled: var(--nui-icon-secondary);
		--nui-button-group-item-icon-selected: var(--nui-icon-primary);
		--nui-button-group-item-text-disabled: var(--nui-text-disabled);
		--nui-button-group-item-text-enabled: var(--nui-text-secondary);
		--nui-button-group-item-text-selected: var(--nui-text-primary);
		--nui-button-icon-brand: var(--nui-icon-brand);
		--nui-button-icon-disabled: var(--nui-icon-disabled);
		--nui-button-icon-primary: var(--nui-icon-primary-inverted);
		--nui-button-icon-primary-destructive: var(--nui-icon-primary-inverted);
		--nui-button-icon-primary-destructive-inverted: var(--nui-icon-error);
		--nui-button-icon-primary-inverted: var(--nui-icon-primary);
		--nui-button-icon-secondary: var(--nui-icon-secondary);
		--nui-button-text-brand: var(--nui-text-brand);
		--nui-button-text-disabled: var(--nui-text-disabled);
		--nui-button-text-primary: var(--nui-text-primary-inverted);
		--nui-button-text-primary-destructive: var(--nui-text-primary-inverted);
		--nui-button-text-primary-destructive-inverted: var(--nui-text-error);
		--nui-button-text-primary-inverted: var(--nui-text-primary);
		--nui-button-text-secondary: var(--nui-text-secondary);
		--nui-button-text-tetriary: var(--nui-text-primary);
		--nui-calendar-date-picker-item-fill-disabled: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-fill-enabled: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-fill-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-calendar-date-picker-item-fill-previous-next: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-fill-selected: var(--nui-fill-brand-primary-boldest);
		--nui-calendar-date-picker-item-fill-selected-span: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-date-picker-item-fill-today: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-text-disabled: var(--nui-text-disabled);
		--nui-calendar-date-picker-item-text-enabled: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-hover: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-previous-next: var(--nui-text-secondary);
		--nui-calendar-date-picker-item-text-selected: var(--nui-text-primary-inverted);
		--nui-calendar-date-picker-item-text-selected-span: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-today: var(--nui-text-brand);
		--nui-calendar-month-border-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-calendar-month-date-border-disabled: var(--nui-border-neutral-secondary-bold);
		--nui-calendar-month-date-border-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-calendar-month-date-border-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-calendar-month-date-border-selected: var(--nui-border-brand-primary-boldest);
		--nui-calendar-month-date-fill-disabled: var(--nui-surface-neutral-subtler);
		--nui-calendar-month-date-fill-enabled: var(--nui-surface-neutral-subtlest);
		--nui-calendar-month-date-fill-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-month-date-fill-selected: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-month-date-text-disabled: var(--nui-text-disabled);
		--nui-calendar-month-date-text-enabled: var(--nui-text-primary);
		--nui-calendar-month-date-text-hover: var(--nui-text-primary);
		--nui-calendar-month-date-text-selected: var(--nui-text-primary);
		--nui-calendar-month-event-fill-enabled: var(--nui-border-categories-teal-bold);
		--nui-calendar-month-event-icon-enabled: var(--nui-icon-primary-inverted);
		--nui-calendar-month-event-text-enabled: var(--nui-text-primary-inverted);
		--nui-calendar-month-fill-deafult: var(--nui-surface-neutral-subtlest);
		--nui-calendar-month-text-week-number: var(--nui-text-secondary);
		--nui-calendar-month-text-weekday: var(--nui-text-primary);
		--nui-calendar-schedule-item-border: var(--core-categories-teal-solid-9);
		--nui-calendar-schedule-item-fill-default: var(--nui-fill-neutral-subtlest);
		--nui-calendar-schedule-item-fill-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-schedule-item-icon-default: var(--nui-icon-secondary);
		--nui-calendar-schedule-item-text-date: var(--nui-text-secondary);
		--nui-calendar-schedule-item-text-name: var(--nui-text-primary);
		--nui-card-border-default: var(--nui-border-neutral-secondary-bold);
		--nui-card-border-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-card-border-selected: var(--nui-border-brand-primary-boldest);
		--nui-card-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-card-fill-hover: var(--nui-fill-brand-secondary-subtlest);
		--nui-card-fill-selected: var(--nui-fill-brand-primary-subtlest);
		--nui-chat-avatar-fill-hover: var(--nui-surface-neutral-subtler);
		--nui-chat-border-default: var(--nui-border-neutral-subtle);
		--nui-chat-emoji-border-default: var(--nui-border-neutral-subtle);
		--nui-chat-emoji-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-chat-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-chat-fill-highlight: var(--nui-fill-brand-primary-subtlest);
		--nui-chat-input-border-active-top: var(--nui-border-neutral-secondary-boldest);
		--nui-chat-thread-fill-closed-default: var(--nui-fill-brand-secondary-subtlest);
		--nui-chat-thread-fill-closed-highlight: var(--nui-fill-brand-primary-subtlest);
		--nui-chat-thread-fill-open-default: var(--nui-surface-neutral-subtlest);
		--nui-checkbox-item-checkbox-fill-checked: var(--nui-icon-primary);
		--nui-checkbox-item-checkbox-fill-disabled: var(--nui-icon-disabled);
		--nui-checkbox-item-checkbox-fill-hover: var(--nui-icon-brand);
		--nui-checkbox-item-checkbox-icon-checked: var(--nui-icon-primary-inverted);
		--nui-checkbox-item-checkbox-icon-disabled: var(--nui-icon-primary-inverted);
		--nui-checkbox-item-checkbox-icon-hover: var(--nui-icon-primary-inverted);
		--nui-checkbox-item-icon-default: var(--nui-icon-primary);
		--nui-checkbox-item-icon-disabled: var(--nui-icon-disabled);
		--nui-checkbox-item-icon-hover: var(--nui-icon-brand);
		--nui-chips-border-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-primary-default-hover: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-primary-selected: var(--nui-border-transparent);
		--nui-chips-border-primary-selected-hover: var(--nui-border-transparent);
		--nui-chips-border-secondary-default: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-secondary-default-hover: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-secondary-selected: var(--nui-border-transparent);
		--nui-chips-border-secondary-selected-hover: var(--nui-border-neutral-subtle);
		--nui-chips-fill-primary-default: var(--nui-fill-neutral-subtlest);
		--nui-chips-fill-primary-default-hover: var(--nui-fill-neutral-subtle);
		--nui-chips-fill-primary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-chips-fill-primary-selected-hover: var(--nui-fill-brand-primary-boldest-hover);
		--nui-chips-fill-secondary-default: var(--nui-fill-brand-secondary-subtler);
		--nui-chips-fill-secondary-default-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-chips-fill-secondary-selected: var(--nui-fill-neutral-boldest);
		--nui-chips-fill-secondary-selected-hover: var(--nui-fill-neutral-boldest);
		--nui-dialog-border-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-dialog-fill-primary-default: var(--nui-surface-neutral-subtlest);
		--nui-dialog-icon-primary-default: var(--nui-icon-primary);
		--nui-dialog-text-body: var(--nui-text-secondary);
		--nui-dialog-text-headline: var(--nui-text-primary);
		--nui-divider-fill-primary-default: var(--nui-border-neutral-subtle);
		--nui-done: var(--nui-fill-neutral-boldest);
		--nui-fill-brand-primary-boldest: var(--core-brand-solid-9);
		--nui-fill-brand-primary-boldest-hover: var(--core-brand-solid-10);
		--nui-fill-brand-primary-subtlest: var(--core-brand-solid-3);
		--nui-fill-brand-secondary-neutral: var(--core-neutral-solid-5);
		--nui-fill-brand-secondary-subtle: var(--core-neutral-solid-4);
		--nui-fill-brand-secondary-subtler: var(--core-neutral-solid-3);
		--nui-fill-brand-secondary-subtlest: var(--core-neutral-solid-2);
		--nui-fill-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-fill-categories-green-boldest: var(--core-categories-green-solid-11);
		--nui-fill-categories-green-subtle: var(--core-categories-green-solid-3);
		--nui-fill-categories-green-subtlest: var(--core-categories-green-solid-1);
		--nui-fill-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-fill-categories-indigo-boldest: var(--core-categories-indigo-solid-11);
		--nui-fill-categories-indigo-subtle: var(--core-categories-indigo-solid-3);
		--nui-fill-categories-indigo-subtlest: var(--core-categories-indigo-solid-1);
		--nui-fill-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-fill-categories-orange-boldest: var(--core-categories-orange-solid-11);
		--nui-fill-categories-orange-subtle: var(--core-categories-orange-solid-3);
		--nui-fill-categories-orange-subtlest: var(--core-categories-orange-solid-1);
		--nui-fill-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-fill-categories-pink-boldest: var(--core-categories-pink-solid-11);
		--nui-fill-categories-pink-subtle: var(--core-categories-pink-solid-3);
		--nui-fill-categories-pink-subtlest: var(--core-categories-pink-solid-1);
		--nui-fill-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-fill-categories-teal-boldest: var(--core-categories-teal-solid-11);
		--nui-fill-categories-teal-subtle: var(--core-categories-teal-solid-3);
		--nui-fill-categories-teal-subtlest: var(--core-categories-teal-solid-1);
		--nui-fill-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-fill-categories-tomato-boldest: var(--core-categories-tomato-solid-11);
		--nui-fill-categories-tomato-subtle: var(--core-categories-tomato-solid-3);
		--nui-fill-categories-tomato-subtlest: var(--core-categories-tomato-solid-1);
		--nui-fill-disabled: var(--core-neutral-solid-5);
		--nui-fill-neutral-bold: var(--core-neutral-solid-8);
		--nui-fill-neutral-boldest: var(--core-neutral-solid-12);
		--nui-fill-neutral-subtle: var(--core-neutral-solid-3);
		--nui-fill-neutral-subtlest: var(--core-bw-white);
		--nui-fill-system-danger-bold: var(--core-system-error-solid-9);
		--nui-fill-system-danger-subtle: var(--core-system-error-solid-6);
		--nui-fill-system-danger-subtler: var(--core-system-error-solid-4);
		--nui-fill-system-info-bold: var(--core-system-info-solid-10);
		--nui-fill-system-info-subtler: var(--core-system-info-solid-4);
		--nui-fill-system-recording: var(--core-system-error-solid-9);
		--nui-fill-system-success-bold: var(--core-system-success-solid-9);
		--nui-fill-system-success-subtler: var(--core-system-success-solid-4);
		--nui-fill-system-warning-bold: var(--core-system-warning-solid-9);
		--nui-fill-system-warning-subtler: var(--core-system-warning-solid-4);
		--nui-fill-transparent: rgba(255, 255, 255, 0%);
		--nui-forms-border-primary-disabled: var(--nui-border-disabled);
		--nui-forms-border-primary-enabled: var(--nui-border-neutral-bolder);
		--nui-forms-border-primary-error: var(--nui-border-system-danger-boldest);
		--nui-forms-border-primary-filled: var(--nui-border-neutral-bolder);
		--nui-forms-border-primary-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-forms-border-primary-selected: var(--nui-border-brand-primary-boldest);
		--nui-forms-border-secondary-disabled: var(--nui-border-disabled);
		--nui-forms-border-secondary-enabled: var(--nui-border-transparent);
		--nui-forms-border-secondary-error: var(--nui-border-system-danger-boldest);
		--nui-forms-border-secondary-filled: var(--nui-border-transparent);
		--nui-forms-border-secondary-hover: var(--nui-border-neutral-secondary-boldest);
		--nui-forms-border-secondary-selected: var(--nui-border-brand-primary-boldest);
		--nui-forms-fill-primary-disabled: var(--nui-surface-neutral-subtlest);
		--nui-forms-fill-primary-enabled: var(--nui-surface-neutral-subtlest);
		--nui-forms-fill-primary-error: var(--nui-surface-neutral-subtlest);
		--nui-forms-fill-primary-filled: var(--nui-surface-neutral-subtlest);
		--nui-forms-fill-primary-hover: var(--nui-surface-neutral-subtlest);
		--nui-forms-fill-primary-selected: var(--nui-surface-neutral-subtlest);
		--nui-forms-fill-secondary-disabled: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-enabled: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-error: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-filled: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-selected: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-icon-primary-disabled: var(--nui-icon-disabled);
		--nui-forms-icon-primary-enabled: var(--nui-icon-secondary);
		--nui-forms-icon-primary-error: var(--nui-icon-primary);
		--nui-forms-icon-primary-filled: var(--nui-icon-primary);
		--nui-forms-icon-primary-hover: var(--nui-icon-secondary);
		--nui-forms-icon-primary-selected: var(--nui-icon-primary);
		--nui-forms-icon-secondary-disabled: var(--nui-icon-disabled);
		--nui-forms-icon-secondary-enabled: var(--nui-icon-secondary);
		--nui-forms-icon-secondary-error: var(--nui-icon-primary);
		--nui-forms-icon-secondary-filled: var(--nui-icon-primary);
		--nui-forms-icon-secondary-hover: var(--nui-icon-secondary);
		--nui-forms-icon-secondary-selected: var(--nui-icon-primary);
		--nui-forms-text-help-primary-disabled: var(--nui-text-disabled);
		--nui-forms-text-help-primary-enabled: var(--nui-text-secondary);
		--nui-forms-text-help-primary-error: var(--nui-text-error);
		--nui-forms-text-help-primary-filled: var(--nui-text-secondary);
		--nui-forms-text-help-primary-hover: var(--nui-text-secondary);
		--nui-forms-text-help-primary-selected: var(--nui-text-secondary);
		--nui-forms-text-input-primary-disabled: var(--nui-text-disabled);
		--nui-forms-text-input-primary-enabled: var(--nui-text-secondary);
		--nui-forms-text-input-primary-error: var(--nui-text-primary);
		--nui-forms-text-input-primary-filled: var(--nui-text-primary);
		--nui-forms-text-input-primary-hover: var(--nui-text-secondary);
		--nui-forms-text-input-primary-selected: var(--nui-text-primary);
		--nui-forms-text-input-secondary-disabled: var(--nui-text-disabled);
		--nui-forms-text-input-secondary-enabled: var(--nui-text-secondary);
		--nui-forms-text-input-secondary-error: var(--nui-text-primary);
		--nui-forms-text-input-secondary-filled: var(--nui-text-primary);
		--nui-forms-text-input-secondary-hover: var(--nui-text-secondary);
		--nui-forms-text-input-secondary-selected: var(--nui-text-primary);
		--nui-forms-text-label-primary-disabled: var(--nui-text-disabled);
		--nui-forms-text-label-primary-enabled: var(--nui-text-primary);
		--nui-forms-text-label-primary-error: var(--nui-text-error);
		--nui-forms-text-label-primary-filled: var(--nui-text-primary);
		--nui-forms-text-label-primary-hover: var(--nui-text-primary);
		--nui-forms-text-label-primary-selected: var(--nui-text-primary);
		--nui-forms-text-suffix-secondary-disabled: var(--nui-text-disabled);
		--nui-forms-text-suffix-secondary-enabled: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-error: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-filled: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-hover: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-selected: var(--nui-text-secondary);
		--nui-grid-item-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-grid-item-fill-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-grid-item-header-icon-default: var(--nui-icon-primary);
		--nui-grid-item-header-icon-hover: var(--nui-icon-primary);
		--nui-grid-item-header-text-default: var(--nui-text-primary);
		--nui-grid-item-header-text-hover: var(--nui-text-primary);
		--nui-header-border-solid-default: var(--nui-border-neutral-secondary-bold);
		--nui-header-border-transparent-default: var(--nui-border-transparent);
		--nui-header-fill-solid-default: var(--nui-surface-neutral-subtlest);
		--nui-header-fill-transparent-default: var(--nui-surface-transparent);
		--nui-headline: var(--nui-text-primary);
		--nui-icon-brand: var(--core-brand-solid-9);
		--nui-icon-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-icon-categories-green-bolder: var(--core-categories-green-solid-12);
		--nui-icon-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-icon-categories-indigo-boldest: var(--core-categories-indigo-solid-12);
		--nui-icon-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-icon-categories-orange-bolder: var(--core-categories-orange-solid-12);
		--nui-icon-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-icon-categories-pink-boldest: var(--core-categories-pink-solid-12);
		--nui-icon-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-icon-categories-teal-boldest: var(--core-categories-teal-solid-12);
		--nui-icon-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-icon-categories-tomato-boldest: var(--core-categories-tomato-solid-12);
		--nui-icon-disabled: var(--core-neutral-alpha-8);
		--nui-icon-error: var(--core-system-error-solid-10);
		--nui-icon-error-boldest: var(--core-system-error-solid-12);
		--nui-icon-info: var(--core-system-info-solid-9);
		--nui-icon-info-boldest: var(--core-system-info-solid-12);
		--nui-icon-primary: var(--core-neutral-solid-12);
		--nui-icon-primary-inverted: var(--core-neutral-solid-1);
		--nui-icon-secondary: var(--core-neutral-solid-11);
		--nui-icon-status-approved: var(--core-system-success-solid-10);
		--nui-icon-status-for-review: var(--core-system-info-solid-9);
		--nui-icon-status-in-progress: var(--core-system-warning-solid-9);
		--nui-icon-status-no-status: var(--core-neutral-solid-9);
		--nui-icon-status-not-approved: var(--core-system-error-solid-10);
		--nui-icon-success: var(--core-system-success-solid-9);
		--nui-icon-success-boldest: var(--core-system-success-solid-12);
		--nui-icon-warning: var(--core-system-warning-solid-10);
		--nui-icon-warning-bold: var(--core-system-warning-solid-12);
		--nui-label-icon-brand: var(--nui-icon-brand);
		--nui-label-icon-destructive: var(--nui-icon-error);
		--nui-label-icon-disabled: var(--nui-icon-disabled);
		--nui-label-icon-inverted: var(--nui-icon-primary-inverted);
		--nui-label-icon-primary: var(--nui-icon-primary);
		--nui-label-icon-secondary: var(--nui-icon-secondary);
		--nui-label-text-brand: var(--nui-text-brand);
		--nui-label-text-destructive: var(--nui-text-error);
		--nui-label-text-disabled: var(--nui-text-disabled);
		--nui-label-text-inverted: var(--nui-text-primary-inverted);
		--nui-label-text-neutral: var(--nui-text-primary);
		--nui-label-text-secondary: var(--nui-text-secondary);
		--nui-list-item-rich-border-default: var(--nui-border-neutral-subtle);
		--nui-list-item-rich-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-list-item-rich-fill-divider: var(--nui-border-neutral-secondary-bold);
		--nui-list-item-rich-fill-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-list-item-rich-fill-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-list-item-rich-fill-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-list-item-rich-text-headline: var(--nui-text-primary);
		--nui-list-item-simple-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-list-item-simple-fill-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-list-item-simple-fill-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-list-item-simple-fill-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-list-item-simple-icon-left-default: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-default-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-hover: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-hover-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-pressed: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-pressed-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-selected: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-selected-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-right-default: var(--nui-icon-secondary);
		--nui-list-item-simple-icon-right-hover: var(--nui-icon-primary);
		--nui-list-item-simple-icon-right-pressed: var(--nui-icon-primary);
		--nui-list-item-simple-icon-right-selected: var(--nui-icon-primary);
		--nui-list-item-simple-text-default: var(--nui-text-primary);
		--nui-list-item-simple-text-hover: var(--nui-text-primary);
		--nui-list-item-simple-text-pressed: var(--nui-text-primary);
		--nui-list-item-simple-text-selected: var(--nui-text-primary);
		--nui-loader-fill-inverted-light: var(--nui-fill-brand-secondary-neutral);
		--nui-loader-fill-inverted-strong: var(--nui-fill-neutral-subtlest);
		--nui-loader-fill-primary-light: var(--nui-fill-brand-secondary-neutral);
		--nui-loader-fill-primary-strong: var(--nui-fill-brand-primary-boldest);
		--nui-main-navigation-fill-default: var(--nui-surface-transparent);
		--nui-main-navigation-item-fill-active: var(--nui-fill-neutral-subtlest);
		--nui-main-navigation-item-fill-default: var(--nui-fill-transparent);
		--nui-main-navigation-item-fill-hover: var(--nui-fill-neutral-subtlest);
		--nui-main-navigation-item-icon-active: var(--nui-icon-brand);
		--nui-main-navigation-item-icon-default: var(--nui-icon-primary);
		--nui-main-navigation-item-icon-hover: var(--nui-icon-primary);
		--nui-main-navigation-item-text-active: var(--nui-text-brand);
		--nui-main-navigation-item-text-default: var(--nui-text-primary);
		--nui-main-navigation-item-text-hover: var(--nui-text-primary);
		--nui-menu-border-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-menu-border-primary-selected: var(--nui-border-brand-primary-boldest);
		--nui-menu-fill-primary-default: var(--nui-surface-neutral-subtlest);
		--nui-menu-fill-secondary-default: var(--nui-surface-neutral-subtler);
		--nui-menu-input-border-primary-default: var(--nui-border-transparent);
		--nui-menu-input-border-primary-disabled: var(--nui-border-transparent);
		--nui-menu-input-border-primary-filled: var(--nui-border-transparent);
		--nui-menu-input-border-primary-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-menu-input-border-primary-selected: var(--nui-border-brand-primary-boldest);
		--nui-menu-input-fill-primary-default: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-disabled: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-filled: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-hover: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-selected: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-icon-input-default: var(--nui-icon-secondary);
		--nui-menu-input-icon-input-disabled: var(--nui-icon-disabled);
		--nui-menu-input-icon-input-filled: var(--nui-icon-secondary);
		--nui-menu-input-icon-input-hover: var(--nui-icon-secondary);
		--nui-menu-input-icon-input-selected: var(--nui-icon-primary);
		--nui-menu-input-icon-label-default: var(--nui-icon-primary);
		--nui-menu-input-icon-label-disabled: var(--nui-icon-disabled);
		--nui-menu-input-icon-label-filled: var(--nui-icon-primary);
		--nui-menu-input-icon-label-hover: var(--nui-icon-primary);
		--nui-menu-input-icon-label-selected: var(--nui-icon-primary);
		--nui-menu-input-text-input-default: var(--nui-text-secondary);
		--nui-menu-input-text-input-disabled: var(--nui-text-disabled);
		--nui-menu-input-text-input-filled: var(--nui-text-primary);
		--nui-menu-input-text-input-hover: var(--nui-text-secondary);
		--nui-menu-input-text-input-selected: var(--nui-text-primary);
		--nui-menu-input-text-right-selected: var(--nui-text-primary);
		--nui-menu-item-fill-destructive-default: var(--nui-surface-neutral-subtlest);
		--nui-menu-item-fill-destructive-hover: var(--nui-fill-system-danger-bold);
		--nui-menu-item-fill-destructive-pressed: var(--nui-fill-system-danger-bold);
		--nui-menu-item-fill-destructive-selected: var(--nui-fill-system-danger-bold);
		--nui-menu-item-fill-disabled: var(--nui-surface-neutral-subtlest);
		--nui-menu-item-fill-primary-default: var(--nui-surface-neutral-subtlest);
		--nui-menu-item-fill-primary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-menu-item-fill-primary-pressed: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-fill-primary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-fill-secondary-default: var(--nui-fill-brand-secondary-subtler);
		--nui-menu-item-fill-secondary-hover: var(--nui-fill-brand-secondary-neutral);
		--nui-menu-item-fill-secondary-pressed: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-fill-secondary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-icon-label-destructive-default: var(--nui-icon-error);
		--nui-menu-item-icon-label-destructive-hover: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-destructive-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-destructive-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-disabled: var(--nui-icon-disabled);
		--nui-menu-item-icon-label-primary-default: var(--nui-icon-primary);
		--nui-menu-item-icon-label-primary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-label-primary-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-primary-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-secondary-default: var(--nui-icon-primary);
		--nui-menu-item-icon-label-secondary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-label-secondary-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-secondary-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-destructive-default: var(--nui-icon-error);
		--nui-menu-item-icon-shortcut-destructive-hover: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-destructive-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-destructive-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-disabled: var(--nui-icon-disabled);
		--nui-menu-item-icon-shortcut-primary-default: var(--nui-icon-secondary);
		--nui-menu-item-icon-shortcut-primary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-primary-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-primary-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-secondary-default: var(--nui-icon-secondary);
		--nui-menu-item-icon-shortcut-secondary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-secondary-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-secondary-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-text-label-destructive-default: var(--nui-text-error);
		--nui-menu-item-text-label-destructive-hover: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-destructive-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-destructive-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-disabled: var(--nui-text-disabled);
		--nui-menu-item-text-label-primary-default: var(--nui-text-primary);
		--nui-menu-item-text-label-primary-hover: var(--nui-text-primary);
		--nui-menu-item-text-label-primary-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-primary-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-secondary-default: var(--nui-text-primary);
		--nui-menu-item-text-label-secondary-hover: var(--nui-text-primary);
		--nui-menu-item-text-label-secondary-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-secondary-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-destructive-default: var(--nui-text-error);
		--nui-menu-item-text-shortcut-destructive-hover: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-destructive-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-destructive-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-disabled: var(--nui-text-disabled);
		--nui-menu-item-text-shortcut-primary-default: var(--nui-text-secondary);
		--nui-menu-item-text-shortcut-primary-hover: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-primary-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-primary-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-secondary-default: var(--nui-text-secondary);
		--nui-menu-item-text-shortcut-secondary-hover: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-secondary-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-secondary-selected: var(--nui-text-primary-inverted);
		--nui-menu-search-fill-disabled: var(--nui-fill-neutral-subtlest);
		--nui-menu-search-fill-primary-default: var(--nui-surface-neutral-subtlest);
		--nui-menu-search-fill-primary-filled: var(--nui-fill-brand-secondary-subtlest);
		--nui-menu-search-fill-primary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-menu-search-fill-primary-selected: var(--nui-fill-brand-secondary-subtlest);
		--nui-menu-search-icon-disabled: var(--nui-icon-disabled);
		--nui-menu-search-icon-primary-default: var(--nui-icon-secondary);
		--nui-menu-search-icon-primary-filled: var(--nui-icon-primary);
		--nui-menu-search-icon-primary-hover: var(--nui-icon-primary);
		--nui-menu-search-icon-primary-selected: var(--nui-icon-primary);
		--nui-menu-search-text-disabled: var(--nui-text-disabled);
		--nui-menu-search-text-primary-default: var(--nui-text-secondary);
		--nui-menu-search-text-primary-filled: var(--nui-text-primary);
		--nui-menu-search-text-primary-hover: var(--nui-text-primary);
		--nui-menu-search-text-primary-selected: var(--nui-text-secondary);
		--nui-paginator-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-paginator-text-primary: var(--nui-text-secondary);
		--nui-pill-border-category-green: var(--nui-border-categories-green-bolder);
		--nui-pill-border-category-indigo: var(--nui-border-categories-indigo-bolder);
		--nui-pill-border-category-orange: var(--nui-border-categories-orange-bolder);
		--nui-pill-border-category-pink: var(--nui-border-categories-pink-bolder);
		--nui-pill-border-category-teal: var(--nui-border-categories-teal-bolder);
		--nui-pill-border-category-tomato: var(--nui-border-categories-tomato-bolder);
		--nui-pill-border-error: var(--nui-border-system-danger-boldest);
		--nui-pill-border-info: var(--nui-border-system-info-boldest);
		--nui-pill-border-success: var(--nui-border-system-success-boldest);
		--nui-pill-border-warning: var(--nui-border-system-warning-boldest);
		--nui-pill-fill-primary-category-green: var(--nui-fill-categories-green-bold);
		--nui-pill-fill-primary-category-indigo: var(--nui-fill-categories-indigo-bold);
		--nui-pill-fill-primary-category-orange: var(--nui-fill-categories-orange-bold);
		--nui-pill-fill-primary-category-pink: var(--nui-fill-categories-pink-bold);
		--nui-pill-fill-primary-category-teal: var(--nui-fill-categories-teal-bold);
		--nui-pill-fill-primary-category-tomato: var(--nui-fill-categories-tomato-bold);
		--nui-pill-fill-primary-error: var(--nui-fill-system-danger-bold);
		--nui-pill-fill-primary-info: var(--nui-fill-system-info-bold);
		--nui-pill-fill-primary-neutral: var(--nui-fill-neutral-bold);
		--nui-pill-fill-primary-success: var(--nui-fill-system-success-bold);
		--nui-pill-fill-primary-warning: var(--nui-fill-system-warning-bold);
		--nui-pill-fill-secondary-category-green: var(--nui-fill-categories-green-subtle);
		--nui-pill-fill-secondary-category-indigo: var(--nui-fill-categories-indigo-subtle);
		--nui-pill-fill-secondary-category-orange: var(--nui-fill-categories-orange-subtle);
		--nui-pill-fill-secondary-category-pink: var(--nui-fill-categories-pink-subtle);
		--nui-pill-fill-secondary-category-teal: var(--nui-fill-categories-teal-subtle);
		--nui-pill-fill-secondary-category-tomato: var(--nui-fill-categories-tomato-subtle);
		--nui-pill-fill-secondary-error: var(--nui-fill-system-danger-subtler);
		--nui-pill-fill-secondary-info: var(--nui-fill-system-info-subtler);
		--nui-pill-fill-secondary-neutral: var(--nui-fill-brand-secondary-subtler);
		--nui-pill-fill-secondary-success: var(--nui-fill-system-success-subtler);
		--nui-pill-fill-secondary-warning: var(--nui-fill-system-warning-subtler);
		--nui-pill-text-primary-error: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-green: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-indigo: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-info: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-neutral: var(--nui-text-primary);
		--nui-pill-text-primary-orange: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-pink: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-success: var(--nui-text-primary);
		--nui-pill-text-primary-teal: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-tomato: var(--nui-text-primary-inverted);
		--nui-pill-text-primary-warning: var(--nui-text-primary);
		--nui-pill-text-secondary-error: var(--nui-text-error-boldest);
		--nui-pill-text-secondary-green: var(--nui-text-categories-green-boldest);
		--nui-pill-text-secondary-indigo: var(--nui-text-categories-indigo-boldest);
		--nui-pill-text-secondary-info: var(--nui-text-info-boldest);
		--nui-pill-text-secondary-neutral: var(--nui-text-primary);
		--nui-pill-text-secondary-orange: var(--nui-text-categories-orange-boldest);
		--nui-pill-text-secondary-pink: var(--nui-text-categories-pink-boldest);
		--nui-pill-text-secondary-success: var(--nui-text-success-boldest);
		--nui-pill-text-secondary-teal: var(--nui-text-categories-teal-boldest);
		--nui-pill-text-secondary-tomato: var(--nui-text-categories-tomato-boldest);
		--nui-pill-text-secondary-warning: var(--nui-text-warning-boldest);
		--nui-progress-bar-bar-default: var(--nui-fill-brand-primary-boldest);
		--nui-progress-bar-bar-success: var(--nui-fill-system-success-bold);
		--nui-progress-bar-fill-default: var(--nui-fill-neutral-subtle);
		--nui-range-fill-disabled: var(--nui-fill-neutral-subtle);
		--nui-range-fill-enabled: var(--nui-fill-neutral-subtle);
		--nui-range-fill-hover: var(--nui-fill-neutral-bold);
		--nui-range-fill-value: var(--nui-fill-brand-primary-boldest);
		--nui-range-fill-value-disabled: var(--nui-fill-neutral-bold);
		--nui-range-handle-border-default: var(--nui-border-neutral-secondary-bold);
		--nui-range-handle-border-disabled: var(--nui-border-neutral-subtle);
		--nui-range-handle-fill-default: var(--nui-fill-neutral-subtlest);
		--nui-range-handle-fill-disabled: var(--nui-fill-neutral-subtlest);
		--nui-scroll-fill-thumb-primary-disabled: var(--nui-fill-disabled);
		--nui-scroll-fill-thumb-primary-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-scroll-fill-thumb-primary-hover: var(--nui-fill-neutral-bold);
		--nui-scroll-fill-thumb-primary-pressed: var(--nui-fill-neutral-bold);
		--nui-scroll-fill-track-primary-disabled: var(--nui-fill-neutral-subtle);
		--nui-scroll-fill-track-primary-enabled: var(--nui-surface-transparent);
		--nui-scroll-fill-track-primary-hover: var(--nui-surface-transparent);
		--nui-scroll-fill-track-primary-pressed: var(--nui-surface-transparent);
		--nui-shadows-bold: var(--core-neutral-alpha-7);
		--nui-shadows-subtle: var(--core-neutral-alpha-7);
		--nui-shadows-subtler: var(--core-neutral-alpha-5);
		--nui-shadows-subtlest: var(--core-neutral-alpha-3);
		--nui-skeleton-fill-default: var(--nui-fill-brand-secondary-subtlest);
		--nui-slider-fill: var(--nui-fill-neutral-bold);
		--nui-slider-fill-active: var(--nui-fill-brand-primary-boldest);
		--nui-snackbar-border-error: var(--nui-border-system-danger-boldest);
		--nui-snackbar-border-info: var(--nui-border-system-info-boldest);
		--nui-snackbar-border-neutral: var(--nui-border-neutral-bolder);
		--nui-snackbar-border-success: var(--nui-border-system-success-boldest);
		--nui-snackbar-border-warning: var(--nui-border-system-warning-boldest);
		--nui-snackbar-fill-error: var(--nui-fill-system-danger-subtler);
		--nui-snackbar-fill-info: var(--nui-fill-system-info-subtler);
		--nui-snackbar-fill-neutral: var(--nui-fill-brand-secondary-subtler);
		--nui-snackbar-fill-success: var(--nui-fill-system-success-subtler);
		--nui-snackbar-fill-warning: var(--nui-fill-system-warning-subtler);
		--nui-snackbar-icon-error: var(--nui-icon-error-boldest);
		--nui-snackbar-icon-info: var(--nui-icon-info-boldest);
		--nui-snackbar-icon-neutral: var(--nui-icon-primary);
		--nui-snackbar-icon-success: var(--nui-icon-success-boldest);
		--nui-snackbar-icon-warning: var(--nui-icon-warning-bold);
		--nui-snackbar-text-error: var(--nui-text-error-boldest);
		--nui-snackbar-text-info: var(--nui-text-info-boldest);
		--nui-snackbar-text-neutral: var(--nui-text-primary);
		--nui-snackbar-text-success: var(--nui-text-success-boldest);
		--nui-snackbar-text-warning: var(--nui-text-warning-boldest);
		--nui-stepper-item-icon-border-active: var(--nui-border-brand-primary-boldest);
		--nui-stepper-item-icon-fill-active: var(--nui-fill-brand-primary-boldest);
		--nui-stepper-item-icon-fill-done: var(--nui-fill-neutral-boldest);
		--nui-stepper-item-icon-fill-waiting: var(--nui-fill-transparent);
		--nui-stepper-item-icon-icon-done: var(--nui-icon-primary-inverted);
		--nui-stepper-item-icon-text-active: var(--nui-text-primary-inverted);
		--nui-stepper-item-icon-text-done: var(--nui-text-primary-inverted);
		--nui-stepper-item-icon-text-waiting: var(--nui-text-brand);
		--nui-surface-gradient-bottom: var(--core-brand-metal-3);
		--nui-surface-gradient-top: var(--core-brand-metal-2);
		--nui-surface-neutral-subtler: var(--core-neutral-solid-2);
		--nui-surface-neutral-subtlest: var(--core-bw-white);
		--nui-surface-overlay: var(--core-neutral-alpha-7);
		--nui-surface-transparent: rgba(255, 255, 255, 0%);
		--nui-table-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-table-fill-even: var(--nui-surface-neutral-subtlest);
		--nui-table-fill-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-table-fill-odd: var(--nui-fill-brand-secondary-subtler);
		--nui-table-fill-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-table-fill-selected: var(--nui-fill-brand-primary-subtlest);
		--nui-tabs-border-primary: var(--nui-border-transparent);
		--nui-tabs-border-secondary: var(--nui-border-neutral-subtle);
		--nui-tabs-border-underlined: var(--nui-border-neutral-secondary-bold);
		--nui-tabs-border-underlined-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-tabs-border-underlined-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-tabs-border-underlined-selected: var(--nui-border-brand-primary-boldest);
		--nui-tabs-fill: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary-enabled: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-tabs-fill-secondary: var(--nui-fill-neutral-subtlest);
		--nui-tabs-fill-secondary-enabled: var(--nui-fill-transparent);
		--nui-tabs-fill-secondary-hover: var(--nui-fill-transparent);
		--nui-tabs-fill-secondary-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-tabs-fill-underlined-enabled: var(--nui-fill-transparent);
		--nui-tabs-fill-underlined-hover: var(--nui-fill-transparent);
		--nui-tabs-fill-underlined-selected: var(--nui-fill-transparent);
		--nui-tabs-icon-primary-enabled: var(--nui-icon-primary);
		--nui-tabs-icon-primary-hover: var(--nui-icon-brand);
		--nui-tabs-icon-primary-selected: var(--nui-icon-primary-inverted);
		--nui-tabs-icon-secondary-enabled: var(--nui-icon-primary);
		--nui-tabs-icon-secondary-hover: var(--nui-icon-brand);
		--nui-tabs-icon-secondary-selected: var(--nui-icon-brand);
		--nui-tabs-icon-underlined-enabled: var(--nui-icon-secondary);
		--nui-tabs-icon-underlined-hover: var(--nui-icon-primary);
		--nui-tabs-icon-underlined-selected: var(--nui-icon-primary);
		--nui-tabs-text-primary-enabled: var(--nui-text-primary);
		--nui-tabs-text-primary-hover: var(--nui-text-brand);
		--nui-tabs-text-primary-selected: var(--nui-text-primary-inverted);
		--nui-tabs-text-secondary-enabled: var(--nui-text-primary);
		--nui-tabs-text-secondary-hover: var(--nui-text-brand);
		--nui-tabs-text-secondary-selected: var(--nui-text-brand);
		--nui-tabs-text-underlined-enabled: var(--nui-text-secondary);
		--nui-tabs-text-underlined-hover: var(--nui-text-primary);
		--nui-tabs-text-underlined-selected: var(--nui-text-primary);
		--nui-text-brand: var(--core-brand-solid-9);
		--nui-text-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-text-categories-green-boldest: var(--core-categories-green-solid-12);
		--nui-text-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-text-categories-indigo-boldest: var(--core-categories-indigo-solid-12);
		--nui-text-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-text-categories-orange-boldest: var(--core-categories-orange-solid-12);
		--nui-text-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-text-categories-pink-boldest: var(--core-categories-pink-solid-12);
		--nui-text-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-text-categories-teal-boldest: var(--core-categories-teal-solid-12);
		--nui-text-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-text-categories-tomato-boldest: var(--core-categories-tomato-solid-12);
		--nui-text-disabled: var(--core-neutral-alpha-8);
		--nui-text-error: var(--core-system-error-solid-10);
		--nui-text-error-boldest: var(--core-system-error-solid-12);
		--nui-text-info: var(--core-system-info-solid-10);
		--nui-text-info-boldest: var(--core-system-info-solid-12);
		--nui-text-link: var(--core-brand-solid-9);
		--nui-text-link-hover: var(--core-brand-solid-8);
		--nui-text-link-pressed: var(--core-brand-solid-10);
		--nui-text-negative-number: var(--core-system-error-solid-10);
		--nui-text-positive-number: var(--core-system-success-solid-10);
		--nui-text-primary: var(--core-neutral-solid-12);
		--nui-text-primary-inverted: var(--core-bw-white);
		--nui-text-secondary: var(--core-neutral-solid-11);
		--nui-text-success: var(--core-system-success-solid-10);
		--nui-text-success-boldest: var(--core-system-success-solid-12);
		--nui-text-warning: var(--core-system-warning-solid-10);
		--nui-text-warning-boldest: var(--core-system-warning-solid-12);
		--nui-toggle-fill-default: var(--nui-fill-neutral-bold);
		--nui-toggle-fill-disabled: var(--nui-fill-disabled);
		--nui-toggle-fill-handle: var(--nui-fill-neutral-subtlest);
		--nui-toggle-fill-selected: var(--nui-fill-brand-primary-boldest);
		--nui-tooltip-fill-default: var(--nui-fill-neutral-boldest);
		--nui-tooltip-text-default: var(--nui-text-primary-inverted);
		--nui-waiting: var(--nui-fill-transparent);
	}
	[data-uitheme="dark"], &[data-uitheme="dark"] {
		--core-brand-metal-1: rgba(132, 150, 179, 100%);
		--core-brand-metal-2: rgba(132, 150, 179, 20%);
		--core-brand-metal-3: rgba(132, 150, 179, 10%);
		--core-brand-solid-1: rgba(12, 17, 28, 100%);
		--core-brand-solid-10: rgba(63, 92, 176, 100%);
		--core-brand-solid-11: rgba(147, 180, 255, 100%);
		--core-brand-solid-12: rgba(213, 226, 255, 100%);
		--core-brand-solid-2: rgba(17, 23, 37, 100%);
		--core-brand-solid-3: rgba(23, 36, 72, 100%);
		--core-brand-solid-4: rgba(29, 46, 97, 100%);
		--core-brand-solid-5: rgba(36, 57, 116, 100%);
		--core-brand-solid-6: rgba(45, 68, 132, 100%);
		--core-brand-solid-7: rgba(55, 80, 152, 100%);
		--core-brand-solid-8: rgba(64, 94, 178, 100%);
		--core-brand-solid-9: rgba(61, 99, 221, 100%);
		--core-bw-white: rgba(18, 18, 18, 100%);
		--core-categories-green-solid-1: rgba(14, 21, 18, 100%);
		--core-categories-green-solid-10: rgba(51, 176, 116, 100%);
		--core-categories-green-solid-11: rgba(61, 214, 140, 100%);
		--core-categories-green-solid-12: rgba(177, 241, 203, 100%);
		--core-categories-green-solid-2: rgba(18, 27, 23, 100%);
		--core-categories-green-solid-3: rgba(19, 45, 33, 100%);
		--core-categories-green-solid-4: rgba(17, 59, 41, 100%);
		--core-categories-green-solid-5: rgba(23, 73, 51, 100%);
		--core-categories-green-solid-6: rgba(32, 87, 62, 100%);
		--core-categories-green-solid-7: rgba(40, 104, 74, 100%);
		--core-categories-green-solid-8: rgba(47, 124, 87, 100%);
		--core-categories-green-solid-9: rgba(48, 164, 108, 100%);
		--core-categories-indigo-solid-1: rgba(17, 19, 31, 100%);
		--core-categories-indigo-solid-10: rgba(84, 114, 228, 100%);
		--core-categories-indigo-solid-11: rgba(158, 177, 255, 100%);
		--core-categories-indigo-solid-12: rgba(214, 225, 255, 100%);
		--core-categories-indigo-solid-2: rgba(20, 23, 38, 100%);
		--core-categories-indigo-solid-3: rgba(24, 36, 73, 100%);
		--core-categories-indigo-solid-4: rgba(29, 46, 98, 100%);
		--core-categories-indigo-solid-5: rgba(37, 57, 116, 100%);
		--core-categories-indigo-solid-6: rgba(48, 67, 132, 100%);
		--core-categories-indigo-solid-7: rgba(58, 79, 151, 100%);
		--core-categories-indigo-solid-8: rgba(67, 93, 177, 100%);
		--core-categories-indigo-solid-9: rgba(62, 99, 221, 100%);
		--core-categories-orange-solid-1: rgba(23, 18, 14, 100%);
		--core-categories-orange-solid-10: rgba(255, 128, 31, 100%);
		--core-categories-orange-solid-11: rgba(255, 160, 87, 100%);
		--core-categories-orange-solid-12: rgba(255, 224, 194, 100%);
		--core-categories-orange-solid-2: rgba(30, 22, 15, 100%);
		--core-categories-orange-solid-3: rgba(51, 30, 11, 100%);
		--core-categories-orange-solid-4: rgba(70, 33, 0, 100%);
		--core-categories-orange-solid-5: rgba(86, 40, 0, 100%);
		--core-categories-orange-solid-6: rgba(102, 53, 12, 100%);
		--core-categories-orange-solid-7: rgba(126, 69, 29, 100%);
		--core-categories-orange-solid-8: rgba(163, 88, 41, 100%);
		--core-categories-orange-solid-9: rgba(247, 107, 21, 100%);
		--core-categories-pink-solid-1: rgba(25, 17, 23, 100%);
		--core-categories-pink-solid-10: rgba(222, 81, 168, 100%);
		--core-categories-pink-solid-11: rgba(255, 141, 204, 100%);
		--core-categories-pink-solid-12: rgba(253, 209, 234, 100%);
		--core-categories-pink-solid-2: rgba(33, 18, 29, 100%);
		--core-categories-pink-solid-3: rgba(55, 23, 47, 100%);
		--core-categories-pink-solid-4: rgba(75, 20, 61, 100%);
		--core-categories-pink-solid-5: rgba(89, 28, 71, 100%);
		--core-categories-pink-solid-6: rgba(105, 41, 85, 100%);
		--core-categories-pink-solid-7: rgba(131, 56, 105, 100%);
		--core-categories-pink-solid-8: rgba(168, 72, 133, 100%);
		--core-categories-pink-solid-9: rgba(214, 64, 159, 100%);
		--core-categories-teal-solid-1: rgba(13, 21, 20, 100%);
		--core-categories-teal-solid-10: rgba(14, 179, 158, 100%);
		--core-categories-teal-solid-11: rgba(11, 216, 182, 100%);
		--core-categories-teal-solid-12: rgba(173, 240, 221, 100%);
		--core-categories-teal-solid-2: rgba(17, 28, 27, 100%);
		--core-categories-teal-solid-3: rgba(13, 45, 42, 100%);
		--core-categories-teal-solid-4: rgba(2, 59, 55, 100%);
		--core-categories-teal-solid-5: rgba(8, 72, 67, 100%);
		--core-categories-teal-solid-6: rgba(20, 87, 80, 100%);
		--core-categories-teal-solid-7: rgba(28, 105, 97, 100%);
		--core-categories-teal-solid-8: rgba(32, 126, 115, 100%);
		--core-categories-teal-solid-9: rgba(18, 165, 148, 100%);
		--core-categories-tomato-solid-1: rgba(24, 17, 17, 100%);
		--core-categories-tomato-solid-10: rgba(236, 97, 66, 100%);
		--core-categories-tomato-solid-11: rgba(255, 151, 125, 100%);
		--core-categories-tomato-solid-12: rgba(251, 211, 203, 100%);
		--core-categories-tomato-solid-2: rgba(31, 21, 19, 100%);
		--core-categories-tomato-solid-3: rgba(57, 23, 20, 100%);
		--core-categories-tomato-solid-4: rgba(78, 21, 17, 100%);
		--core-categories-tomato-solid-5: rgba(94, 28, 22, 100%);
		--core-categories-tomato-solid-6: rgba(110, 41, 32, 100%);
		--core-categories-tomato-solid-7: rgba(133, 58, 45, 100%);
		--core-categories-tomato-solid-8: rgba(172, 77, 57, 100%);
		--core-categories-tomato-solid-9: rgba(229, 77, 46, 100%);
		--core-neutral-alpha-1: rgba(0, 0, 0, 0%);
		--core-neutral-alpha-10: rgba(229, 237, 253, 48%);
		--core-neutral-alpha-11: rgba(241, 247, 254, 71%);
		--core-neutral-alpha-12: rgba(252, 253, 255, 94%);
		--core-neutral-alpha-2: rgba(216, 244, 246, 4%);
		--core-neutral-alpha-3: rgba(221, 234, 248, 8%);
		--core-neutral-alpha-4: rgba(211, 237, 248, 11%);
		--core-neutral-alpha-5: rgba(217, 237, 254, 15%);
		--core-neutral-alpha-6: rgba(214, 235, 253, 19%);
		--core-neutral-alpha-7: rgba(217, 237, 255, 25%);
		--core-neutral-alpha-8: rgba(217, 237, 255, 36%);
		--core-neutral-alpha-9: rgba(223, 235, 253, 43%);
		--core-neutral-solid-1: rgba(17, 17, 19, 100%);
		--core-neutral-solid-10: rgba(119, 123, 132, 100%);
		--core-neutral-solid-11: rgba(176, 180, 186, 100%);
		--core-neutral-solid-12: rgba(237, 238, 240, 100%);
		--core-neutral-solid-2: rgba(24, 25, 27, 100%);
		--core-neutral-solid-3: rgba(33, 34, 37, 100%);
		--core-neutral-solid-4: rgba(39, 42, 45, 100%);
		--core-neutral-solid-5: rgba(46, 49, 53, 100%);
		--core-neutral-solid-6: rgba(54, 58, 63, 100%);
		--core-neutral-solid-7: rgba(67, 72, 78, 100%);
		--core-neutral-solid-8: rgba(90, 97, 105, 100%);
		--core-neutral-solid-9: rgba(105, 110, 119, 100%);
		--core-system-error-solid-1: rgba(21, 15, 16, 100%);
		--core-system-error-solid-10: rgba(212, 101, 118, 100%);
		--core-system-error-solid-11: rgba(255, 150, 163, 100%);
		--core-system-error-solid-12: rgba(250, 213, 216, 100%);
		--core-system-error-solid-2: rgba(29, 21, 22, 100%);
		--core-system-error-solid-3: rgba(53, 24, 28, 100%);
		--core-system-error-solid-4: rgba(72, 27, 34, 100%);
		--core-system-error-solid-5: rgba(87, 34, 43, 100%);
		--core-system-error-solid-6: rgba(103, 46, 55, 100%);
		--core-system-error-solid-7: rgba(127, 61, 71, 100%);
		--core-system-error-solid-8: rgba(167, 81, 93, 100%);
		--core-system-error-solid-9: rgba(225, 113, 129, 100%);
		--core-system-info-solid-1: rgba(8, 18, 28, 100%);
		--core-system-info-solid-10: rgba(0, 130, 240, 100%);
		--core-system-info-solid-11: rgba(112, 184, 255, 100%);
		--core-system-info-solid-12: rgba(201, 227, 255, 100%);
		--core-system-info-solid-2: rgba(14, 25, 38, 100%);
		--core-system-info-solid-3: rgba(10, 40, 71, 100%);
		--core-system-info-solid-4: rgba(0, 50, 98, 100%);
		--core-system-info-solid-5: rgba(1, 63, 117, 100%);
		--core-system-info-solid-6: rgba(17, 77, 134, 100%);
		--core-system-info-solid-7: rgba(29, 94, 157, 100%);
		--core-system-info-solid-8: rgba(36, 113, 189, 100%);
		--core-system-info-solid-9: rgba(0, 143, 254, 100%);
		--core-system-success-solid-1: rgba(12, 19, 17, 100%);
		--core-system-success-solid-10: rgba(80, 180, 155, 100%);
		--core-system-success-solid-11: rgba(110, 209, 183, 100%);
		--core-system-success-solid-12: rgba(186, 242, 225, 100%);
		--core-system-success-solid-2: rgba(17, 27, 24, 100%);
		--core-system-success-solid-3: rgba(17, 44, 37, 100%);
		--core-system-success-solid-4: rgba(11, 58, 48, 100%);
		--core-system-success-solid-5: rgba(20, 71, 59, 100%);
		--core-system-success-solid-6: rgba(31, 86, 73, 100%);
		--core-system-success-solid-7: rgba(42, 104, 89, 100%);
		--core-system-success-solid-8: rgba(50, 126, 107, 100%);
		--core-system-success-solid-9: rgba(92, 191, 166, 100%);
		--core-system-warning-solid-1: rgba(20, 17, 12, 100%);
		--core-system-warning-solid-10: rgba(219, 156, 0, 100%);
		--core-system-warning-solid-11: rgba(249, 189, 71, 100%);
		--core-system-warning-solid-12: rgba(251, 229, 191, 100%);
		--core-system-warning-solid-2: rgba(28, 23, 15, 100%);
		--core-system-warning-solid-3: rgba(45, 33, 11, 100%);
		--core-system-warning-solid-4: rgba(61, 40, 0, 100%);
		--core-system-warning-solid-5: rgba(74, 50, 0, 100%);
		--core-system-warning-solid-6: rgba(88, 63, 10, 100%);
		--core-system-warning-solid-7: rgba(109, 81, 27, 100%);
		--core-system-warning-solid-8: rgba(138, 104, 36, 100%);
		--core-system-warning-solid-9: rgba(231, 167, 0, 100%);
		--nui-accordion-rich-border-default: var(--nui-border-neutral-subtle);
		--nui-accordion-rich-border-hover: var(--nui-border-neutral-secondary-bold);
		--nui-accordion-rich-border-open: var(--nui-border-neutral-secondary-bold);
		--nui-accordion-rich-fill-default: var(--nui-surface-neutral-subtler);
		--nui-accordion-rich-fill-divider: var(--nui-border-neutral-secondary-bold);
		--nui-accordion-rich-fill-hover: var(--nui-surface-neutral-subtler);
		--nui-accordion-rich-fill-selected: var(--nui-surface-neutral-subtler);
		--nui-accordion-rich-text-body: var(--nui-text-primary);
		--nui-accordion-rich-text-headline: var(--nui-text-primary);
		--nui-accordion-rich-text-subheadline: var(--nui-text-secondary);
		--nui-accordion-simple-fill-default: var(--nui-fill-transparent);
		--nui-accordion-simple-text-body: var(--nui-text-primary);
		--nui-accordion-simple-text-headline: var(--nui-text-primary);
		--nui-active: var(--nui-fill-brand-primary-boldest);
		--nui-banner-fill-error: var(--nui-fill-system-danger-subtler);
		--nui-banner-fill-info: var(--nui-fill-system-info-subtler);
		--nui-banner-fill-neutral: var(--nui-fill-brand-secondary-subtler);
		--nui-banner-fill-success: var(--nui-fill-system-success-subtler);
		--nui-banner-fill-warning: var(--nui-fill-system-warning-subtler);
		--nui-banner-icon-error: var(--nui-icon-error-boldest);
		--nui-banner-icon-info: var(--nui-icon-info-boldest);
		--nui-banner-icon-neutral: var(--nui-icon-primary);
		--nui-banner-icon-success: var(--nui-icon-success-boldest);
		--nui-banner-icon-warning: var(--nui-icon-warning-bold);
		--nui-banner-text-error: var(--nui-text-error-boldest);
		--nui-banner-text-info: var(--nui-text-info-boldest);
		--nui-banner-text-neutral: var(--nui-text-primary);
		--nui-banner-text-success: var(--nui-text-success-boldest);
		--nui-banner-text-warning: var(--nui-text-warning-boldest);
		--nui-border-brand-primary-boldest: var(--core-brand-solid-10);
		--nui-border-brand-primary-boldest-hover: var(--core-brand-solid-8);
		--nui-border-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-border-categories-green-bolder: var(--core-categories-green-solid-11);
		--nui-border-categories-green-subtle: var(--core-categories-green-solid-6);
		--nui-border-categories-green-subtler: var(--core-categories-green-solid-3);
		--nui-border-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-border-categories-indigo-bolder: var(--core-categories-indigo-solid-11);
		--nui-border-categories-indigo-subtle: var(--core-categories-indigo-solid-6);
		--nui-border-categories-indigo-subtler: var(--core-categories-indigo-solid-3);
		--nui-border-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-border-categories-orange-bolder: var(--core-categories-orange-solid-11);
		--nui-border-categories-orange-subtle: var(--core-categories-orange-solid-6);
		--nui-border-categories-orange-subtler: var(--core-categories-orange-solid-3);
		--nui-border-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-border-categories-pink-bolder: var(--core-categories-pink-solid-11);
		--nui-border-categories-pink-subtle: var(--core-categories-pink-solid-6);
		--nui-border-categories-pink-subtler: var(--core-categories-pink-solid-3);
		--nui-border-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-border-categories-teal-bolder: var(--core-categories-teal-solid-11);
		--nui-border-categories-teal-subtle: var(--core-categories-teal-solid-6);
		--nui-border-categories-teal-subtler: var(--core-categories-teal-solid-3);
		--nui-border-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-border-categories-tomato-bolder: var(--core-categories-tomato-solid-11);
		--nui-border-categories-tomato-subtle: var(--core-categories-tomato-solid-6);
		--nui-border-categories-tomato-subtler: var(--core-categories-tomato-solid-3);
		--nui-border-disabled: var(--core-neutral-solid-8);
		--nui-border-neutral-bolder: var(--core-neutral-solid-11);
		--nui-border-neutral-secondary-bold: var(--core-neutral-solid-7);
		--nui-border-neutral-secondary-boldest: var(--core-neutral-solid-10);
		--nui-border-neutral-subtle: var(--core-neutral-solid-3);
		--nui-border-system-danger-bold: var(--core-system-error-solid-8);
		--nui-border-system-danger-boldest: var(--core-system-error-solid-11);
		--nui-border-system-focus: var(--core-brand-solid-5);
		--nui-border-system-info-boldest: var(--core-system-info-solid-9);
		--nui-border-system-success-boldest: var(--core-system-success-solid-9);
		--nui-border-system-warning-boldest: var(--core-system-warning-solid-9);
		--nui-border-transparent: rgba(255, 255, 255, 0%);
		--nui-breadcrumb-fill-primary-default: var(--nui-fill-brand-secondary-subtler);
		--nui-breadcrumb-fill-primary-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-breadcrumb-fill-secondary-default: var(--nui-fill-transparent);
		--nui-breadcrumb-fill-secondary-hover: var(--nui-fill-transparent);
		--nui-breadcrumb-icon-chevron-primary-default: var(--nui-icon-brand);
		--nui-breadcrumb-icon-chevron-primary-hover: var(--nui-icon-brand);
		--nui-breadcrumb-icon-chevron-primary-last: var(--nui-icon-brand);
		--nui-breadcrumb-icon-chevron-secondary-default: var(--nui-icon-primary);
		--nui-breadcrumb-icon-chevron-secondary-hover: var(--nui-icon-primary);
		--nui-breadcrumb-icon-chevron-secondary-last: var(--nui-icon-primary);
		--nui-breadcrumb-icon-left-primary-default: var(--nui-icon-brand);
		--nui-breadcrumb-icon-left-primary-hover: var(--nui-icon-brand);
		--nui-breadcrumb-icon-left-primary-last: var(--nui-icon-brand);
		--nui-breadcrumb-icon-left-secondary-default: var(--nui-icon-secondary);
		--nui-breadcrumb-icon-left-secondary-hover: var(--nui-icon-primary);
		--nui-breadcrumb-icon-left-secondary-last: var(--nui-icon-primary);
		--nui-breadcrumb-text-primary-default: var(--nui-text-brand);
		--nui-breadcrumb-text-primary-hover: var(--nui-text-brand);
		--nui-breadcrumb-text-primary-last: var(--nui-text-brand);
		--nui-breadcrumb-text-secondary-default: var(--nui-text-secondary);
		--nui-breadcrumb-text-secondary-hover: var(--nui-text-primary);
		--nui-breadcrumb-text-secondary-last: var(--nui-text-primary);
		--nui-button-border-secondary-destructive: var(--nui-border-system-danger-boldest);
		--nui-button-fill-primary: var(--nui-fill-brand-primary-boldest);
		--nui-button-fill-primary-destructive: var(--nui-fill-system-danger-bold);
		--nui-button-fill-primary-destructive-hover: var(--nui-fill-neutral-subtlest);
		--nui-button-fill-primary-disabled: var(--nui-fill-brand-secondary-subtler);
		--nui-button-fill-primary-hover: var(--nui-fill-brand-primary-boldest-hover);
		--nui-button-fill-primary-pressed: var(--nui-fill-brand-primary-boldest);
		--nui-button-fill-secondary: var(--nui-fill-brand-secondary-subtler);
		--nui-button-fill-secondary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-button-fill-secondary-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-button-fill-secondary-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-button-fill-transparent: var(--nui-fill-transparent);
		--nui-button-group-border-default: var(--nui-border-neutral-subtle);
		--nui-button-group-fill-default: var(--nui-fill-brand-secondary-subtler);
		--nui-button-group-item-fill-disabled: var(--nui-fill-transparent);
		--nui-button-group-item-fill-enabled: var(--nui-fill-transparent);
		--nui-button-group-item-fill-selected: var(--nui-fill-neutral-subtlest);
		--nui-button-group-item-icon-disabled: var(--nui-icon-disabled);
		--nui-button-group-item-icon-enabled: var(--nui-icon-secondary);
		--nui-button-group-item-icon-selected: var(--nui-icon-primary);
		--nui-button-group-item-text-disabled: var(--nui-text-disabled);
		--nui-button-group-item-text-enabled: var(--nui-text-secondary);
		--nui-button-group-item-text-selected: var(--nui-text-primary);
		--nui-button-icon-brand: var(--nui-icon-brand);
		--nui-button-icon-disabled: var(--nui-icon-disabled);
		--nui-button-icon-primary: var(--nui-icon-primary);
		--nui-button-icon-primary-destructive: var(--nui-icon-primary);
		--nui-button-icon-primary-destructive-inverted: var(--nui-icon-error);
		--nui-button-icon-primary-inverted: var(--nui-icon-primary);
		--nui-button-icon-secondary: var(--nui-icon-secondary);
		--nui-button-text-brand: var(--nui-text-brand);
		--nui-button-text-disabled: var(--nui-text-disabled);
		--nui-button-text-primary: var(--nui-text-primary);
		--nui-button-text-primary-destructive: var(--nui-text-primary);
		--nui-button-text-primary-destructive-inverted: var(--nui-text-error);
		--nui-button-text-primary-inverted: var(--nui-text-primary);
		--nui-button-text-secondary: var(--nui-text-secondary);
		--nui-button-text-tetriary: var(--nui-text-primary);
		--nui-calendar-date-picker-item-fill-disabled: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-fill-enabled: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-fill-hover: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-fill-previous-next: var(--nui-fill-transparent);
		--nui-calendar-date-picker-item-fill-selected: var(--nui-fill-brand-primary-boldest);
		--nui-calendar-date-picker-item-fill-selected-span: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-date-picker-item-fill-today: var(--nui-surface-neutral-subtlest);
		--nui-calendar-date-picker-item-text-disabled: var(--nui-text-disabled);
		--nui-calendar-date-picker-item-text-enabled: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-hover: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-previous-next: var(--nui-text-secondary);
		--nui-calendar-date-picker-item-text-selected: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-selected-span: var(--nui-text-primary);
		--nui-calendar-date-picker-item-text-today: var(--nui-text-brand);
		--nui-calendar-month-border-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-calendar-month-date-border-disabled: var(--nui-border-neutral-secondary-bold);
		--nui-calendar-month-date-border-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-calendar-month-date-border-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-calendar-month-date-border-selected: var(--nui-border-brand-primary-boldest);
		--nui-calendar-month-date-fill-disabled: var(--nui-surface-neutral-subtler);
		--nui-calendar-month-date-fill-enabled: var(--nui-surface-neutral-subtler);
		--nui-calendar-month-date-fill-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-month-date-fill-selected: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-month-date-text-disabled: var(--nui-text-disabled);
		--nui-calendar-month-date-text-enabled: var(--nui-text-primary);
		--nui-calendar-month-date-text-hover: var(--nui-text-primary);
		--nui-calendar-month-date-text-selected: var(--nui-text-primary);
		--nui-calendar-month-event-fill-enabled: var(--nui-fill-categories-teal-bold);
		--nui-calendar-month-event-icon-enabled: var(--nui-icon-primary);
		--nui-calendar-month-event-text-enabled: var(--nui-text-primary);
		--nui-calendar-month-fill-deafult: var(--nui-surface-neutral-subtler);
		--nui-calendar-month-text-week-number: var(--nui-text-secondary);
		--nui-calendar-month-text-weekday: var(--nui-text-primary);
		--nui-calendar-schedule-item-border: var(--core-categories-teal-solid-9);
		--nui-calendar-schedule-item-fill-default: var(--nui-fill-neutral-subtlest);
		--nui-calendar-schedule-item-fill-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-calendar-schedule-item-icon-default: var(--nui-icon-secondary);
		--nui-calendar-schedule-item-text-date: var(--nui-text-secondary);
		--nui-calendar-schedule-item-text-name: var(--nui-text-primary);
		--nui-card-border-default: var(--nui-border-neutral-secondary-bold);
		--nui-card-border-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-card-border-selected: var(--nui-border-brand-primary-boldest);
		--nui-card-fill-default: var(--nui-surface-neutral-subtler);
		--nui-card-fill-hover: var(--nui-fill-brand-secondary-subtlest);
		--nui-card-fill-selected: var(--nui-fill-brand-primary-subtlest);
		--nui-chat-avatar-fill-hover: var(--nui-surface-neutral-subtler);
		--nui-chat-border-default: var(--nui-border-neutral-subtle);
		--nui-chat-emoji-border-default: var(--nui-border-neutral-subtle);
		--nui-chat-emoji-fill-default: var(--nui-surface-neutral-subtler);
		--nui-chat-fill-default: var(--nui-surface-neutral-subtler);
		--nui-chat-fill-highlight: var(--nui-fill-brand-primary-subtlest);
		--nui-chat-input-border-active-top: var(--nui-border-neutral-secondary-bold);
		--nui-chat-thread-fill-closed-default: var(--nui-fill-brand-secondary-subtlest);
		--nui-chat-thread-fill-closed-highlight: var(--nui-fill-brand-primary-subtlest);
		--nui-chat-thread-fill-open-default: var(--nui-surface-neutral-subtler);
		--nui-checkbox-item-checkbox-fill-checked: var(--nui-icon-primary);
		--nui-checkbox-item-checkbox-fill-disabled: var(--nui-icon-disabled);
		--nui-checkbox-item-checkbox-fill-hover: var(--nui-icon-brand);
		--nui-checkbox-item-checkbox-icon-checked: var(--nui-icon-primary-inverted);
		--nui-checkbox-item-checkbox-icon-disabled: var(--nui-icon-primary-inverted);
		--nui-checkbox-item-checkbox-icon-hover: var(--nui-icon-primary-inverted);
		--nui-checkbox-item-icon-default: var(--nui-icon-primary);
		--nui-checkbox-item-icon-disabled: var(--nui-icon-disabled);
		--nui-checkbox-item-icon-hover: var(--nui-icon-brand);
		--nui-chips-border-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-primary-default-hover: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-primary-selected: var(--nui-border-transparent);
		--nui-chips-border-primary-selected-hover: var(--nui-border-transparent);
		--nui-chips-border-secondary-default: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-secondary-default-hover: var(--nui-border-neutral-secondary-bold);
		--nui-chips-border-secondary-selected: var(--nui-border-transparent);
		--nui-chips-border-secondary-selected-hover: var(--nui-border-neutral-subtle);
		--nui-chips-fill-primary-default: var(--nui-fill-neutral-subtlest);
		--nui-chips-fill-primary-default-hover: var(--nui-fill-neutral-subtle);
		--nui-chips-fill-primary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-chips-fill-primary-selected-hover: var(--nui-fill-brand-primary-boldest-hover);
		--nui-chips-fill-secondary-default: var(--nui-fill-brand-secondary-subtler);
		--nui-chips-fill-secondary-default-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-chips-fill-secondary-selected: var(--nui-fill-neutral-boldest);
		--nui-chips-fill-secondary-selected-hover: var(--nui-fill-neutral-boldest);
		--nui-dialog-border-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-dialog-fill-primary-default: var(--nui-surface-neutral-subtler);
		--nui-dialog-icon-primary-default: var(--nui-icon-primary);
		--nui-dialog-text-body: var(--nui-text-secondary);
		--nui-dialog-text-headline: var(--nui-text-primary);
		--nui-divider-fill-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-done: var(--nui-fill-neutral-boldest);
		--nui-fill-brand-primary-boldest: var(--core-brand-solid-9);
		--nui-fill-brand-primary-boldest-hover: var(--core-brand-solid-10);
		--nui-fill-brand-primary-subtlest: var(--core-brand-solid-3);
		--nui-fill-brand-secondary-neutral: var(--core-neutral-solid-7);
		--nui-fill-brand-secondary-subtle: var(--core-neutral-solid-6);
		--nui-fill-brand-secondary-subtler: var(--core-neutral-solid-5);
		--nui-fill-brand-secondary-subtlest: var(--core-neutral-solid-4);
		--nui-fill-categories-green-bold: var(--core-categories-green-solid-7);
		--nui-fill-categories-green-boldest: var(--core-categories-green-solid-11);
		--nui-fill-categories-green-subtle: var(--core-categories-green-solid-4);
		--nui-fill-categories-green-subtlest: var(--core-categories-green-solid-3);
		--nui-fill-categories-indigo-bold: var(--core-categories-indigo-solid-7);
		--nui-fill-categories-indigo-boldest: var(--core-categories-indigo-solid-11);
		--nui-fill-categories-indigo-subtle: var(--core-categories-indigo-solid-4);
		--nui-fill-categories-indigo-subtlest: var(--core-categories-indigo-solid-3);
		--nui-fill-categories-orange-bold: var(--core-categories-orange-solid-7);
		--nui-fill-categories-orange-boldest: var(--core-categories-orange-solid-11);
		--nui-fill-categories-orange-subtle: var(--core-categories-orange-solid-4);
		--nui-fill-categories-orange-subtlest: var(--core-categories-orange-solid-3);
		--nui-fill-categories-pink-bold: var(--core-categories-pink-solid-7);
		--nui-fill-categories-pink-boldest: var(--core-categories-pink-solid-11);
		--nui-fill-categories-pink-subtle: var(--core-categories-pink-solid-4);
		--nui-fill-categories-pink-subtlest: var(--core-categories-pink-solid-3);
		--nui-fill-categories-teal-bold: var(--core-categories-teal-solid-7);
		--nui-fill-categories-teal-boldest: var(--core-categories-teal-solid-11);
		--nui-fill-categories-teal-subtle: var(--core-categories-teal-solid-4);
		--nui-fill-categories-teal-subtlest: var(--core-categories-teal-solid-3);
		--nui-fill-categories-tomato-bold: var(--core-categories-tomato-solid-7);
		--nui-fill-categories-tomato-boldest: var(--core-categories-tomato-solid-11);
		--nui-fill-categories-tomato-subtle: var(--core-categories-tomato-solid-4);
		--nui-fill-categories-tomato-subtlest: var(--core-categories-tomato-solid-3);
		--nui-fill-disabled: var(--core-neutral-solid-7);
		--nui-fill-neutral-bold: var(--core-neutral-solid-9);
		--nui-fill-neutral-boldest: var(--core-neutral-solid-12);
		--nui-fill-neutral-subtle: var(--core-neutral-solid-5);
		--nui-fill-neutral-subtlest: var(--core-neutral-solid-3);
		--nui-fill-system-danger-bold: var(--core-system-error-solid-7);
		--nui-fill-system-danger-subtle: var(--core-system-error-solid-5);
		--nui-fill-system-danger-subtler: var(--core-system-error-solid-4);
		--nui-fill-system-info-bold: var(--core-system-info-solid-7);
		--nui-fill-system-info-subtler: var(--core-system-info-solid-4);
		--nui-fill-system-recording: var(--core-system-error-solid-9);
		--nui-fill-system-success-bold: var(--core-system-success-solid-7);
		--nui-fill-system-success-subtler: var(--core-system-success-solid-4);
		--nui-fill-system-warning-bold: var(--core-system-warning-solid-8);
		--nui-fill-system-warning-subtler: var(--core-system-warning-solid-4);
		--nui-fill-transparent: rgba(255, 255, 255, 0%);
		--nui-forms-border-primary-disabled: var(--nui-border-disabled);
		--nui-forms-border-primary-enabled: var(--nui-border-neutral-secondary-boldest);
		--nui-forms-border-primary-error: var(--nui-border-system-danger-boldest);
		--nui-forms-border-primary-filled: var(--nui-border-neutral-bolder);
		--nui-forms-border-primary-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-forms-border-primary-selected: var(--nui-border-brand-primary-boldest);
		--nui-forms-border-secondary-disabled: var(--nui-border-disabled);
		--nui-forms-border-secondary-enabled: var(--nui-border-transparent);
		--nui-forms-border-secondary-error: var(--nui-border-system-danger-boldest);
		--nui-forms-border-secondary-filled: var(--nui-border-transparent);
		--nui-forms-border-secondary-hover: var(--nui-border-neutral-secondary-boldest);
		--nui-forms-border-secondary-selected: var(--nui-border-brand-primary-boldest);
		--nui-forms-fill-primary-disabled: var(--nui-surface-neutral-subtler);
		--nui-forms-fill-primary-enabled: var(--nui-surface-neutral-subtler);
		--nui-forms-fill-primary-error: var(--nui-surface-neutral-subtler);
		--nui-forms-fill-primary-filled: var(--nui-surface-neutral-subtler);
		--nui-forms-fill-primary-hover: var(--nui-surface-neutral-subtler);
		--nui-forms-fill-primary-selected: var(--nui-surface-neutral-subtler);
		--nui-forms-fill-secondary-disabled: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-enabled: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-error: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-filled: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-fill-secondary-selected: var(--nui-fill-brand-secondary-subtler);
		--nui-forms-icon-primary-disabled: var(--nui-icon-disabled);
		--nui-forms-icon-primary-enabled: var(--nui-icon-secondary);
		--nui-forms-icon-primary-error: var(--nui-icon-primary);
		--nui-forms-icon-primary-filled: var(--nui-icon-primary);
		--nui-forms-icon-primary-hover: var(--nui-icon-secondary);
		--nui-forms-icon-primary-selected: var(--nui-icon-primary);
		--nui-forms-icon-secondary-disabled: var(--nui-icon-disabled);
		--nui-forms-icon-secondary-enabled: var(--nui-icon-secondary);
		--nui-forms-icon-secondary-error: var(--nui-icon-primary);
		--nui-forms-icon-secondary-filled: var(--nui-icon-primary);
		--nui-forms-icon-secondary-hover: var(--nui-icon-secondary);
		--nui-forms-icon-secondary-selected: var(--nui-icon-primary);
		--nui-forms-text-help-primary-disabled: var(--nui-text-disabled);
		--nui-forms-text-help-primary-enabled: var(--nui-text-secondary);
		--nui-forms-text-help-primary-error: var(--nui-text-error);
		--nui-forms-text-help-primary-filled: var(--nui-text-secondary);
		--nui-forms-text-help-primary-hover: var(--nui-text-secondary);
		--nui-forms-text-help-primary-selected: var(--nui-text-secondary);
		--nui-forms-text-input-primary-disabled: var(--nui-text-disabled);
		--nui-forms-text-input-primary-enabled: var(--nui-text-secondary);
		--nui-forms-text-input-primary-error: var(--nui-text-primary);
		--nui-forms-text-input-primary-filled: var(--nui-text-primary);
		--nui-forms-text-input-primary-hover: var(--nui-text-secondary);
		--nui-forms-text-input-primary-selected: var(--nui-text-primary);
		--nui-forms-text-input-secondary-disabled: var(--nui-text-disabled);
		--nui-forms-text-input-secondary-enabled: var(--nui-text-secondary);
		--nui-forms-text-input-secondary-error: var(--nui-text-primary);
		--nui-forms-text-input-secondary-filled: var(--nui-text-primary);
		--nui-forms-text-input-secondary-hover: var(--nui-text-secondary);
		--nui-forms-text-input-secondary-selected: var(--nui-text-primary);
		--nui-forms-text-label-primary-disabled: var(--nui-text-disabled);
		--nui-forms-text-label-primary-enabled: var(--nui-text-primary);
		--nui-forms-text-label-primary-error: var(--nui-text-error);
		--nui-forms-text-label-primary-filled: var(--nui-text-primary);
		--nui-forms-text-label-primary-hover: var(--nui-text-primary);
		--nui-forms-text-label-primary-selected: var(--nui-text-primary);
		--nui-forms-text-suffix-secondary-disabled: var(--nui-text-disabled);
		--nui-forms-text-suffix-secondary-enabled: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-error: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-filled: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-hover: var(--nui-text-secondary);
		--nui-forms-text-suffix-secondary-selected: var(--nui-text-secondary);
		--nui-grid-item-fill-default: var(--nui-surface-neutral-subtler);
		--nui-grid-item-fill-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-grid-item-header-icon-default: var(--nui-icon-primary);
		--nui-grid-item-header-icon-hover: var(--nui-icon-primary);
		--nui-grid-item-header-text-default: var(--nui-text-primary);
		--nui-grid-item-header-text-hover: var(--nui-text-primary);
		--nui-header-border-solid-default: var(--nui-border-neutral-secondary-bold);
		--nui-header-border-transparent-default: var(--nui-border-transparent);
		--nui-header-fill-solid-default: var(--nui-surface-neutral-subtler);
		--nui-header-fill-transparent-default: var(--nui-surface-transparent);
		--nui-headline: var(--nui-text-primary);
		--nui-icon-brand: var(--core-brand-solid-11);
		--nui-icon-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-icon-categories-green-bolder: var(--core-categories-green-solid-12);
		--nui-icon-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-icon-categories-indigo-boldest: var(--core-categories-indigo-solid-12);
		--nui-icon-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-icon-categories-orange-bolder: var(--core-categories-orange-solid-12);
		--nui-icon-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-icon-categories-pink-boldest: var(--core-categories-pink-solid-12);
		--nui-icon-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-icon-categories-teal-boldest: var(--core-categories-teal-solid-12);
		--nui-icon-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-icon-categories-tomato-boldest: var(--core-categories-tomato-solid-12);
		--nui-icon-disabled: var(--core-neutral-alpha-9);
		--nui-icon-error: var(--core-system-error-solid-11);
		--nui-icon-error-boldest: var(--core-system-error-solid-12);
		--nui-icon-info: var(--core-system-info-solid-9);
		--nui-icon-info-boldest: var(--core-system-info-solid-12);
		--nui-icon-primary: var(--core-neutral-solid-12);
		--nui-icon-primary-inverted: var(--core-neutral-solid-1);
		--nui-icon-secondary: var(--core-neutral-solid-11);
		--nui-icon-status-approved: var(--core-system-success-solid-10);
		--nui-icon-status-for-review: var(--core-system-info-solid-9);
		--nui-icon-status-in-progress: var(--core-system-warning-solid-9);
		--nui-icon-status-no-status: var(--core-neutral-solid-9);
		--nui-icon-status-not-approved: var(--core-system-error-solid-10);
		--nui-icon-success: var(--core-system-success-solid-9);
		--nui-icon-success-boldest: var(--core-system-success-solid-12);
		--nui-icon-warning: var(--core-system-warning-solid-10);
		--nui-icon-warning-bold: var(--core-system-warning-solid-12);
		--nui-label-icon-brand: var(--nui-icon-brand);
		--nui-label-icon-destructive: var(--nui-icon-error);
		--nui-label-icon-disabled: var(--nui-icon-disabled);
		--nui-label-icon-inverted: var(--nui-icon-primary-inverted);
		--nui-label-icon-primary: var(--nui-icon-primary);
		--nui-label-icon-secondary: var(--nui-icon-secondary);
		--nui-label-text-brand: var(--nui-text-brand);
		--nui-label-text-destructive: var(--nui-text-error);
		--nui-label-text-disabled: var(--nui-text-disabled);
		--nui-label-text-inverted: var(--nui-text-primary-inverted);
		--nui-label-text-neutral: var(--nui-text-primary);
		--nui-label-text-secondary: var(--nui-text-secondary);
		--nui-list-item-rich-border-default: var(--nui-border-neutral-subtle);
		--nui-list-item-rich-fill-default: var(--nui-surface-neutral-subtler);
		--nui-list-item-rich-fill-divider: var(--nui-border-neutral-secondary-bold);
		--nui-list-item-rich-fill-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-list-item-rich-fill-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-list-item-rich-fill-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-list-item-rich-text-headline: var(--nui-text-primary);
		--nui-list-item-simple-fill-default: var(--nui-surface-neutral-subtler);
		--nui-list-item-simple-fill-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-list-item-simple-fill-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-list-item-simple-fill-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-list-item-simple-icon-left-default: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-default-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-hover: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-hover-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-pressed: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-pressed-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-selected: var(--nui-icon-primary);
		--nui-list-item-simple-icon-left-selected-2: var(--nui-icon-primary);
		--nui-list-item-simple-icon-right-default: var(--nui-icon-secondary);
		--nui-list-item-simple-icon-right-hover: var(--nui-icon-primary);
		--nui-list-item-simple-icon-right-pressed: var(--nui-icon-primary);
		--nui-list-item-simple-icon-right-selected: var(--nui-icon-primary);
		--nui-list-item-simple-text-default: var(--nui-text-primary);
		--nui-list-item-simple-text-hover: var(--nui-text-primary);
		--nui-list-item-simple-text-pressed: var(--nui-text-primary);
		--nui-list-item-simple-text-selected: var(--nui-text-primary);
		--nui-loader-fill-inverted-light: var(--nui-fill-neutral-boldest);
		--nui-loader-fill-inverted-strong: var(--nui-fill-neutral-boldest);
		--nui-loader-fill-primary-light: var(--nui-fill-brand-secondary-neutral);
		--nui-loader-fill-primary-strong: var(--nui-fill-brand-primary-boldest);
		--nui-main-navigation-fill-default: var(--nui-surface-transparent);
		--nui-main-navigation-item-fill-active: var(--nui-fill-neutral-subtlest);
		--nui-main-navigation-item-fill-default: var(--nui-fill-transparent);
		--nui-main-navigation-item-fill-hover: var(--nui-fill-neutral-subtlest);
		--nui-main-navigation-item-icon-active: var(--nui-icon-brand);
		--nui-main-navigation-item-icon-default: var(--nui-icon-primary);
		--nui-main-navigation-item-icon-hover: var(--nui-icon-primary);
		--nui-main-navigation-item-text-active: var(--nui-text-brand);
		--nui-main-navigation-item-text-default: var(--nui-text-primary);
		--nui-main-navigation-item-text-hover: var(--nui-text-primary);
		--nui-menu-border-primary-default: var(--nui-border-neutral-secondary-bold);
		--nui-menu-border-primary-selected: var(--nui-border-brand-primary-boldest);
		--nui-menu-fill-primary-default: var(--nui-surface-neutral-subtler);
		--nui-menu-fill-secondary-default: var(--nui-surface-neutral-subtler);
		--nui-menu-input-border-primary-default: var(--nui-border-transparent);
		--nui-menu-input-border-primary-disabled: var(--nui-border-transparent);
		--nui-menu-input-border-primary-filled: var(--nui-border-transparent);
		--nui-menu-input-border-primary-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-menu-input-border-primary-selected: var(--nui-border-brand-primary-boldest);
		--nui-menu-input-fill-primary-default: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-disabled: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-filled: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-hover: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-fill-primary-selected: var(--nui-fill-neutral-subtlest);
		--nui-menu-input-icon-input-default: var(--nui-icon-secondary);
		--nui-menu-input-icon-input-disabled: var(--nui-icon-disabled);
		--nui-menu-input-icon-input-filled: var(--nui-icon-secondary);
		--nui-menu-input-icon-input-hover: var(--nui-icon-secondary);
		--nui-menu-input-icon-input-selected: var(--nui-icon-primary);
		--nui-menu-input-icon-label-default: var(--nui-icon-primary);
		--nui-menu-input-icon-label-disabled: var(--nui-icon-disabled);
		--nui-menu-input-icon-label-filled: var(--nui-icon-primary);
		--nui-menu-input-icon-label-hover: var(--nui-icon-primary);
		--nui-menu-input-icon-label-selected: var(--nui-icon-primary);
		--nui-menu-input-text-input-default: var(--nui-text-secondary);
		--nui-menu-input-text-input-disabled: var(--nui-text-disabled);
		--nui-menu-input-text-input-filled: var(--nui-text-primary);
		--nui-menu-input-text-input-hover: var(--nui-text-secondary);
		--nui-menu-input-text-input-selected: var(--nui-text-primary);
		--nui-menu-input-text-right-selected: var(--nui-text-primary);
		--nui-menu-item-fill-destructive-default: var(--nui-surface-neutral-subtler);
		--nui-menu-item-fill-destructive-hover: var(--nui-fill-system-danger-bold);
		--nui-menu-item-fill-destructive-pressed: var(--nui-fill-system-danger-bold);
		--nui-menu-item-fill-destructive-selected: var(--nui-fill-system-danger-bold);
		--nui-menu-item-fill-disabled: var(--nui-surface-neutral-subtler);
		--nui-menu-item-fill-primary-default: var(--nui-surface-neutral-subtler);
		--nui-menu-item-fill-primary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-menu-item-fill-primary-pressed: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-fill-primary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-fill-secondary-default: var(--nui-surface-neutral-subtler);
		--nui-menu-item-fill-secondary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-menu-item-fill-secondary-pressed: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-fill-secondary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-menu-item-icon-label-destructive-default: var(--nui-icon-error);
		--nui-menu-item-icon-label-destructive-hover: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-destructive-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-destructive-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-label-disabled: var(--nui-icon-disabled);
		--nui-menu-item-icon-label-primary-default: var(--nui-icon-primary);
		--nui-menu-item-icon-label-primary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-label-primary-pressed: var(--nui-icon-primary);
		--nui-menu-item-icon-label-primary-selected: var(--nui-icon-primary);
		--nui-menu-item-icon-label-secondary-default: var(--nui-icon-primary);
		--nui-menu-item-icon-label-secondary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-label-secondary-pressed: var(--nui-icon-primary);
		--nui-menu-item-icon-label-secondary-selected: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-destructive-default: var(--nui-icon-error);
		--nui-menu-item-icon-shortcut-destructive-hover: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-destructive-pressed: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-destructive-selected: var(--nui-icon-primary-inverted);
		--nui-menu-item-icon-shortcut-disabled: var(--nui-icon-disabled);
		--nui-menu-item-icon-shortcut-primary-default: var(--nui-icon-secondary);
		--nui-menu-item-icon-shortcut-primary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-primary-pressed: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-primary-selected: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-secondary-default: var(--nui-icon-secondary);
		--nui-menu-item-icon-shortcut-secondary-hover: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-secondary-pressed: var(--nui-icon-primary);
		--nui-menu-item-icon-shortcut-secondary-selected: var(--nui-icon-primary);
		--nui-menu-item-text-label-destructive-default: var(--nui-text-error);
		--nui-menu-item-text-label-destructive-hover: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-destructive-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-destructive-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-label-disabled: var(--nui-text-disabled);
		--nui-menu-item-text-label-primary-default: var(--nui-text-primary);
		--nui-menu-item-text-label-primary-hover: var(--nui-text-primary);
		--nui-menu-item-text-label-primary-pressed: var(--nui-text-primary);
		--nui-menu-item-text-label-primary-selected: var(--nui-text-primary);
		--nui-menu-item-text-label-secondary-default: var(--nui-text-primary);
		--nui-menu-item-text-label-secondary-hover: var(--nui-text-primary);
		--nui-menu-item-text-label-secondary-pressed: var(--nui-text-primary);
		--nui-menu-item-text-label-secondary-selected: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-destructive-default: var(--nui-text-error);
		--nui-menu-item-text-shortcut-destructive-hover: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-destructive-pressed: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-destructive-selected: var(--nui-text-primary-inverted);
		--nui-menu-item-text-shortcut-disabled: var(--nui-text-disabled);
		--nui-menu-item-text-shortcut-primary-default: var(--nui-text-secondary);
		--nui-menu-item-text-shortcut-primary-hover: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-primary-pressed: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-primary-selected: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-secondary-default: var(--nui-text-secondary);
		--nui-menu-item-text-shortcut-secondary-hover: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-secondary-pressed: var(--nui-text-primary);
		--nui-menu-item-text-shortcut-secondary-selected: var(--nui-text-primary);
		--nui-menu-search-fill-disabled: var(--nui-fill-neutral-subtlest);
		--nui-menu-search-fill-primary-default: var(--nui-surface-neutral-subtler);
		--nui-menu-search-fill-primary-filled: var(--nui-fill-brand-secondary-subtlest);
		--nui-menu-search-fill-primary-hover: var(--nui-fill-brand-secondary-subtle);
		--nui-menu-search-fill-primary-selected: var(--nui-fill-brand-secondary-subtlest);
		--nui-menu-search-icon-disabled: var(--nui-icon-disabled);
		--nui-menu-search-icon-primary-default: var(--nui-icon-secondary);
		--nui-menu-search-icon-primary-filled: var(--nui-icon-primary);
		--nui-menu-search-icon-primary-hover: var(--nui-icon-primary);
		--nui-menu-search-icon-primary-selected: var(--nui-icon-primary);
		--nui-menu-search-text-disabled: var(--nui-text-disabled);
		--nui-menu-search-text-primary-default: var(--nui-text-secondary);
		--nui-menu-search-text-primary-filled: var(--nui-text-primary);
		--nui-menu-search-text-primary-hover: var(--nui-text-primary);
		--nui-menu-search-text-primary-selected: var(--nui-text-secondary);
		--nui-paginator-fill-default: var(--nui-surface-neutral-subtlest);
		--nui-paginator-text-primary: var(--nui-text-secondary);
		--nui-pill-border-category-green: var(--nui-border-categories-green-bolder);
		--nui-pill-border-category-indigo: var(--nui-border-categories-indigo-bolder);
		--nui-pill-border-category-orange: var(--nui-border-categories-orange-bolder);
		--nui-pill-border-category-pink: var(--nui-border-categories-pink-bolder);
		--nui-pill-border-category-teal: var(--nui-border-categories-teal-bolder);
		--nui-pill-border-category-tomato: var(--nui-border-categories-tomato-bolder);
		--nui-pill-border-error: var(--nui-border-system-danger-boldest);
		--nui-pill-border-info: var(--nui-border-system-info-boldest);
		--nui-pill-border-success: var(--nui-border-system-success-boldest);
		--nui-pill-border-warning: var(--nui-border-system-warning-boldest);
		--nui-pill-fill-primary-category-green: var(--nui-fill-categories-green-bold);
		--nui-pill-fill-primary-category-indigo: var(--nui-fill-categories-indigo-bold);
		--nui-pill-fill-primary-category-orange: var(--nui-fill-categories-orange-bold);
		--nui-pill-fill-primary-category-pink: var(--nui-fill-categories-pink-bold);
		--nui-pill-fill-primary-category-teal: var(--nui-fill-categories-teal-bold);
		--nui-pill-fill-primary-category-tomato: var(--nui-fill-categories-tomato-bold);
		--nui-pill-fill-primary-error: var(--nui-fill-system-danger-bold);
		--nui-pill-fill-primary-info: var(--nui-fill-system-info-bold);
		--nui-pill-fill-primary-neutral: var(--nui-fill-neutral-bold);
		--nui-pill-fill-primary-success: var(--nui-fill-system-success-bold);
		--nui-pill-fill-primary-warning: var(--nui-fill-system-warning-bold);
		--nui-pill-fill-secondary-category-green: var(--nui-fill-categories-green-subtle);
		--nui-pill-fill-secondary-category-indigo: var(--nui-fill-categories-indigo-subtle);
		--nui-pill-fill-secondary-category-orange: var(--nui-fill-categories-orange-subtle);
		--nui-pill-fill-secondary-category-pink: var(--nui-fill-categories-pink-subtle);
		--nui-pill-fill-secondary-category-teal: var(--nui-fill-categories-teal-subtle);
		--nui-pill-fill-secondary-category-tomato: var(--nui-fill-categories-tomato-subtle);
		--nui-pill-fill-secondary-error: var(--nui-fill-system-danger-subtler);
		--nui-pill-fill-secondary-info: var(--nui-fill-system-info-subtler);
		--nui-pill-fill-secondary-neutral: var(--nui-fill-brand-secondary-subtler);
		--nui-pill-fill-secondary-success: var(--nui-fill-system-success-subtler);
		--nui-pill-fill-secondary-warning: var(--nui-fill-system-warning-subtler);
		--nui-pill-text-primary-error: var(--nui-text-primary);
		--nui-pill-text-primary-green: var(--nui-text-primary);
		--nui-pill-text-primary-indigo: var(--nui-text-primary);
		--nui-pill-text-primary-info: var(--nui-text-primary);
		--nui-pill-text-primary-neutral: var(--nui-text-primary);
		--nui-pill-text-primary-orange: var(--nui-text-primary);
		--nui-pill-text-primary-pink: var(--nui-text-primary);
		--nui-pill-text-primary-success: var(--nui-text-primary);
		--nui-pill-text-primary-teal: var(--nui-text-primary);
		--nui-pill-text-primary-tomato: var(--nui-text-primary);
		--nui-pill-text-primary-warning: var(--nui-text-primary);
		--nui-pill-text-secondary-error: var(--nui-text-error-boldest);
		--nui-pill-text-secondary-green: var(--nui-text-categories-green-boldest);
		--nui-pill-text-secondary-indigo: var(--nui-text-categories-indigo-boldest);
		--nui-pill-text-secondary-info: var(--nui-text-info-boldest);
		--nui-pill-text-secondary-neutral: var(--nui-text-primary);
		--nui-pill-text-secondary-orange: var(--nui-text-categories-orange-boldest);
		--nui-pill-text-secondary-pink: var(--nui-text-categories-pink-boldest);
		--nui-pill-text-secondary-success: var(--nui-text-success-boldest);
		--nui-pill-text-secondary-teal: var(--nui-text-categories-teal-boldest);
		--nui-pill-text-secondary-tomato: var(--nui-text-categories-tomato-boldest);
		--nui-pill-text-secondary-warning: var(--nui-text-warning-boldest);
		--nui-progress-bar-bar-default: var(--nui-fill-brand-primary-boldest);
		--nui-progress-bar-bar-success: var(--nui-fill-system-success-bold);
		--nui-progress-bar-fill-default: var(--nui-fill-neutral-subtle);
		--nui-range-fill-disabled: var(--nui-fill-neutral-subtle);
		--nui-range-fill-enabled: var(--nui-fill-neutral-subtle);
		--nui-range-fill-hover: var(--nui-fill-neutral-bold);
		--nui-range-fill-value: var(--nui-fill-brand-primary-boldest);
		--nui-range-fill-value-disabled: var(--nui-fill-neutral-bold);
		--nui-range-handle-border-default: var(--nui-border-neutral-secondary-bold);
		--nui-range-handle-border-disabled: var(--nui-border-neutral-subtle);
		--nui-range-handle-fill-default: var(--nui-fill-neutral-subtlest);
		--nui-range-handle-fill-disabled: var(--nui-fill-neutral-subtlest);
		--nui-scroll-fill-thumb-primary-disabled: var(--nui-fill-disabled);
		--nui-scroll-fill-thumb-primary-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-scroll-fill-thumb-primary-hover: var(--nui-fill-neutral-bold);
		--nui-scroll-fill-thumb-primary-pressed: var(--nui-fill-neutral-bold);
		--nui-scroll-fill-track-primary-disabled: var(--nui-fill-neutral-subtle);
		--nui-scroll-fill-track-primary-enabled: var(--nui-surface-transparent);
		--nui-scroll-fill-track-primary-hover: var(--nui-surface-transparent);
		--nui-scroll-fill-track-primary-pressed: var(--nui-surface-transparent);
		--nui-shadows-bold: var(--core-neutral-solid-1);
		--nui-shadows-subtle: var(--core-neutral-solid-1);
		--nui-shadows-subtler: var(--core-neutral-solid-2);
		--nui-shadows-subtlest: var(--core-neutral-solid-2);
		--nui-skeleton-fill-default: var(--nui-fill-brand-secondary-subtlest);
		--nui-slider-fill: var(--nui-fill-neutral-bold);
		--nui-slider-fill-active: var(--nui-fill-brand-primary-boldest);
		--nui-snackbar-border-error: var(--nui-border-system-danger-boldest);
		--nui-snackbar-border-info: var(--nui-border-system-info-boldest);
		--nui-snackbar-border-neutral: var(--nui-border-neutral-bolder);
		--nui-snackbar-border-success: var(--nui-border-system-success-boldest);
		--nui-snackbar-border-warning: var(--nui-border-system-warning-boldest);
		--nui-snackbar-fill-error: var(--nui-fill-system-danger-subtler);
		--nui-snackbar-fill-info: var(--nui-fill-system-info-subtler);
		--nui-snackbar-fill-neutral: var(--nui-fill-brand-secondary-subtler);
		--nui-snackbar-fill-success: var(--nui-fill-system-success-subtler);
		--nui-snackbar-fill-warning: var(--nui-fill-system-warning-subtler);
		--nui-snackbar-icon-error: var(--nui-icon-error-boldest);
		--nui-snackbar-icon-info: var(--nui-icon-info-boldest);
		--nui-snackbar-icon-neutral: var(--nui-icon-primary);
		--nui-snackbar-icon-success: var(--nui-icon-success-boldest);
		--nui-snackbar-icon-warning: var(--nui-icon-warning-bold);
		--nui-snackbar-text-error: var(--nui-text-error-boldest);
		--nui-snackbar-text-info: var(--nui-text-info-boldest);
		--nui-snackbar-text-neutral: var(--nui-text-primary);
		--nui-snackbar-text-success: var(--nui-text-success-boldest);
		--nui-snackbar-text-warning: var(--nui-text-warning-boldest);
		--nui-stepper-item-icon-border-active: var(--nui-border-brand-primary-boldest);
		--nui-stepper-item-icon-fill-active: var(--nui-fill-brand-primary-boldest);
		--nui-stepper-item-icon-fill-done: var(--nui-fill-neutral-boldest);
		--nui-stepper-item-icon-fill-waiting: var(--nui-fill-transparent);
		--nui-stepper-item-icon-icon-done: var(--nui-icon-primary-inverted);
		--nui-stepper-item-icon-text-active: var(--nui-text-primary);
		--nui-stepper-item-icon-text-done: var(--nui-text-primary-inverted);
		--nui-stepper-item-icon-text-waiting: var(--nui-text-brand);
		--nui-surface-gradient-bottom: var(--core-brand-metal-3);
		--nui-surface-gradient-top: var(--core-brand-metal-2);
		--nui-surface-neutral-subtler: var(--core-neutral-solid-3);
		--nui-surface-neutral-subtlest: var(--core-neutral-solid-2);
		--nui-surface-overlay: var(--core-neutral-alpha-7);
		--nui-surface-transparent: rgba(255, 255, 255, 0%);
		--nui-table-fill-default: var(--nui-surface-neutral-subtler);
		--nui-table-fill-even: var(--nui-surface-neutral-subtler);
		--nui-table-fill-hover: var(--nui-fill-brand-primary-subtlest);
		--nui-table-fill-odd: var(--nui-fill-brand-secondary-subtle);
		--nui-table-fill-pressed: var(--nui-fill-brand-secondary-neutral);
		--nui-table-fill-selected: var(--nui-fill-brand-primary-subtlest);
		--nui-tabs-border-primary: var(--nui-border-transparent);
		--nui-tabs-border-secondary: var(--nui-border-neutral-subtle);
		--nui-tabs-border-underlined: var(--nui-border-neutral-secondary-bold);
		--nui-tabs-border-underlined-enabled: var(--nui-border-neutral-secondary-bold);
		--nui-tabs-border-underlined-hover: var(--nui-border-brand-primary-boldest-hover);
		--nui-tabs-border-underlined-selected: var(--nui-border-brand-primary-boldest);
		--nui-tabs-fill: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary-enabled: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary-hover: var(--nui-fill-brand-secondary-subtler);
		--nui-tabs-fill-primary-selected: var(--nui-fill-brand-primary-boldest);
		--nui-tabs-fill-secondary: var(--nui-fill-neutral-subtlest);
		--nui-tabs-fill-secondary-enabled: var(--nui-fill-transparent);
		--nui-tabs-fill-secondary-hover: var(--nui-fill-transparent);
		--nui-tabs-fill-secondary-selected: var(--nui-fill-brand-secondary-subtle);
		--nui-tabs-fill-underlined-enabled: var(--nui-fill-transparent);
		--nui-tabs-fill-underlined-hover: var(--nui-fill-transparent);
		--nui-tabs-fill-underlined-selected: var(--nui-fill-transparent);
		--nui-tabs-icon-primary-enabled: var(--nui-icon-primary);
		--nui-tabs-icon-primary-hover: var(--nui-icon-brand);
		--nui-tabs-icon-primary-selected: var(--nui-icon-primary);
		--nui-tabs-icon-secondary-enabled: var(--nui-icon-primary);
		--nui-tabs-icon-secondary-hover: var(--nui-icon-brand);
		--nui-tabs-icon-secondary-selected: var(--nui-icon-brand);
		--nui-tabs-icon-underlined-enabled: var(--nui-icon-secondary);
		--nui-tabs-icon-underlined-hover: var(--nui-icon-primary);
		--nui-tabs-icon-underlined-selected: var(--nui-icon-primary);
		--nui-tabs-text-primary-enabled: var(--nui-text-primary);
		--nui-tabs-text-primary-hover: var(--nui-text-brand);
		--nui-tabs-text-primary-selected: var(--nui-text-primary);
		--nui-tabs-text-secondary-enabled: var(--nui-text-primary);
		--nui-tabs-text-secondary-hover: var(--nui-text-brand);
		--nui-tabs-text-secondary-selected: var(--nui-text-brand);
		--nui-tabs-text-underlined-enabled: var(--nui-text-secondary);
		--nui-tabs-text-underlined-hover: var(--nui-text-primary);
		--nui-tabs-text-underlined-selected: var(--nui-text-primary);
		--nui-text-brand: var(--core-brand-solid-11);
		--nui-text-categories-green-bold: var(--core-categories-green-solid-9);
		--nui-text-categories-green-boldest: var(--core-categories-green-solid-12);
		--nui-text-categories-indigo-bold: var(--core-categories-indigo-solid-9);
		--nui-text-categories-indigo-boldest: var(--core-categories-indigo-solid-12);
		--nui-text-categories-orange-bold: var(--core-categories-orange-solid-9);
		--nui-text-categories-orange-boldest: var(--core-categories-orange-solid-12);
		--nui-text-categories-pink-bold: var(--core-categories-pink-solid-9);
		--nui-text-categories-pink-boldest: var(--core-categories-pink-solid-12);
		--nui-text-categories-teal-bold: var(--core-categories-teal-solid-9);
		--nui-text-categories-teal-boldest: var(--core-categories-teal-solid-12);
		--nui-text-categories-tomato-bold: var(--core-categories-tomato-solid-9);
		--nui-text-categories-tomato-boldest: var(--core-categories-tomato-solid-12);
		--nui-text-disabled: var(--core-neutral-alpha-9);
		--nui-text-error: var(--core-system-error-solid-11);
		--nui-text-error-boldest: var(--core-system-error-solid-12);
		--nui-text-info: var(--core-system-info-solid-10);
		--nui-text-info-boldest: var(--core-system-info-solid-12);
		--nui-text-link: var(--core-brand-solid-11);
		--nui-text-link-hover: var(--core-brand-solid-12);
		--nui-text-link-pressed: var(--core-brand-solid-10);
		--nui-text-negative-number: var(--core-system-error-solid-10);
		--nui-text-positive-number: var(--core-system-success-solid-10);
		--nui-text-primary: var(--core-neutral-solid-12);
		--nui-text-primary-inverted: var(--core-bw-white);
		--nui-text-secondary: var(--core-neutral-solid-11);
		--nui-text-success: var(--core-system-success-solid-10);
		--nui-text-success-boldest: var(--core-system-success-solid-12);
		--nui-text-warning: var(--core-system-warning-solid-10);
		--nui-text-warning-boldest: var(--core-system-warning-solid-12);
		--nui-toggle-fill-default: var(--nui-fill-neutral-bold);
		--nui-toggle-fill-disabled: var(--nui-fill-disabled);
		--nui-toggle-fill-handle: var(--nui-fill-neutral-boldest);
		--nui-toggle-fill-selected: var(--nui-fill-brand-primary-boldest);
		--nui-tooltip-fill-default: var(--nui-fill-brand-secondary-subtle);
		--nui-tooltip-text-default: var(--nui-text-primary);
		--nui-waiting: var(--nui-fill-transparent);
	}
	[data-uisize="xs"], &[data-uisize="xs"] {
		--nui-accordion-border: 0px;
		--nui-accordion-radius: 0px;
		--nui-accordion-rich-radius: 0px;
		--nui-accordion-rich-space-gap-actions: 0px;
		--nui-accordion-rich-space-gap-left: 0px;
		--nui-accordion-rich-space-gap-left-text: 0px;
		--nui-accordion-rich-space-padding-bottom-head: 0px;
		--nui-accordion-rich-space-padding-horizontal: 0px;
		--nui-accordion-rich-space-padding-top-body: 0px;
		--nui-accordion-rich-space-padding-vertical: 0px;
		--nui-accordion-simple-space-gap: var(--nui-space-050);
		--nui-accordion-simple-space-gap-actions: var(--nui-space-100);
		--nui-accordion-simple-space-gap-label: var(--nui-space-100);
		--nui-accordion-simple-space-padding-horizontal: var(--nui-space-100);
		--nui-accordion-simple-space-padding-vertical: var(--nui-space-100);
		--nui-avatar-height: 0px;
		--nui-avatar-radius-default: 0px;
		--nui-avatar-radius-full: 0px;
		--nui-avatar-width: 0px;
		--nui-banner-radius: 0px;
		--nui-banner-space-gap: 0px;
		--nui-banner-space-padding-horizontal: 0px;
		--nui-banner-space-padding-vertical: 0px;
		--nui-breadcrumbs-item-radius-crumb: 0px;
		--nui-breadcrumbs-item-space-gap: 0px;
		--nui-breadcrumbs-item-space-padding-horizontal-crumb: 0px;
		--nui-breadcrumbs-item-space-padding-vertical-crumb: 0px;
		--nui-breadcrumbs-space-gap: 0px;
		--nui-button-group-border: var(--nui-border-width-tiny);
		--nui-button-group-item-border: var(--nui-border-width-tiny);
		--nui-button-group-item-height: var(--nui-height-tiny);
		--nui-button-group-item-radius: var(--nui-border-radius-tiny);
		--nui-button-group-item-space-padding-horizontal-icon: var(--core-numeric-100);
		--nui-button-group-item-space-padding-horizontal-text: var(--core-numeric-200);
		--nui-button-group-radius: var(--nui-border-radius-tiny);
		--nui-button-group-space-gap: var(--core-numeric-0125);
		--nui-button-height: var(--nui-height-tiny);
		--nui-button-radius: var(--nui-border-radius-small);
		--nui-button-space-gap: var(--nui-space-200);
		--nui-button-space-padding-focused: var(--nui-border-width-small);
		--nui-button-space-padding-horizontal: var(--nui-space-200);
		--nui-calendar-month-border: 0px;
		--nui-calendar-month-date-border: 0px;
		--nui-calendar-month-date-height: 0px;
		--nui-calendar-month-date-space-gap: 0px;
		--nui-calendar-month-date-space-padding-horizontal: 0px;
		--nui-calendar-month-date-space-padding-vertical: 0px;
		--nui-calendar-month-date-width: 0px;
		--nui-calendar-month-event-radius: 0px;
		--nui-calendar-month-event-space-gap-indicators: 0px;
		--nui-calendar-month-event-space-padding-horizontal: 0px;
		--nui-calendar-month-event-space-padding-horizontal-indicators: 0px;
		--nui-calendar-month-event-space-padding-vertical: 0px;
		--nui-calendar-month-radius: 0px;
		--nui-calendar-month-space-padding-right-week-number: 0px;
		--nui-calendar-schedule-item-space-gap: 0px;
		--nui-calendar-schedule-item-space-padding-horizontal-icon: 0px;
		--nui-calendar-schedule-item-space-padding-left: 0px;
		--nui-calendar-schedule-item-space-padding-vertical: 0px;
		--nui-card-border: var(--nui-border-width-small);
		--nui-card-radius: var(--nui-border-radius-small);
		--nui-card-space-padding-horizontal: var(--nui-space-200);
		--nui-card-space-padding-vertical: var(--nui-space-200);
		--nui-chat-border: 0px;
		--nui-chat-emoji-border: 0px;
		--nui-chat-emoji-radius: 0px;
		--nui-chat-emoji-space-gap: 0px;
		--nui-chat-emoji-space-gap-emojis: 0px;
		--nui-chat-emoji-space-padding: 0px;
		--nui-chat-head-space-gap-left: 0px;
		--nui-chat-head-space-padding-right: 0px;
		--nui-chat-input-space-gap-active: 0px;
		--nui-chat-input-space-gap-input: 0px;
		--nui-chat-input-space-gap-reply: 0px;
		--nui-chat-input-space-gap-send-actions: 0px;
		--nui-chat-input-space-padding-horizontal-new-comment: 0px;
		--nui-chat-input-space-padding-vertical-new-comment: 0px;
		--nui-chat-message-space-gap: 0px;
		--nui-chat-message-space-gap-actions: 0px;
		--nui-chat-message-space-gap-message: 0px;
		--nui-chat-message-space-gap-row: 0px;
		--nui-chat-message-space-gap-sender: 0px;
		--nui-chat-radius: 0px;
		--nui-chat-thread-border: 0px;
		--nui-chat-thread-radius: 0px;
		--nui-chat-thread-space-gap: 0px;
		--nui-chat-thread-space-padding: 0px;
		--nui-chat-width: 0px;
		--nui-checkbox-list-space-gap: 0px;
		--nui-checkbox-space-gap: 0px;
		--nui-chips-border: 0px;
		--nui-chips-height: 0px;
		--nui-chips-radius: 0px;
		--nui-chips-space-padding-horizontal: 0px;
		--nui-chips-space-padding-vertical: 0px;
		--nui-dialog-border: var(--nui-border-width-small);
		--nui-dialog-radius: var(--nui-border-radius-medium);
		--nui-dialog-space-gap: var(--nui-space-400);
		--nui-dialog-space-gap-content: 0px;
		--nui-dialog-space-gap-main-actions: var(--nui-space-300);
		--nui-dialog-space-padding-horizontal: var(--nui-space-400);
		--nui-dialog-space-padding-vertical: var(--nui-space-400);
		--nui-dialog-width: 400px;
		--nui-divider-space-padding-vertical: 0px;
		--nui-forms-height: var(--nui-height-tiny);
		--nui-forms-help-space-padding-left: var(--nui-space-050);
		--nui-forms-help-space-padding-top: var(--nui-space-050);
		--nui-forms-label-space-padding-bottom: var(--nui-space-050);
		--nui-forms-label-space-padding-left: var(--nui-space-050);
		--nui-forms-radius: var(--nui-border-radius-small);
		--nui-forms-space-gap: var(--nui-space-050);
		--nui-forms-space-padding-horizontal: var(--nui-space-100);
		--nui-forms-space-padding-left-items: var(--nui-space-100);
		--nui-forms-space-secondary-gap: var(--nui-space-100);
		--nui-grid-item-header-space-gap: 0px;
		--nui-grid-item-radius: var(--nui-border-radius-small);
		--nui-grid-item-space-gap: var(--nui-space-100);
		--nui-grid-item-space-padding-horizontal: var(--nui-space-100);
		--nui-grid-item-space-padding-vertical: var(--nui-space-100);
		--nui-grid-list-space-gap: var(--nui-space-100);
		--nui-header-space-gap-actions: 0px;
		--nui-header-space-gap-breadcrumbs: 0px;
		--nui-header-space-gap-dropdown: 0px;
		--nui-header-space-gap-right: 0px;
		--nui-header-space-padding-horizontal: 0px;
		--nui-header-space-padding-vertical: 0px;
		--nui-heading-font-family: var(--core-font-family-primary);
		--nui-heading-font-size: var(--nui-font-size-500);
		--nui-heading-font-weight: var(--nui-font-weight-15000);
		--nui-heading-letter-spacing: var(--nui-font-letter-spacing-024);
		--nui-heading-line-height: var(--nui-font-line-height-700);
		--nui-icon-font-icon-size: var(--core-numeric-400);
		--nui-icon-height: var(--core-numeric-300);
		--nui-icon-width: var(--core-numeric-300);
		--nui-label-bold-font-family: var(--core-font-family-primary);
		--nui-label-bold-font-size: var(--nui-font-size-275);
		--nui-label-bold-font-weight: var(--nui-font-weight-12500);
		--nui-label-bold-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-label-bold-line-height: var(--nui-font-line-height-400);
		--nui-label-regular-font-family: var(--core-font-family-primary);
		--nui-label-regular-font-size: var(--nui-font-size-275);
		--nui-label-regular-font-weight: var(--nui-font-weight-10000);
		--nui-label-regular-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-label-regular-line-height: var(--nui-font-line-height-400);
		--nui-label-space-gap: var(--nui-space-100);
		--nui-list-item-border: 0px;
		--nui-list-item-radius: 0px;
		--nui-list-item-rich-space-gap: 0px;
		--nui-list-item-rich-space-gap-actions: 0px;
		--nui-list-item-rich-space-gap-left: 0px;
		--nui-list-item-rich-space-no-background-gap-left-text: 0px;
		--nui-list-item-rich-space-no-background-padding-horizontal: 0px;
		--nui-list-item-rich-space-no-background-padding-vertical: 0px;
		--nui-list-item-rich-space-with-background-gap-left-text: 0px;
		--nui-list-item-rich-space-with-background-padding-horizontal: 0px;
		--nui-list-item-rich-space-with-background-padding-vertical: 0px;
		--nui-list-item-simple-height: 0px;
		--nui-list-item-simple-radius: 0px;
		--nui-list-item-simple-space-gap: 0px;
		--nui-list-item-simple-space-padding-horizontal: 0px;
		--nui-list-item-simple-space-padding-vertical: 0px;
		--nui-main-navigation-item-height: 0px;
		--nui-main-navigation-item-radius: 0px;
		--nui-main-navigation-item-space-padding-horizontal-collapsed: 0px;
		--nui-main-navigation-item-space-padding-horizontal-expanded: 0px;
		--nui-main-navigation-item-space-padding-vertical-collapsed: 0px;
		--nui-main-navigation-item-space-padding-vertical-expanded: 0px;
		--nui-main-navigation-space-gap-top: 0px;
		--nui-main-navigation-space-padding-horizontal: 0px;
		--nui-main-navigation-space-padding-vertical: 0px;
		--nui-menu-item-input-radius: var(--nui-border-radius-tiny);
		--nui-menu-item-radius: var(--nui-space-000);
		--nui-menu-item-space-gap-option: var(--nui-space-100);
		--nui-menu-item-space-gap-shortcut: var(--nui-space-100);
		--nui-menu-item-space-label-padding-bottom: var(--nui-space-100);
		--nui-menu-item-space-label-padding-top: var(--nui-space-200);
		--nui-menu-item-space-padding-horizontal: var(--nui-space-200);
		--nui-menu-item-space-secondary-padding-horizontal: var(--nui-space-100);
		--nui-menu-min-width: var(--nui-min-width-medium);
		--nui-menu-radius: var(--nui-border-radius-small);
		--nui-menu-space-padding-horizontal: var(--nui-space-000);
		--nui-menu-space-padding-vertical: var(--nui-space-000);
		--nui-paginator-space-gap-page: 0px;
		--nui-paginator-space-gap-show: 0px;
		--nui-paginator-space-padding-horizontal: 0px;
		--nui-paginator-space-padding-vertical: 0px;
		--nui-pill-radius: var(--nui-border-radius-tiny);
		--nui-pill-space-padding-horizontal: var(--nui-space-050);
		--nui-pill-space-padding-vertical: var(--nui-space-000);
		--nui-progress-bar-item-height: 0px;
		--nui-progress-bar-item-radius: 0px;
		--nui-radio-button-list-space-gap: 0px;
		--nui-range-slider-handle-height: 12px;
		--nui-range-slider-handle-width: 12px;
		--nui-range-space-gap: var(--nui-space-200);
		--nui-range-space-padding-horizontal: var(--nui-space-200);
		--nui-range-space-padding-vertical: var(--nui-space-200);
		--nui-scroll-radius: 0px;
		--nui-scroll-space-padding-horizontal: 0px;
		--nui-scroll-space-padding-vertical: 0px;
		--nui-scroll-width: 0px;
		--nui-snackbar-border: 0px;
		--nui-snackbar-radius: 0px;
		--nui-snackbar-space-gap: 0px;
		--nui-snackbar-space-padding-horizontal: 0px;
		--nui-snackbar-space-padding-vertical: 0px;
		--nui-stepper-item-icon-border: 0px;
		--nui-stepper-item-icon-height: 0px;
		--nui-stepper-item-icon-radius: 0px;
		--nui-stepper-item-icon-width: 0px;
		--nui-stepper-item-space-gap: 0px;
		--nui-stepper-space-gap: 0px;
		--nui-table-item-gap: var(--nui-space-100);
		--nui-table-item-height: var(--nui-height-small);
		--nui-table-item-padding-horizontal: var(--nui-space-300);
		--nui-table-row-height: var(--nui-height-small);
		--nui-table-row-space-gap: var(--nui-space-050);
		--nui-table-space-padding-horizontal: var(--nui-space-000);
		--nui-table-space-padding-vertical: var(--nui-space-100);
		--nui-tabs-height: var(--nui-height-tiny);
		--nui-tabs-item-height: 0px;
		--nui-tabs-item-radius: 0px;
		--nui-tabs-item-space-padding-horizontal-icon: 0px;
		--nui-tabs-item-space-padding-horizontal-text: 0px;
		--nui-tabs-radius: var(--nui-border-radius-tiny);
		--nui-tabs-space-padding-horizontal: 0px;
		--nui-toggle-item-height: 0px;
		--nui-toggle-item-radius: 0px;
		--nui-toggle-item-space-padding-horizontal: 0px;
		--nui-toggle-item-space-padding-vertical: 0px;
		--nui-toggle-item-width: 0px;
		--nui-toggle-space-gap: 0px;
		--nui-toggle-space-width: 0px;
		--nui-tooltip-radius: 0px;
		--nui-tooltip-space-padding-horizontal: 0px;
		--nui-tooltip-space-padding-vertical: 0px;
	}
	[data-uisize="sm"], &[data-uisize="sm"] {
		--nui-accordion-border: 0px;
		--nui-accordion-radius: 0px;
		--nui-accordion-rich-radius: 0px;
		--nui-accordion-rich-space-gap-actions: 0px;
		--nui-accordion-rich-space-gap-left: 0px;
		--nui-accordion-rich-space-gap-left-text: 0px;
		--nui-accordion-rich-space-padding-bottom-head: 0px;
		--nui-accordion-rich-space-padding-horizontal: 0px;
		--nui-accordion-rich-space-padding-top-body: 0px;
		--nui-accordion-rich-space-padding-vertical: 0px;
		--nui-accordion-simple-space-gap: var(--nui-space-100);
		--nui-accordion-simple-space-gap-actions: var(--nui-space-200);
		--nui-accordion-simple-space-gap-label: var(--nui-space-050);
		--nui-accordion-simple-space-padding-horizontal: var(--nui-space-300);
		--nui-accordion-simple-space-padding-vertical: var(--nui-space-300);
		--nui-avatar-height: var(--nui-height-small);
		--nui-avatar-radius-default: var(--nui-border-radius-small);
		--nui-avatar-radius-full: var(--nui-border-radius-full);
		--nui-avatar-width: var(--nui-height-small);
		--nui-banner-radius: 0px;
		--nui-banner-space-gap: 0px;
		--nui-banner-space-padding-horizontal: 0px;
		--nui-banner-space-padding-vertical: 0px;
		--nui-body-bold-font-family: var(--core-font-family-primary);
		--nui-body-bold-font-size: var(--nui-font-size-275);
		--nui-body-bold-font-weight: var(--nui-font-weight-12500);
		--nui-body-bold-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-body-bold-line-height: var(--nui-font-line-height-400);
		--nui-body-regular-font-family: var(--core-font-family-primary);
		--nui-body-regular-font-size: var(--nui-font-size-275);
		--nui-body-regular-font-weight: var(--nui-font-weight-10000);
		--nui-body-regular-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-body-regular-line-height: var(--nui-font-line-height-400);
		--nui-breadcrumbs-item-radius-crumb: var(--nui-border-radius-full);
		--nui-breadcrumbs-item-space-gap: var(--nui-space-100);
		--nui-breadcrumbs-item-space-padding-horizontal-crumb: var(--nui-space-200);
		--nui-breadcrumbs-item-space-padding-vertical-crumb: var(--nui-space-100);
		--nui-breadcrumbs-space-gap: var(--nui-space-100);
		--nui-button-group-border: var(--nui-border-width-small);
		--nui-button-group-item-border: var(--nui-border-width-small);
		--nui-button-group-item-height: var(--nui-height-small);
		--nui-button-group-item-radius: var(--nui-border-radius-small);
		--nui-button-group-item-space-padding-horizontal-icon: var(--core-numeric-200);
		--nui-button-group-item-space-padding-horizontal-text: var(--core-numeric-400);
		--nui-button-group-radius: var(--nui-border-radius-small);
		--nui-button-group-space-gap: var(--core-numeric-025);
		--nui-button-height: var(--nui-height-small);
		--nui-button-radius: var(--nui-border-radius-small);
		--nui-button-space-gap: var(--nui-space-100);
		--nui-button-space-padding-focused: var(--nui-border-width-small);
		--nui-button-space-padding-horizontal: var(--nui-space-200);
		--nui-calendar-month-border: 0px;
		--nui-calendar-month-date-border: 0px;
		--nui-calendar-month-date-height: 0px;
		--nui-calendar-month-date-space-gap: 0px;
		--nui-calendar-month-date-space-padding-horizontal: 0px;
		--nui-calendar-month-date-space-padding-vertical: 0px;
		--nui-calendar-month-date-width: 0px;
		--nui-calendar-month-event-radius: 0px;
		--nui-calendar-month-event-space-gap-indicators: 0px;
		--nui-calendar-month-event-space-padding-horizontal: 0px;
		--nui-calendar-month-event-space-padding-horizontal-indicators: 0px;
		--nui-calendar-month-event-space-padding-vertical: 0px;
		--nui-calendar-month-radius: 0px;
		--nui-calendar-month-space-padding-right-week-number: 0px;
		--nui-calendar-schedule-item-space-gap: 0px;
		--nui-calendar-schedule-item-space-padding-horizontal-icon: 0px;
		--nui-calendar-schedule-item-space-padding-left: 0px;
		--nui-calendar-schedule-item-space-padding-vertical: 0px;
		--nui-card-border: var(--nui-border-width-small);
		--nui-card-radius: var(--nui-border-radius-medium);
		--nui-card-space-padding-horizontal: var(--nui-space-300);
		--nui-card-space-padding-vertical: var(--nui-space-300);
		--nui-chat-border: 0px;
		--nui-chat-emoji-border: 0px;
		--nui-chat-emoji-radius: 0px;
		--nui-chat-emoji-space-gap: 0px;
		--nui-chat-emoji-space-gap-emojis: 0px;
		--nui-chat-emoji-space-padding: 0px;
		--nui-chat-head-space-gap-left: 0px;
		--nui-chat-head-space-padding-right: 0px;
		--nui-chat-input-space-gap-active: 0px;
		--nui-chat-input-space-gap-input: 0px;
		--nui-chat-input-space-gap-reply: 0px;
		--nui-chat-input-space-gap-send-actions: 0px;
		--nui-chat-input-space-padding-horizontal-new-comment: 0px;
		--nui-chat-input-space-padding-vertical-new-comment: 0px;
		--nui-chat-message-space-gap: 0px;
		--nui-chat-message-space-gap-actions: 0px;
		--nui-chat-message-space-gap-message: 0px;
		--nui-chat-message-space-gap-row: 0px;
		--nui-chat-message-space-gap-sender: 0px;
		--nui-chat-radius: 0px;
		--nui-chat-thread-border: 0px;
		--nui-chat-thread-radius: 0px;
		--nui-chat-thread-space-gap: 0px;
		--nui-chat-thread-space-padding: 0px;
		--nui-chat-width: 0px;
		--nui-checkbox-list-space-gap: var(--nui-space-200);
		--nui-checkbox-space-gap: 0px;
		--nui-chips-border: var(--nui-border-width-tiny);
		--nui-chips-height: 0px;
		--nui-chips-radius: var(--nui-border-radius-tiny);
		--nui-chips-space-padding-horizontal: var(--core-numeric-100);
		--nui-chips-space-padding-vertical: var(--nui-space-000);
		--nui-dialog-border: var(--nui-border-width-small);
		--nui-dialog-radius: var(--nui-border-radius-medium);
		--nui-dialog-space-gap: var(--nui-space-600);
		--nui-dialog-space-gap-content: var(--nui-space-100);
		--nui-dialog-space-gap-main-actions: var(--nui-space-400);
		--nui-dialog-space-padding-horizontal: var(--nui-space-600);
		--nui-dialog-space-padding-vertical: var(--nui-space-600);
		--nui-dialog-width: 500px;
		--nui-divider-space-padding-vertical: var(--nui-space-050);
		--nui-forms-height: var(--nui-height-small);
		--nui-forms-help-space-padding-left: var(--nui-space-100);
		--nui-forms-help-space-padding-top: var(--nui-space-100);
		--nui-forms-label-space-padding-bottom: var(--nui-space-100);
		--nui-forms-label-space-padding-left: var(--nui-space-050);
		--nui-forms-radius: var(--nui-border-radius-small);
		--nui-forms-space-gap: var(--nui-space-200);
		--nui-forms-space-padding-horizontal: var(--nui-space-200);
		--nui-forms-space-padding-left-items: var(--nui-space-100);
		--nui-forms-space-secondary-gap: var(--nui-space-100);
		--nui-grid-item-header-space-gap: var(--nui-space-100);
		--nui-grid-item-radius: var(--nui-border-radius-small);
		--nui-grid-item-space-gap: var(--nui-space-100);
		--nui-grid-item-space-padding-horizontal: var(--nui-space-100);
		--nui-grid-item-space-padding-vertical: var(--nui-space-100);
		--nui-grid-list-space-gap: var(--nui-space-200);
		--nui-header-space-gap-actions: 0px;
		--nui-header-space-gap-breadcrumbs: 0px;
		--nui-header-space-gap-dropdown: 0px;
		--nui-header-space-gap-right: 0px;
		--nui-header-space-padding-horizontal: 0px;
		--nui-header-space-padding-vertical: 0px;
		--nui-heading-font-family: var(--core-font-family-primary);
		--nui-heading-font-size: var(--nui-font-size-600);
		--nui-heading-font-weight: var(--nui-font-weight-15000);
		--nui-heading-letter-spacing: var(--nui-font-letter-spacing-028);
		--nui-heading-line-height: var(--nui-font-line-height-800);
		--nui-icon-font-icon-size: var(--core-numeric-500);
		--nui-icon-height: var(--core-numeric-500);
		--nui-icon-width: var(--core-numeric-500);
		--nui-label-bold-font-family: var(--core-font-family-primary);
		--nui-label-bold-font-size: var(--nui-font-size-275);
		--nui-label-bold-font-weight: var(--nui-font-weight-12500);
		--nui-label-bold-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-label-bold-line-height: var(--nui-font-line-height-400);
		--nui-label-regular-font-family: var(--core-font-family-primary);
		--nui-label-regular-font-size: var(--nui-font-size-275);
		--nui-label-regular-font-weight: var(--nui-font-weight-10000);
		--nui-label-regular-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-label-regular-line-height: var(--nui-font-line-height-400);
		--nui-label-space-gap: var(--nui-space-100);
		--nui-list-item-border: 0px;
		--nui-list-item-radius: 0px;
		--nui-list-item-rich-space-gap: 0px;
		--nui-list-item-rich-space-gap-actions: 0px;
		--nui-list-item-rich-space-gap-left: 0px;
		--nui-list-item-rich-space-no-background-gap-left-text: 0px;
		--nui-list-item-rich-space-no-background-padding-horizontal: 0px;
		--nui-list-item-rich-space-no-background-padding-vertical: 0px;
		--nui-list-item-rich-space-with-background-gap-left-text: 0px;
		--nui-list-item-rich-space-with-background-padding-horizontal: 0px;
		--nui-list-item-rich-space-with-background-padding-vertical: 0px;
		--nui-list-item-simple-height: var(--nui-height-tiny);
		--nui-list-item-simple-radius: var(--nui-border-radius-tiny);
		--nui-list-item-simple-space-gap: var(--nui-space-100);
		--nui-list-item-simple-space-padding-horizontal: var(--nui-space-200);
		--nui-list-item-simple-space-padding-vertical: var(--nui-space-100);
		--nui-main-navigation-item-height: 0px;
		--nui-main-navigation-item-radius: 0px;
		--nui-main-navigation-item-space-padding-horizontal-collapsed: 0px;
		--nui-main-navigation-item-space-padding-horizontal-expanded: 0px;
		--nui-main-navigation-item-space-padding-vertical-collapsed: 0px;
		--nui-main-navigation-item-space-padding-vertical-expanded: 0px;
		--nui-main-navigation-space-gap-top: 0px;
		--nui-main-navigation-space-padding-horizontal: 0px;
		--nui-main-navigation-space-padding-vertical: 0px;
		--nui-menu-item-input-radius: var(--nui-border-radius-tiny);
		--nui-menu-item-radius: var(--nui-space-000);
		--nui-menu-item-space-gap-option: var(--nui-space-100);
		--nui-menu-item-space-gap-shortcut: var(--nui-space-100);
		--nui-menu-item-space-label-padding-bottom: var(--nui-space-100);
		--nui-menu-item-space-label-padding-top: var(--nui-space-200);
		--nui-menu-item-space-padding-horizontal: var(--nui-space-200);
		--nui-menu-item-space-secondary-padding-horizontal: var(--nui-space-200);
		--nui-menu-min-width: var(--nui-min-width-large);
		--nui-menu-radius: var(--nui-border-radius-medium);
		--nui-menu-space-padding-horizontal: var(--nui-space-000);
		--nui-menu-space-padding-vertical: var(--nui-space-000);
		--nui-paginator-space-gap-page: 0px;
		--nui-paginator-space-gap-show: 0px;
		--nui-paginator-space-padding-horizontal: 0px;
		--nui-paginator-space-padding-vertical: 0px;
		--nui-pill-radius: var(--nui-border-radius-tiny);
		--nui-pill-space-padding-horizontal: var(--nui-space-100);
		--nui-pill-space-padding-vertical: var(--nui-space-000);
		--nui-progress-bar-item-height: 0px;
		--nui-progress-bar-item-radius: 0px;
		--nui-radio-button-list-space-gap: var(--nui-space-200);
		--nui-range-slider-handle-height: 16px;
		--nui-range-slider-handle-width: 16px;
		--nui-range-space-gap: var(--nui-space-300);
		--nui-range-space-padding-horizontal: var(--nui-space-200);
		--nui-range-space-padding-vertical: var(--nui-space-200);
		--nui-scroll-radius: var(--nui-border-radius-full);
		--nui-scroll-space-padding-horizontal: var(--nui-space-050);
		--nui-scroll-space-padding-vertical: var(--nui-space-050);
		--nui-scroll-width: 6px;
		--nui-snackbar-border: 0px;
		--nui-snackbar-radius: 0px;
		--nui-snackbar-space-gap: 0px;
		--nui-snackbar-space-padding-horizontal: 0px;
		--nui-snackbar-space-padding-vertical: 0px;
		--nui-stepper-item-icon-border: var(--nui-border-width-small);
		--nui-stepper-item-icon-height: var(--nui-height-tiny);
		--nui-stepper-item-icon-radius: var(--nui-border-radius-full);
		--nui-stepper-item-icon-width: var(--nui-height-tiny);
		--nui-stepper-item-space-gap: var(--nui-space-200);
		--nui-stepper-space-gap: var(--nui-space-200);
		--nui-table-item-gap: var(--nui-space-100);
		--nui-table-item-height: var(--nui-height-medium);
		--nui-table-item-padding-horizontal: var(--nui-space-300);
		--nui-table-row-height: var(--nui-height-medium);
		--nui-table-row-space-gap: var(--nui-space-050);
		--nui-table-space-padding-horizontal: var(--nui-space-000);
		--nui-table-space-padding-vertical: var(--nui-space-000);
		--nui-tabs-height: var(--nui-height-small);
		--nui-tabs-item-height: var(--nui-height-small);
		--nui-tabs-item-radius: var(--nui-border-radius-small);
		--nui-tabs-item-space-padding-horizontal-icon: var(--nui-space-200);
		--nui-tabs-item-space-padding-horizontal-text: var(--nui-space-300);
		--nui-tabs-radius: var(--nui-border-radius-small);
		--nui-tabs-space-padding-horizontal: var(--nui-space-300);
		--nui-title-font-family: var(--core-font-family-primary);
		--nui-title-font-size: var(--nui-font-size-275);
		--nui-title-font-weight: var(--nui-font-weight-15000);
		--nui-title-letter-spacing: var(--nui-font-letter-spacing-014);
		--nui-title-line-height: var(--nui-font-line-height-400);
		--nui-toggle-item-height: var(--nui-space-300);
		--nui-toggle-item-radius: var(--nui-border-radius-full);
		--nui-toggle-item-space-padding-horizontal: var(--nui-space-050);
		--nui-toggle-item-space-padding-vertical: var(--nui-space-050);
		--nui-toggle-item-width: var(--nui-space-300);
		--nui-toggle-space-gap: var(--nui-space-200);
		--nui-toggle-space-width: var(--nui-space-800);
		--nui-tooltip-radius: 0px;
		--nui-tooltip-space-padding-horizontal: 0px;
		--nui-tooltip-space-padding-vertical: 0px;
	}
	[data-uisize="md"], &[data-uisize="md"],
	&:not([data-uisize="sm"]):not([data-uisize="xs"]):not([data-uisize="lg"]) {
		--nui-accordion-border: var(--nui-border-width-small);
		--nui-accordion-radius: var(--nui-border-radius-small);
		--nui-accordion-rich-radius: var(--nui-border-radius-medium);
		--nui-accordion-rich-space-gap-actions: var(--nui-space-300);
		--nui-accordion-rich-space-gap-left: var(--nui-space-200);
		--nui-accordion-rich-space-gap-left-text: var(--nui-space-050);
		--nui-accordion-rich-space-padding-bottom-head: var(--nui-space-400);
		--nui-accordion-rich-space-padding-horizontal: var(--nui-space-400);
		--nui-accordion-rich-space-padding-top-body: var(--nui-space-400);
		--nui-accordion-rich-space-padding-vertical: var(--nui-space-400);
		--nui-accordion-simple-space-gap: var(--nui-space-200);
		--nui-accordion-simple-space-gap-actions: var(--nui-space-200);
		--nui-accordion-simple-space-gap-label: var(--nui-space-050);
		--nui-accordion-simple-space-padding-horizontal: var(--nui-space-300);
		--nui-accordion-simple-space-padding-vertical: var(--nui-space-300);
		--nui-avatar-height: var(--nui-height-medium);
		--nui-avatar-radius-default: var(--nui-border-radius-medium);
		--nui-avatar-radius-full: var(--nui-border-radius-full);
		--nui-avatar-width: var(--nui-height-medium);
		--nui-banner-radius: var(--nui-border-radius-small);
		--nui-banner-space-gap: var(--nui-space-200);
		--nui-banner-space-padding-horizontal: var(--nui-space-200);
		--nui-banner-space-padding-vertical: var(--nui-space-200);
		--nui-body-bold-font-family: var(--core-font-family-primary);
		--nui-body-bold-font-size: var(--nui-font-size-350);
		--nui-body-bold-font-weight: var(--nui-font-weight-12500);
		--nui-body-bold-letter-spacing: var(--nui-font-letter-spacing-018);
		--nui-body-bold-line-height: var(--nui-font-line-height-525);
		--nui-body-regular-font-family: var(--core-font-family-primary);
		--nui-body-regular-font-size: var(--nui-font-size-350);
		--nui-body-regular-font-weight: var(--nui-font-weight-10000);
		--nui-body-regular-letter-spacing: var(--nui-font-letter-spacing-018);
		--nui-body-regular-line-height: var(--nui-font-line-height-525);
		--nui-breadcrumbs-item-radius-crumb: var(--nui-border-radius-full);
		--nui-breadcrumbs-item-space-gap: var(--nui-space-100);
		--nui-breadcrumbs-item-space-padding-horizontal-crumb: var(--nui-space-300);
		--nui-breadcrumbs-item-space-padding-vertical-crumb: var(--nui-space-200);
		--nui-breadcrumbs-space-gap: var(--nui-space-100);
		--nui-button-group-border: var(--nui-border-width-medium);
		--nui-button-group-item-border: var(--nui-border-width-small);
		--nui-button-group-item-height: var(--nui-height-medium);
		--nui-button-group-item-radius: var(--nui-border-radius-small);
		--nui-button-group-item-space-padding-horizontal-icon: var(--core-numeric-300);
		--nui-button-group-item-space-padding-horizontal-text: var(--core-numeric-400);
		--nui-button-group-radius: var(--nui-border-radius-small);
		--nui-button-group-space-gap: var(--nui-space-050);
		--nui-button-height: var(--nui-height-medium);
		--nui-button-radius: var(--nui-border-radius-medium);
		--nui-button-space-gap: var(--nui-space-200);
		--nui-button-space-padding-focused: var(--nui-border-width-small);
		--nui-button-space-padding-horizontal: var(--nui-space-400);
		--nui-calendar-month-border: var(--nui-border-width-small);
		--nui-calendar-month-date-border: var(--nui-border-width-tiny);
		--nui-calendar-month-date-height: 84px;
		--nui-calendar-month-date-space-gap: var(--nui-space-050);
		--nui-calendar-month-date-space-padding-horizontal: var(--nui-space-100);
		--nui-calendar-month-date-space-padding-vertical: var(--nui-space-100);
		--nui-calendar-month-date-width: 84px;
		--nui-calendar-month-event-radius: var(--nui-border-radius-small);
		--nui-calendar-month-event-space-gap-indicators: var(--nui-space-200);
		--nui-calendar-month-event-space-padding-horizontal: var(--nui-space-100);
		--nui-calendar-month-event-space-padding-horizontal-indicators: var(--nui-space-200);
		--nui-calendar-month-event-space-padding-vertical: var(--nui-space-050);
		--nui-calendar-month-radius: var(--nui-border-radius-medium);
		--nui-calendar-month-space-padding-right-week-number: var(--nui-space-050);
		--nui-calendar-schedule-item-space-gap: var(--nui-space-100);
		--nui-calendar-schedule-item-space-padding-horizontal-icon: var(--nui-space-200);
		--nui-calendar-schedule-item-space-padding-left: var(--nui-space-300);
		--nui-calendar-schedule-item-space-padding-vertical: var(--nui-space-200);
		--nui-card-border: var(--nui-border-width-small);
		--nui-card-radius: var(--nui-border-radius-large);
		--nui-card-space-padding-horizontal: var(--nui-space-400);
		--nui-card-space-padding-vertical: var(--nui-space-400);
		--nui-chat-border: var(--nui-border-width-small);
		--nui-chat-emoji-border: var(--nui-border-width-small);
		--nui-chat-emoji-radius: var(--nui-border-radius-medium);
		--nui-chat-emoji-space-gap: var(--nui-space-100);
		--nui-chat-emoji-space-gap-emojis: var(--nui-space-400);
		--nui-chat-emoji-space-padding: var(--nui-space-300);
		--nui-chat-head-space-gap-left: var(--nui-space-200);
		--nui-chat-head-space-padding-right: var(--nui-space-200);
		--nui-chat-input-space-gap-active: var(--nui-space-300);
		--nui-chat-input-space-gap-input: var(--nui-space-300);
		--nui-chat-input-space-gap-reply: var(--nui-space-100);
		--nui-chat-input-space-gap-send-actions: var(--nui-space-300);
		--nui-chat-input-space-padding-horizontal-new-comment: var(--nui-space-300);
		--nui-chat-input-space-padding-vertical-new-comment: var(--nui-space-400);
		--nui-chat-message-space-gap: var(--nui-space-400);
		--nui-chat-message-space-gap-actions: var(--nui-space-300);
		--nui-chat-message-space-gap-message: var(--nui-space-050);
		--nui-chat-message-space-gap-row: var(--nui-space-300);
		--nui-chat-message-space-gap-sender: var(--nui-space-100);
		--nui-chat-radius: var(--nui-border-radius-medium);
		--nui-chat-thread-border: var(--nui-border-width-small);
		--nui-chat-thread-radius: var(--nui-border-radius-medium);
		--nui-chat-thread-space-gap: var(--nui-space-500);
		--nui-chat-thread-space-padding: var(--nui-space-400);
		--nui-chat-width: 320px;
		--nui-checkbox-list-space-gap: 0px;
		--nui-checkbox-space-gap: var(--nui-space-200);
		--nui-chips-border: var(--nui-border-width-tiny);
		--nui-chips-height: var(--nui-height-tiny);
		--nui-chips-radius: var(--nui-border-radius-tiny);
		--nui-chips-space-padding-horizontal: var(--nui-space-200);
		--nui-chips-space-padding-vertical: var(--nui-space-050);
		--nui-dialog-border: var(--nui-border-width-small);
		--nui-dialog-radius: var(--nui-border-radius-large);
		--nui-dialog-space-gap: var(--nui-space-600);
		--nui-dialog-space-gap-content: var(--nui-space-100);
		--nui-dialog-space-gap-main-actions: var(--nui-space-400);
		--nui-dialog-space-padding-horizontal: var(--nui-space-800);
		--nui-dialog-space-padding-vertical: var(--nui-space-800);
		--nui-dialog-width: 600px;
		--nui-divider-space-padding-vertical: var(--nui-space-100);
		--nui-forms-height: var(--nui-height-medium);
		--nui-forms-help-space-padding-left: var(--nui-space-100);
		--nui-forms-help-space-padding-top: var(--nui-space-100);
		--nui-forms-label-space-padding-bottom: var(--nui-space-100);
		--nui-forms-label-space-padding-left: var(--nui-space-050);
		--nui-forms-radius: var(--nui-border-radius-small);
		--nui-forms-space-gap: var(--nui-space-200);
		--nui-forms-space-padding-horizontal: var(--nui-space-300);
		--nui-forms-space-padding-left-items: var(--nui-space-200);
		--nui-forms-space-secondary-gap: var(--nui-space-100);
		--nui-grid-item-header-space-gap: 0px;
		--nui-grid-item-radius: 0px;
		--nui-grid-item-space-gap: 0px;
		--nui-grid-item-space-padding-horizontal: 0px;
		--nui-grid-item-space-padding-vertical: 0px;
		--nui-grid-list-space-gap: 0px;
		--nui-header-space-gap-actions: var(--nui-space-300);
		--nui-header-space-gap-breadcrumbs: var(--nui-space-400);
		--nui-header-space-gap-dropdown: var(--nui-space-300);
		--nui-header-space-gap-right: var(--nui-space-400);
		--nui-header-space-padding-horizontal: var(--nui-space-500);
		--nui-header-space-padding-vertical: var(--nui-space-300);
		--nui-heading-font-family: var(--core-font-family-primary);
		--nui-heading-font-size: var(--nui-font-size-800);
		--nui-heading-font-weight: var(--nui-font-weight-15000);
		--nui-heading-letter-spacing: var(--nui-font-letter-spacing-040);
		--nui-heading-line-height: var(--nui-font-line-height-1000);
		--nui-icon-font-icon-size: var(--core-numeric-600);
		--nui-icon-height: var(--core-numeric-600);
		--nui-icon-width: var(--core-numeric-600);
		--nui-label-bold-font-family: var(--core-font-family-primary);
		--nui-label-bold-font-size: var(--nui-font-size-350);
		--nui-label-bold-font-weight: var(--nui-font-weight-12500);
		--nui-label-bold-letter-spacing: var(--nui-font-letter-spacing-018);
		--nui-label-bold-line-height: var(--nui-font-line-height-500);
		--nui-label-regular-font-family: var(--core-font-family-primary);
		--nui-label-regular-font-size: var(--nui-font-size-350);
		--nui-label-regular-font-weight: var(--nui-font-weight-10000);
		--nui-label-regular-letter-spacing: var(--nui-font-letter-spacing-018);
		--nui-label-regular-line-height: var(--nui-font-line-height-500);
		--nui-label-space-gap: var(--nui-space-200);
		--nui-list-item-border: var(--nui-border-width-small);
		--nui-list-item-radius: var(--nui-border-radius-small);
		--nui-list-item-rich-space-gap: var(--nui-space-200);
		--nui-list-item-rich-space-gap-actions: var(--nui-space-100);
		--nui-list-item-rich-space-gap-left: var(--nui-space-300);
		--nui-list-item-rich-space-no-background-gap-left-text: var(--nui-space-050);
		--nui-list-item-rich-space-no-background-padding-horizontal: var(--nui-space-200);
		--nui-list-item-rich-space-no-background-padding-vertical: var(--nui-space-200);
		--nui-list-item-rich-space-with-background-gap-left-text: var(--nui-space-100);
		--nui-list-item-rich-space-with-background-padding-horizontal: var(--nui-space-400);
		--nui-list-item-rich-space-with-background-padding-vertical: var(--nui-space-400);
		--nui-list-item-simple-height: var(--nui-height-small);
		--nui-list-item-simple-radius: var(--nui-border-radius-medium);
		--nui-list-item-simple-space-gap: var(--nui-space-200);
		--nui-list-item-simple-space-padding-horizontal: var(--nui-space-300);
		--nui-list-item-simple-space-padding-vertical: var(--nui-space-100);
		--nui-main-navigation-item-height: var(--nui-height-medium);
		--nui-main-navigation-item-radius: var(--nui-border-radius-medium);
		--nui-main-navigation-item-space-padding-horizontal-collapsed: var(--nui-space-200);
		--nui-main-navigation-item-space-padding-horizontal-expanded: var(--nui-space-300);
		--nui-main-navigation-item-space-padding-vertical-collapsed: var(--nui-space-200);
		--nui-main-navigation-item-space-padding-vertical-expanded: var(--nui-space-200);
		--nui-main-navigation-space-gap-top: var(--nui-space-400);
		--nui-main-navigation-space-padding-horizontal: var(--nui-space-500);
		--nui-main-navigation-space-padding-vertical: var(--nui-space-500);
		--nui-menu-item-input-radius: var(--nui-border-radius-tiny);
		--nui-menu-item-radius: var(--nui-space-000);
		--nui-menu-item-space-gap-option: var(--nui-space-200);
		--nui-menu-item-space-gap-shortcut: var(--nui-space-200);
		--nui-menu-item-space-label-padding-bottom: var(--nui-space-100);
		--nui-menu-item-space-label-padding-top: var(--nui-space-200);
		--nui-menu-item-space-padding-horizontal: var(--nui-space-300);
		--nui-menu-item-space-secondary-padding-horizontal: var(--nui-space-300);
		--nui-menu-min-width: var(--nui-min-width-large);
		--nui-menu-radius: var(--nui-border-radius-medium);
		--nui-menu-space-padding-horizontal: var(--nui-space-000);
		--nui-menu-space-padding-vertical: var(--nui-space-000);
		--nui-paginator-space-gap-page: var(--nui-space-300);
		--nui-paginator-space-gap-show: var(--nui-space-300);
		--nui-paginator-space-padding-horizontal: var(--nui-space-400);
		--nui-paginator-space-padding-vertical: var(--nui-space-400);
		--nui-pill-radius: var(--nui-border-radius-small);
		--nui-pill-space-padding-horizontal: var(--nui-space-100);
		--nui-pill-space-padding-vertical: var(--nui-space-000);
		--nui-progress-bar-item-height: var(--nui-space-300);
		--nui-progress-bar-item-radius: var(--nui-border-radius-huge);
		--nui-radio-button-list-space-gap: 0px;
		--nui-range-slider-handle-height: 20px;
		--nui-range-slider-handle-width: 20px;
		--nui-range-space-gap: var(--nui-space-400);
		--nui-range-space-padding-horizontal: var(--nui-space-200);
		--nui-range-space-padding-vertical: var(--nui-space-200);
		--nui-scroll-radius: var(--nui-border-radius-full);
		--nui-scroll-space-padding-horizontal: var(--nui-space-050);
		--nui-scroll-space-padding-vertical: var(--nui-space-050);
		--nui-scroll-width: var(--nui-space-200);
		--nui-snackbar-border: var(--nui-border-width-small);
		--nui-snackbar-radius: var(--nui-border-radius-small);
		--nui-snackbar-space-gap: var(--nui-space-200);
		--nui-snackbar-space-padding-horizontal: var(--nui-space-300);
		--nui-snackbar-space-padding-vertical: var(--nui-space-200);
		--nui-stepper-item-icon-border: var(--nui-border-width-small);
		--nui-stepper-item-icon-height: var(--nui-height-small);
		--nui-stepper-item-icon-radius: var(--nui-border-radius-full);
		--nui-stepper-item-icon-width: var(--nui-height-small);
		--nui-stepper-item-space-gap: var(--nui-space-200);
		--nui-stepper-space-gap: var(--nui-space-200);
		--nui-table-item-gap: var(--nui-space-200);
		--nui-table-item-height: var(--nui-space-1200);
		--nui-table-item-padding-horizontal: var(--nui-space-400);
		--nui-table-row-height: var(--nui-space-1200);
		--nui-table-row-space-gap: var(--nui-space-050);
		--nui-table-space-padding-horizontal: var(--nui-space-000);
		--nui-table-space-padding-vertical: var(--nui-space-000);
		--nui-tabs-height: var(--nui-height-medium);
		--nui-tabs-item-height: var(--nui-height-medium);
		--nui-tabs-item-radius: var(--nui-border-radius-medium);
		--nui-tabs-item-space-padding-horizontal-icon: var(--nui-space-200);
		--nui-tabs-item-space-padding-horizontal-text: var(--nui-space-400);
		--nui-tabs-radius: var(--nui-border-radius-medium);
		--nui-tabs-space-padding-horizontal: var(--nui-space-400);
		--nui-title-font-family: var(--core-font-family-primary);
		--nui-title-font-size: var(--nui-font-size-350);
		--nui-title-font-weight: var(--nui-font-weight-15000);
		--nui-title-letter-spacing: var(--nui-font-letter-spacing-018);
		--nui-title-line-height: var(--nui-font-line-height-500);
		--nui-toggle-item-height: var(--nui-space-400);
		--nui-toggle-item-radius: var(--nui-border-radius-full);
		--nui-toggle-item-space-padding-horizontal: var(--nui-space-050);
		--nui-toggle-item-space-padding-vertical: var(--nui-space-050);
		--nui-toggle-item-width: var(--nui-space-400);
		--nui-toggle-space-gap: var(--nui-space-200);
		--nui-toggle-space-width: var(--nui-space-1000);
		--nui-tooltip-radius: var(--nui-border-radius-tiny);
		--nui-tooltip-space-padding-horizontal: var(--nui-space-200);
		--nui-tooltip-space-padding-vertical: var(--nui-space-100);
	}
	[data-uisize="lg"], &[data-uisize="lg"] {
		--nui-heading-font-family: var(--core-font-family-primary);
		--nui-heading-font-size: var(--nui-font-size-1000);
		--nui-heading-font-weight: var(--nui-font-weight-15000);
		--nui-heading-letter-spacing: var(--nui-font-letter-spacing-048);
		--nui-heading-line-height: var(--nui-font-line-height-1200);
		--nui-title-font-family: var(--core-font-family-primary);
		--nui-title-font-size: var(--nui-font-size-400);
		--nui-title-font-weight: var(--nui-font-weight-15000);
		--nui-title-letter-spacing: var(--nui-font-letter-spacing-020);
		--nui-title-line-height: var(--nui-font-line-height-500);
	}
}