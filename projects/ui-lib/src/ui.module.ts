import { OverlayModule } from '@angular/cdk/overlay';
import { NgModule } from '@angular/core';
import {
    UIAccordionComponent,
    UIAccordionHeaderTemplateDirective,
    UIAccordionItemComponent,
    UIAlertDialogComponent,
    UIAvatarComponent,
    UIBodyComponent,
    UIBreadcrumbComponent,
    UIBreadcrumbsComponent,
    UIButtonComponent,
    UIButtonGroupComponent,
    UIButtonGroupItemComponent,
    UICheckboxComponent,
    UIChipComponent,
    UIConfirmDialogComponent,
    UIDatePickerComponent,
    UIDialogButtonsComponent,
    UIDialogComponent,
    UIDialogDirective,
    UIDialogTemplateDirective,
    UIDropdownComponent,
    UIDropdownDividerComponent,
    UIDropdownInputComponent,
    UIDropdownItemComponent,
    UIDropdownTargetDirective,
    UIErrorDialogComponent,
    UIFileDropzoneComponent,
    UIFlagComponent,
    UIHeaderComponent,
    UIHelpMenuComponent,
    UIIconComponent,
    UIIframeComponent,
    UIImageComponent,
    UIInlineEditComponent,
    UIInlineLoaderComponent,
    UIInputComponent,
    UILabelComponent,
    UILinkComponent,
    UILinkDirective,
    UIListCellComponent,
    UIListCellTemplateDirective,
    UIListColumnDirective,
    UIListComponent,
    UIListFolderCellComponent,
    UIListFolderTemplateDirective,
    UIListGridCellComponent,
    UIListGridDirective,
    UIListHeaderCellComponent,
    UIListHeaderComponent,
    UIListHeaderTemplateDirective,
    UILoaderComponent,
    UILogoComponent,
    UIMenuGroupComponent,
    UIMenuItemComponent,
    UIMenuSidePanelComponent,
    UINotificationComponent,
    UINumberInputComponent,
    UIOptionComponent,
    UIPaginatorComponent,
    UIPanelComponent,
    UIPopoverComponent,
    UIPopoverDirective,
    UIPopoverTargetDirective,
    UIPopoverTemplateDirective,
    UIRadioComponent,
    UIRangeComponent,
    UISelectableBaseDirective,
    UISelectableDirective,
    UISelectableListComponent,
    UISelectComponent,
    UISelectLabelDirective,
    UISelectTokenFieldTemplateDirective,
    UISkeletonCellComponent,
    UIStickyNotificationComponent,
    UISVGIconComponent,
    UISvgIconsComponent,
    UITabComponent,
    UITableComponent,
    UITabsComponent,
    UITextareaComponent,
    UIToggleSwitchComponent,
    UITooltipComponent,
    UITooltipDirective,
    UISearchInputComponent
} from './components';
import {
    UIComponentSizeDirective,
    UIDragDropDirective,
    UIDraggableDirective,
    UIDropZoneDirective,
    UIInViewDirective,
    UIPreventClickDirective
} from './directives';
import { UIFileExtensionPipe, UIFormatBytesPipe, UIOrdinalPipe } from './pipes';
import { UIAdBlockerDetectionService } from './services';

@NgModule({
    imports: [
        OverlayModule,

        UIAccordionComponent,
        UIAccordionHeaderTemplateDirective,
        UIAccordionItemComponent,
        UIAlertDialogComponent,
        UIAvatarComponent,
        UIBodyComponent,
        UIBreadcrumbComponent,
        UIBreadcrumbsComponent,
        UIButtonComponent,
        UIButtonGroupComponent,
        UIButtonGroupItemComponent,
        UICheckboxComponent,
        UIChipComponent,
        UIComponentSizeDirective,
        UIConfirmDialogComponent,
        UIDatePickerComponent,
        UIDialogButtonsComponent,
        UIDialogComponent,
        UIDialogDirective,
        UIDialogTemplateDirective,
        UIDragDropDirective,
        UIDraggableDirective,
        UIDropZoneDirective,
        UIDropdownComponent,
        UIDropdownDividerComponent,
        UIDropdownInputComponent,
        UIDropdownItemComponent,
        UIDropdownTargetDirective,
        UIErrorDialogComponent,
        UIFileDropzoneComponent,
        UIFileExtensionPipe,
        UIFlagComponent,
        UIFormatBytesPipe,
        UIHeaderComponent,
        UIHelpMenuComponent,
        UIIconComponent,
        UIIframeComponent,
        UIImageComponent,
        UIInViewDirective,
        UIInlineEditComponent,
        UIInlineLoaderComponent,
        UIInputComponent,
        UILabelComponent,
        UILinkComponent,
        UILinkDirective,
        UIListCellComponent,
        UIListCellTemplateDirective,
        UIListColumnDirective,
        UIListComponent,
        UIListFolderCellComponent,
        UIListFolderTemplateDirective,
        UIListGridCellComponent,
        UIListGridDirective,
        UIListHeaderCellComponent,
        UIListHeaderComponent,
        UIListHeaderTemplateDirective,
        UILoaderComponent,
        UILogoComponent,
        UIMenuGroupComponent,
        UIMenuItemComponent,
        UIMenuSidePanelComponent,
        UINotificationComponent,
        UINumberInputComponent,
        UIOptionComponent,
        UIOrdinalPipe,
        UIPaginatorComponent,
        UIPanelComponent,
        UIPopoverComponent,
        UIPopoverDirective,
        UIPopoverTargetDirective,
        UIPopoverTemplateDirective,
        UIPreventClickDirective,
        UIRadioComponent,
        UIRangeComponent,
        UISVGIconComponent,
        UISelectComponent,
        UISelectLabelDirective,
        UISelectTokenFieldTemplateDirective,
        UISelectableBaseDirective,
        UISelectableDirective,
        UISelectableListComponent,
        UISearchInputComponent,
        UISkeletonCellComponent,
        UIStickyNotificationComponent,
        UISvgIconsComponent,
        UITabComponent,
        UITableComponent,
        UITabsComponent,
        UITextareaComponent,
        UIToggleSwitchComponent,
        UITooltipComponent,
        UITooltipDirective
    ],
    exports: [
        UIAccordionComponent,
        UIAccordionHeaderTemplateDirective,
        UIAccordionItemComponent,
        UIAlertDialogComponent,
        UIAvatarComponent,
        UIBodyComponent,
        UIBreadcrumbComponent,
        UIBreadcrumbsComponent,
        UIButtonComponent,
        UIButtonGroupComponent,
        UIButtonGroupItemComponent,
        UICheckboxComponent,
        UIChipComponent,
        UIComponentSizeDirective,
        UIConfirmDialogComponent,
        UIDatePickerComponent,
        UIDialogButtonsComponent,
        UIDialogComponent,
        UIDialogDirective,
        UIDialogTemplateDirective,
        UIDragDropDirective,
        UIDraggableDirective,
        UIDropZoneDirective,
        UIDropdownComponent,
        UIDropdownDividerComponent,
        UIDropdownInputComponent,
        UIDropdownItemComponent,
        UIDropdownTargetDirective,
        UIErrorDialogComponent,
        UIFileDropzoneComponent,
        UIFileExtensionPipe,
        UIFlagComponent,
        UIFormatBytesPipe,
        UIHeaderComponent,
        UIHelpMenuComponent,
        UIIconComponent,
        UIIframeComponent,
        UIImageComponent,
        UIInViewDirective,
        UIInlineEditComponent,
        UIInlineLoaderComponent,
        UIInputComponent,
        UILabelComponent,
        UILinkComponent,
        UILinkDirective,
        UIListCellComponent,
        UIListCellTemplateDirective,
        UIListColumnDirective,
        UIListComponent,
        UIListFolderCellComponent,
        UIListFolderTemplateDirective,
        UIListGridCellComponent,
        UIListGridDirective,
        UIListHeaderCellComponent,
        UIListHeaderComponent,
        UIListHeaderTemplateDirective,
        UILoaderComponent,
        UILogoComponent,
        UIMenuGroupComponent,
        UIMenuItemComponent,
        UIMenuSidePanelComponent,
        UINotificationComponent,
        UINumberInputComponent,
        UIOptionComponent,
        UIOrdinalPipe,
        UIPaginatorComponent,
        UIPanelComponent,
        UIPopoverComponent,
        UIPopoverDirective,
        UIPopoverTargetDirective,
        UIPopoverTemplateDirective,
        UIPreventClickDirective,
        UIRadioComponent,
        UIRangeComponent,
        UISVGIconComponent,
        UISelectComponent,
        UISelectLabelDirective,
        UISelectTokenFieldTemplateDirective,
        UISelectableBaseDirective,
        UISelectableDirective,
        UISelectableListComponent,
        UISearchInputComponent,
        UISkeletonCellComponent,
        UIStickyNotificationComponent,
        UISvgIconsComponent,
        UITabComponent,
        UITableComponent,
        UITabsComponent,
        UITextareaComponent,
        UIToggleSwitchComponent,
        UITooltipComponent,
        UITooltipDirective
    ],
    providers: [UIAdBlockerDetectionService, UIFormatBytesPipe]
})
export class UIModule {}
