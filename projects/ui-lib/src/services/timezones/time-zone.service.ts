import { Injectable } from '@angular/core';
import { UiTimeZone } from '../../types';
import { timeZones } from './time-zones';
import { timeZonesDts } from './time-zones-dts';

@Injectable({ providedIn: 'root' })
export class UiTimeZoneService {
    public getTimeZones(time = new Date()): UiTimeZone[] {
        return this.isDaylightSavingTime(time) ? timeZonesDts : timeZones;
    }

    private isDaylightSavingTime(time: Date): boolean {
        const dtsTimeOffset = new Date(time.getFullYear(), 6, 1).getTimezoneOffset();
        return time.getTimezoneOffset() === dtsTimeOffset;
    }
}
