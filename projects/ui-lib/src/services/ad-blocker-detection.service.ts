import { HttpBackend, HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, map, Observable, of, tap } from 'rxjs';
import { UINotificationService } from '../components';

@Injectable({ providedIn: 'root' })
export class UIAdBlockerDetectionService {
    private httpBackend = inject(HttpBackend);
    private httpClient = new HttpClient(this.httpBackend);
    private uiNotificationService = inject(UINotificationService);

    private readonly TEST_URL_PROD =
        'https://c.bannerflow.net/io/api/image/optimize?u=https%3A%2F%2Fbfstudio.blob.core.windows.net%2Faccounts%2Fbannerflow-enterprise%2F5daeecfa0bbf0503b0ebf6fe%2Fimages%2F902389bd-ec16-4cb4-bf48-68d5c2eabc20.jpeg&w=250&h=250&q=85&f=webp&rt=contain';

    // Checks if an AdBlocker is active
    // @returns {boolean}: true if an AdBlocker was found
    checkIfAdBlockerEnabled(): Observable<boolean> {
        const url = this.TEST_URL_PROD;
        return this.httpClient
            .get(url, {
                responseType: 'arraybuffer',
                observe: 'response'
            })
            .pipe(
                catchError(() => of({ status: 0 })),
                map(response => response.status === 0)
            );
    }

    // Checks and notifies if an AdBlocker is active
    // @returns {boolean}: true if an AdBlocker was found
    checkAndNotifiy(): Observable<boolean> {
        return this.checkIfAdBlockerEnabled().pipe(
            tap(blockerFound => {
                if (blockerFound) {
                    this.uiNotificationService.open(
                        'It seems like you are using ad blocker.\n This can impact the experience within the platform. We strongly recommend you to disable it.',
                        {
                            placement: 'top',
                            type: 'warning'
                        }
                    );
                }
            })
        );
    }
}
