import { effect, Injectable, signal } from '@angular/core';
import { Observable, Subject } from 'rxjs';

// ! Careful: All these values need to exist in `../style/generated/figma-vars.scss`
export const DATASET_THEME = 'uitheme';
export const DATASET_FLAG = 'uinew';
export const DATASET_SIZE = 'uisize';

export enum Themes {
    Light = 'light',
    Dark = 'dark'
}

const THEME_STORAGE_KEY = 'BF-UI-CURRENT_THEME';

/**
 * This service takes care of two things:
 * * Themes: light and dark
 *
 * Themes are general and they are always set on the root element (`document.documentElement`)
 * Themes are saved in localStorage using `BF-UI-CURRENT_THEME` as key
 *
 * In most of the cases, the consumer would want to set the Environment on the root element
 *
 * Note: another way to change the style for components is to use sizes, every component will
 * implement their own set of sizes.
 * */
@Injectable({ providedIn: 'root' })
export class UINewThemeService {
    currentTheme = signal(Themes.Light);

    private _isNewThemeEnabled = signal(false);
    isNewThemeEnabled = this._isNewThemeEnabled.asReadonly();

    constructor() {
        effect(() => this.saveTheme());
        effect(() => this.applyThemeChange());

        this.updateCurrentTheme();

        this.getRootMutationObserver().subscribe(mutationRecord => {
            const elementAttributeValue = (mutationRecord.target as HTMLElement)?.dataset[DATASET_FLAG];
            const isNewThemeEnabled = this.isNewThemeEnabled();
            if (!!elementAttributeValue === !!isNewThemeEnabled) {
                return;
            }
            this._isNewThemeEnabled.set(!!elementAttributeValue);
        });
    }

    enableNewTheme(): void {
        document.documentElement.dataset[DATASET_FLAG] = 'true';
        this._isNewThemeEnabled.set(true);
    }

    disableNewTheme(): void {
        delete document.documentElement.dataset[DATASET_FLAG];
        this._isNewThemeEnabled.set(false);
    }

    toggleTheme(): void {
        this.currentTheme.update(currentTheme =>
            currentTheme === Themes.Dark ? Themes.Light : Themes.Dark
        );
    }

    private applyThemeChange(): void {
        const currentTheme = this.currentTheme();
        document.documentElement.dataset[DATASET_THEME] = currentTheme;
    }

    private saveTheme(): void {
        const currentTheme = this.currentTheme();
        if (currentTheme === Themes.Dark || currentTheme === Themes.Light) {
            localStorage.setItem(THEME_STORAGE_KEY, currentTheme);
            return;
        }
        localStorage.setItem(THEME_STORAGE_KEY, Themes.Light);
    }

    private updateCurrentTheme(): void {
        const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme === Themes.Dark || savedTheme === Themes.Light) {
            this.currentTheme.set(savedTheme);
            return;
        }
        this.currentTheme.set(Themes.Light);
    }

    private getRootMutationObserver(): Observable<MutationRecord> {
        const subject = new Subject<MutationRecord>();
        const rootElement = document.documentElement;
        const observer = new MutationObserver(mutations => {
            for (const mutation of mutations) {
                if (mutation.type === 'attributes') {
                    subject.next(mutation);
                }
            }
        });

        observer.observe(rootElement, {
            attributes: true // configure it to listen to attribute changes
        });

        return subject.asObservable();
    }
}
