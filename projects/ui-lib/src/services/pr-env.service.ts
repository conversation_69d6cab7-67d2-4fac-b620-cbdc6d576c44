import { httpResource } from '@angular/common/http';
import { computed, inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { PREnvToken, RepoAndPRsDto } from '../types/pr-env';

export const QUERY_PARAM_PRS = 'prEnvs';
export const QUERY_PARAM_SEPARATOR = '+';
// Example: 'https://pr-envs-manager.bannerflow.workers.dev/api/pr-envs?repos=["frontend-template","backend-template-service"]'
const API_URL = 'https://pr-envs-manager.bannerflow.workers.dev/api/pr-envs';

@Injectable()
export class UIPREnvService {
    private readonly activatedRoute = inject(ActivatedRoute);
    private readonly router = inject(Router);

    configs = inject(PREnvToken);

    private queryParamsSignal = toSignal(this.activatedRoute.queryParamMap);

    selectedPrEnvs = computed<[string, string][]>(() => {
        const queryParams = this.queryParamsSignal();
        const fromParams: string[] = queryParams?.get(QUERY_PARAM_PRS)?.split(',') ?? [];
        const newValue: [string, string][] = fromParams.map(repoBranch => {
            const [repo, branch] = repoBranch.split(QUERY_PARAM_SEPARATOR);
            return [repo, branch];
        });
        return newValue;
    });

    prEnvs = httpResource<RepoAndPRsDto>(() => {
        const configs = this.configs;
        if (!configs.beRepos.length && !configs.feRepos.length) {
            return;
        }

        const repoNames = [
            ...configs.beRepos.map(({ repoName }) => repoName),
            ...configs.feRepos.map(({ repoName }) => repoName)
        ];
        return {
            url: API_URL,
            headers: { 'X-Api-key': 'please' },
            params: { repos: JSON.stringify(repoNames) }
        };
    });

    deselectPrEnv(repo: string): void {
        const queryParams = this.queryParamsSignal();
        const currentQueryParam = queryParams?.get(QUERY_PARAM_PRS) ?? '';

        const fromParams: string[] = currentQueryParam.split(',');
        const newParam = fromParams
            .filter(param => !param.startsWith(repo + QUERY_PARAM_SEPARATOR))
            .join(',');

        if (currentQueryParam !== newParam) {
            this.navigateTo(newParam);
        }
    }

    selectPrEnv(repo: string, branch: string): void {
        const formatted = `${repo}${QUERY_PARAM_SEPARATOR}${branch}`;

        const prEnvs = this.prEnvs.value();

        if (!prEnvs?.success) {
            return;
        }

        const branchInfo = prEnvs.data[repo]?.find(({ formattedName }) => formattedName === branch);

        const queryParams = this.queryParamsSignal();
        const currentQueryParam = queryParams?.get(QUERY_PARAM_PRS) ?? '';
        const fromParams: string[] = currentQueryParam.split(',');
        const newParam = [
            ...fromParams.filter(param => !param.startsWith(repo + QUERY_PARAM_SEPARATOR)),
            formatted
        ].join(',');

        if (currentQueryParam !== newParam) {
            this.navigateTo(newParam).then(() => {
                const skipRedirect = !!this.configs.feRepos.find(({ repoName }) => repoName === repo)
                    ?.skipRedirection;
                if (
                    !skipRedirect &&
                    !!branchInfo?.previewUrl &&
                    !branchInfo.previewUrl.includes(window.location.hostname)
                ) {
                    // Redirect to previewUrl
                    window.location.hostname =
                        branchInfo?.previewUrl?.replace('https://', '') ?? window.location.hostname;
                }
            });
        }
    }

    private navigateTo(queryParamForPrEnvs: string): Promise<boolean> {
        const queryParams: Params = {
            ...this.activatedRoute.snapshot.queryParams,
            [QUERY_PARAM_PRS]: queryParamForPrEnvs
        };

        return this.router.navigate([], {
            relativeTo: this.activatedRoute,
            queryParams
        });
    }
}
