import { ArgTypes } from '@storybook/angular';

/**
 * Provides correct tag, and sets the nui to enabled by default
 */
export const NUI_STORY_SETTINGS = {
    tags: ['Nui'],
    globals: {
        rootAttributes: {
            'data-uinew': 'true'
        }
    }
};

/**
 * Provides control for 'md'|'sm'|'xs' variants
 */
export const NUI_STORY_ARG_TYPE_SIZE: ArgTypes<{ size: 'xs' | 'sm' | 'md' }> = {
    size: {
        control: 'inline-radio',
        options: ['xs', 'sm', 'md']
    }
};
/**
 * Provides control for 'md'|'sm' variants
 */
export const NUI_STORY_ARG_TYPE_SIZE2: ArgTypes<{ variant: 'sm' | 'md' }> = {
    variant: {
        ...NUI_STORY_ARG_TYPE_SIZE.size,
        options: ['md', 'sm']
    }
};
