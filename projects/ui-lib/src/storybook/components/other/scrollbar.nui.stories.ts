import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { NUI_STORY_SETTINGS } from '../../constants';
import { UIComponentSizeDirective } from '../../../directives';

const meta: Meta = {
    ...NUI_STORY_SETTINGS,
    title: 'NUI/Extras/Scrollbar',
    decorators: [
        moduleMetadata({
            imports: [UIComponentSizeDirective]
        })
    ],
    parameters: {
        layout: 'centered'
    }
};

export default meta;
type Story = StoryObj;

export const Default: Story = {
    args: {},
    render: () => ({
        template: `
        <style>
        h6 {
            margin-bottom: 16px;
        }
        </style>

        <h6 [uiSize]="'md'">Medium Scrollbar</h6>
        <div
            class="ui-scrollbar"
            data-uisize="md"
            style="max-width: 500px; max-height: 200px; overflow: auto;"
        >
            <div
                style="width: 700px; max-height: 400px; overflow: auto;"
            >
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer accumsan nec arcu vel aliquet. Nulla consectetur lacus quis ex sollicitudin, vitae pharetra sapien vestibulum. Suspendisse iaculis magna non mauris laoreet, id lacinia urna pharetra. Donec ultricies nisl ac enim scelerisque, eget malesuada urna laoreet. In eu nulla sed nisl mollis rutrum. Aenean consequat non purus rutrum interdum. Pellentesque dapibus quam ex, ac fermentum ante vehicula sit amet. Aenean at tellus at diam vehicula feugiat sed eget urna. Vivamus commodo arcu commodo nisl porta rutrum.

                Aenean tempor, ipsum tincidunt gravida iaculis, lorem erat molestie odio, id iaculis odio dui id neque. Donec egestas enim quis aliquam pellentesque. Ut dapibus turpis sapien, at tempor nulla dictum vel. Donec consectetur lorem in risus ultricies dapibus. Nunc id leo ultrices metus convallis varius. Curabitur consectetur justo eget tortor euismod rutrum. Donec venenatis imperdiet ex id vestibulum. Lorem ipsum dolor sit amet, consectetur adipiscing elit.

                Donec hendrerit non mi sed condimentum. Nullam eget elit eu dui feugiat tempor vel et enim. Sed mi magna, luctus sit amet purus eu, cursus interdum libero. Ut vitae mi et nisl venenatis aliquam nec eget nisl. Curabitur sed est arcu. Nulla ac posuere nulla, et ultricies neque. Donec sollicitudin urna luctus iaculis vulputate. Integer auctor maximus enim, non fringilla erat finibus ac. Suspendisse potenti. Vestibulum leo libero, aliquet et tincidunt at, cursus nec erat.

                Cras scelerisque lectus mi, ut eleifend dolor vestibulum molestie. Donec rutrum mi in arcu sollicitudin, id posuere lectus interdum. Suspendisse euismod condimentum ex eu gravida. Mauris consequat sem vitae convallis feugiat. Etiam eget aliquet ex, eu consequat mi. Sed facilisis ex dui, ut elementum massa porttitor in. Nulla facilisi. Suspendisse sagittis finibus dui, non placerat erat ultrices in. Nulla sit amet sapien cursus, lacinia nisl et, molestie metus. Phasellus vitae euismod diam, eu aliquam mi. Praesent sit amet lobortis lectus, sit amet mattis urna. Quisque vel velit ultricies, egestas orci a, condimentum nulla. Mauris ut consectetur nisi. Nam vehicula sed arcu non suscipit.

                Duis tincidunt faucibus sagittis. Aliquam ac nulla mauris. Aenean pulvinar lectus pellentesque ex mollis, eu maximus lacus viverra. Lorem ipsum dolor sit amet, consectetur adipiscing elit. In pharetra nisi leo, non blandit arcu volutpat a. Ut lacinia tristique velit vel semper. Mauris a aliquam magna, vel luctus leo. Phasellus eleifend egestas nulla, sed molestie risus pulvinar at. Curabitur egestas lacus justo. Quisque non neque elementum, vehicula justo at, dictum arcu. Ut et dapibus dui. Aliquam erat volutpat. Aliquam erat volutpat. Sed turpis elit, tincidunt sit amet enim sed, pellentesque cursus nisi.

                Aenean eu fermentum dui. Praesent ut diam ac tortor tincidunt faucibus. Nunc ultrices eget velit eget fringilla. Etiam iaculis, lorem id tincidunt fermentum, orci nisi interdum risus, vel pretium justo felis eget eros. Etiam porttitor ipsum massa, eget tristique orci placerat in. Fusce magna metus, commodo at elementum vitae, maximus at arcu. Duis faucibus vitae erat ut varius. Vivamus et nisl vitae ante accumsan imperdiet. Morbi sagittis dapibus tortor, dapibus vestibulum massa.
            </div>
        </div>

        <h6 [uiSize]="'md'">Small Scrollbar</h6>
        <div
            class="ui-scrollbar"
            data-uisize="sm"
            style="max-width: 500px; max-height: 200px; overflow: auto;"
        >
            <div
                style="width: 700px; max-height: 400px; overflow: auto;"
            >
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer accumsan nec arcu vel aliquet. Nulla consectetur lacus quis ex sollicitudin, vitae pharetra sapien vestibulum. Suspendisse iaculis magna non mauris laoreet, id lacinia urna pharetra. Donec ultricies nisl ac enim scelerisque, eget malesuada urna laoreet. In eu nulla sed nisl mollis rutrum. Aenean consequat non purus rutrum interdum. Pellentesque dapibus quam ex, ac fermentum ante vehicula sit amet. Aenean at tellus at diam vehicula feugiat sed eget urna. Vivamus commodo arcu commodo nisl porta rutrum.

                Aenean tempor, ipsum tincidunt gravida iaculis, lorem erat molestie odio, id iaculis odio dui id neque. Donec egestas enim quis aliquam pellentesque. Ut dapibus turpis sapien, at tempor nulla dictum vel. Donec consectetur lorem in risus ultricies dapibus. Nunc id leo ultrices metus convallis varius. Curabitur consectetur justo eget tortor euismod rutrum. Donec venenatis imperdiet ex id vestibulum. Lorem ipsum dolor sit amet, consectetur adipiscing elit.

                Donec hendrerit non mi sed condimentum. Nullam eget elit eu dui feugiat tempor vel et enim. Sed mi magna, luctus sit amet purus eu, cursus interdum libero. Ut vitae mi et nisl venenatis aliquam nec eget nisl. Curabitur sed est arcu. Nulla ac posuere nulla, et ultricies neque. Donec sollicitudin urna luctus iaculis vulputate. Integer auctor maximus enim, non fringilla erat finibus ac. Suspendisse potenti. Vestibulum leo libero, aliquet et tincidunt at, cursus nec erat.

                Cras scelerisque lectus mi, ut eleifend dolor vestibulum molestie. Donec rutrum mi in arcu sollicitudin, id posuere lectus interdum. Suspendisse euismod condimentum ex eu gravida. Mauris consequat sem vitae convallis feugiat. Etiam eget aliquet ex, eu consequat mi. Sed facilisis ex dui, ut elementum massa porttitor in. Nulla facilisi. Suspendisse sagittis finibus dui, non placerat erat ultrices in. Nulla sit amet sapien cursus, lacinia nisl et, molestie metus. Phasellus vitae euismod diam, eu aliquam mi. Praesent sit amet lobortis lectus, sit amet mattis urna. Quisque vel velit ultricies, egestas orci a, condimentum nulla. Mauris ut consectetur nisi. Nam vehicula sed arcu non suscipit.

                Duis tincidunt faucibus sagittis. Aliquam ac nulla mauris. Aenean pulvinar lectus pellentesque ex mollis, eu maximus lacus viverra. Lorem ipsum dolor sit amet, consectetur adipiscing elit. In pharetra nisi leo, non blandit arcu volutpat a. Ut lacinia tristique velit vel semper. Mauris a aliquam magna, vel luctus leo. Phasellus eleifend egestas nulla, sed molestie risus pulvinar at. Curabitur egestas lacus justo. Quisque non neque elementum, vehicula justo at, dictum arcu. Ut et dapibus dui. Aliquam erat volutpat. Aliquam erat volutpat. Sed turpis elit, tincidunt sit amet enim sed, pellentesque cursus nisi.

                Aenean eu fermentum dui. Praesent ut diam ac tortor tincidunt faucibus. Nunc ultrices eget velit eget fringilla. Etiam iaculis, lorem id tincidunt fermentum, orci nisi interdum risus, vel pretium justo felis eget eros. Etiam porttitor ipsum massa, eget tristique orci placerat in. Fusce magna metus, commodo at elementum vitae, maximus at arcu. Duis faucibus vitae erat ut varius. Vivamus et nisl vitae ante accumsan imperdiet. Morbi sagittis dapibus tortor, dapibus vestibulum massa.
            </div>
        </div>
    `
    })
};
