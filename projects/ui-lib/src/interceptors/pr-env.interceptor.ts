import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { QUERY_PARAM_PRS, QUERY_PARAM_SEPARATOR } from '../services/pr-env.service';
import { PREnvToken } from '../types/pr-env';

const baseSandboxUrl = 'https://sandbox-api.bannerflow.com';

export function uiPREnvInterceptor(
    req: HttpRequest<unknown>,
    next: HttpHandlerFn
): Observable<HttpEvent<unknown>> {
    const activatedRoute = inject(ActivatedRoute);
    const params = new URLSearchParams(window.location.search);

    const paramsFromQuery =
        params?.get(QUERY_PARAM_PRS) ?? activatedRoute.snapshot.queryParamMap.get(QUERY_PARAM_PRS);
    if (!req.url.includes(baseSandboxUrl) || !paramsFromQuery) {
        return next(req);
    }

    const fromParams: string[] = paramsFromQuery.split(',');
    const selectedPrEnvs: [string, string][] = fromParams.map(repoBranch => {
        const [repo, branch] = repoBranch.split(QUERY_PARAM_SEPARATOR);
        return [repo, branch];
    });

    if (!selectedPrEnvs.length) {
        return next(req);
    }

    const repos = inject(PREnvToken).beRepos;

    for (const [repo, branch] of selectedPrEnvs) {
        const path = repos.find(({ repoName }) => repoName === repo)?.path;

        if (!path || !req.url.includes(`.com/${path}`)) {
            continue;
        }

        const updatedUrl = req.url.replace(`.com/${path}`, `.com/${branch}/${path}`);

        if (req.url !== updatedUrl) {
            const modifiedRequest = req.clone({ url: updatedUrl });
            return next(modifiedRequest);
        }
    }

    return next(req);
}
