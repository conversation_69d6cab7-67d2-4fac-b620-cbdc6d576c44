import { APP_INITIALIZER, Injector, ApplicationConfig } from '@angular/core';
import type { ICollection } from '@storybook/angular/dist/client/types.js';
import { makeDecorator } from 'storybook/internal/preview-api';

// ! Used by Storybook only
export const injectInjectorToProps = makeDecorator({
    name: 'injectInjectorToProps',
    parameterName: 'injectInjectorToProps',
    skipIfNoParametersOrOptions: true,
    wrapper: (getStory, context) => {
        const story = getStory(context) as { props: ICollection; applicationConfig: ApplicationConfig };

        if (!story.applicationConfig) {
            story.applicationConfig = { providers: [] };
        }

        story.applicationConfig.providers.push({
            provide: APP_INITIALIZER,
            useFactory: (injector: Injector): void => {
                if (!story.props) {
                    story.props = { injector };
                }
                Object.assign(story.props, { injector });
            },
            deps: [Injector]
        });
        return story;
    }
});
