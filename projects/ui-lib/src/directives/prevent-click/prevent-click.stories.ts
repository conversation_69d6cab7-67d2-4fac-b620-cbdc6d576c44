import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UIButtonComponent } from '../../components';
import { UIPreventClickDirective } from './prevent-click.directive';

const meta: Meta<UIPreventClickDirective> = {
    title: 'Directives/PreventClick',
    component: UIPreventClickDirective,
    decorators: [
        moduleMetadata({
            imports: [UIButtonComponent]
        })
    ]
};
export default meta;

type Story = StoryObj<UIPreventClickDirective>;

export const Default: Story = {
    render: args => ({
        ...args,
        props: {
            ...args,
            alert: (message: string): void => {
                window.alert(message);
            }
        },
        template: `
<ui-button text="Clickable" (click)="alert('Click not prevented')" />
<ui-button [ui-prevent-click] text="Not clickable" (click)="alert('Click not prevented')" />
`
    })
};
