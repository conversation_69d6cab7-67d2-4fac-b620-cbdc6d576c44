import { Directive, input } from '@angular/core';
import { DATASET_SIZE } from '../../services/uinew-theme.service';

/**
 * Meant to be used on HTML tags. It will set the internals for size
 *
 * Usage:
 * ```html
 *   <span [uiSize]="'xs'">My awesome text</span>
 * ````
 */
@Directive({
    standalone: true,
    selector: '[uiSize]',
    host: {
        [`[attr.data-${DATASET_SIZE}]`]: 'uiSize()'
    }
})
export class UIComponentSizeDirective {
    uiSize = input<'xs' | 'sm' | 'md' | 'lg'>('md');
}
