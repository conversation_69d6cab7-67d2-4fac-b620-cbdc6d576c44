import { Directive, computed, input } from '@angular/core';

type TypographyVariant = 'nui-regular' | 'nui-bold';

type TypographyType = 'nui-heading' | 'nui-title' | 'nui-body';

@Directive({
    standalone: true,
    selector: '[nuiTypography]',
    host: {
        '[class]': 'uiTypography()',
        '[class.nui-bold]': 'isBold()'
    }
})
export class UITypographyDirective {
    nuiTypography = input<TypographyType>('nui-body');
    variant = input<TypographyVariant>('nui-regular');

    protected isBold = computed(() => this.variant() === 'nui-bold');
    protected uiTypography = computed(() => this.nuiTypography());
}
