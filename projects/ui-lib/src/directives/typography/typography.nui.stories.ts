import { <PERSON>a, StoryObj } from '@storybook/angular';
import { applicationConfig, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { UIComponentSizeDirective } from '../component-size/component-size.directive';
import { UITypographyDirective } from './typography.directive';

const meta: Meta<UITypographyDirective> = {
    title: 'NUI/Typography',
    decorators: [
        applicationConfig({
            providers: []
        }),
        moduleMetadata({
            imports: [CommonModule, UIComponentSizeDirective, UITypographyDirective]
        })
    ],
    parameters: {
        docs: {
            description: {
                component: `
The \`nuiTypography\` directive provides a consistent way to apply typography styles throughout the application.

## Features

- **Types**: \`nui-heading\`, \`nui-title\`, \`nui-body\`
- **Sizing**: Works with the existing \`uiSize\` directive
- **Variants**: \`nui-bold\`

## Installation

Import the directive in your module:

\`\`\`typescript
import { UITypographyDirective } from '@bannerflow/ui';

@Component({
  imports: [UITypographyDirective]
})
export class YourComponent { }
\`\`\`

### Size Handling

- **Heading Sizes** (h1-h4): ld, md, sm, xs
- **Title Sizes** (h5, h6, p): lg, md, sm
- **Body Sizes** (p, small): md, sm
- **Variant** (strong, b): nui-bold

Invalid sizes will fall back to 'md'.

## Best Practices

1. Always use semantic HTML elements (h1-h6, p, span)
2. Combine with \`uiSize\` directive for consistent sizing

## Usage

### Basic Usage

\`\`\`html
<!-- Heading -->
<h1 [uiSize]="'lg'">Large Heading</h1>
<h2 [uiSize]="'md'">Medium Heading</h2>
<h3 [uiSize]="'sm'">Small Heading</h3>
<h4 [uiSize]="'xs'">Extra Small Heading</h4>
<!-- or -->
<div [nuiTypography]="'nui-heading'" [uiSize]="'lg'">Large Heading</div>
<section [nuiTypography]="'nui-heading'" [uiSize]="'md'">Medium Heading</section>
<caption [nuiTypography]="'nui-heading'" [uiSize]="'sm'">Small Heading</caption>
<details [nuiTypography]="'nui-heading'" [uiSize]="'xs'">Extra Small Heading</details>

<!-- Title -->
<h5 [uiSize]="'lg'">Large Title</h5>
<h6 [uiSize]="'md'">Medium Title</h6>
<p [nuiTypography]="'nui-title'" [uiSize]="'sm'">Small Title</p>

<!-- or for God's sake, if you insist on being a div developer -->
<div [nuiTypography]="'nui-title'" [uiSize]="'xs'">Small Title</div>


<!-- Body Text -->
<p>Regular body text</p>
<p [uiSize]="'sm'">Small body text</p>
<!-- or -->
<article [nuiTypography]="'nui-body'" [uiSize]="'md'">Regular body text</article>
\`\`\`

### With Variants

\`\`\`html
<!-- Bold Text -->
<b>Bold Text</b>
<strong>Bold Text</strong>
<!-- or -->
<abbr [nuiTypography]="'nui-body'" [uiSize]="'sm'" [variant]="'nui-bold'">Bold Text</abbr>
\`\`\`


        `
            }
        }
    },
    argTypes: {
        nuiTypography: {
            control: 'select',
            options: ['nui-heading', 'nui-title', 'nui-body'],
            description: 'The typography type'
        },
        variant: {
            control: 'select',
            options: ['nui-bold'],
            description: 'Text variant'
        }
    }
};

export default meta;

type Story = StoryObj<UITypographyDirective>;

export const Playground: Story = {
    args: {
        nuiTypography: 'nui-body',
        variant: 'nui-bold'
    },

    argTypes: {
        nuiTypography: {
            control: 'select',
            options: ['nui-heading', 'nui-title', 'nui-body'],
            description: 'The typography type'
        },
        variant: {
            control: 'select',
            options: ['nui-regular', 'nui-bold'],
            description: 'Text variant'
        }
    },

    render: args => ({
        args,
        props: args,
        template: `
            <div [nuiTypography]="nuiTypography" [uiSize]="md" [variant]="variant">
                Change the controls to see different typography styles
            </div>
        `
    })
};
