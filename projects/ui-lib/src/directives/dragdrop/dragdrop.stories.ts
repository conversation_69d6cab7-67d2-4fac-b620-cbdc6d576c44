import { OverlayModule } from '@angular/cdk/overlay';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata } from '@storybook/angular';
import type { Meta, StoryObj } from '@storybook/angular/';
import { UIDragDropDirective } from './dragdrop.directive';
import { UIDragdropService } from './dragdrop.service';
import { UIDropDirective, UIDropEvent } from './drop.directive';

const meta: Meta<UIDragDropDirective<string>> = {
    title: 'Directives/DragDrop/DragDrop',
    component: UIDragDropDirective,
    decorators: [
        moduleMetadata({
            providers: [UIDragdropService],
            imports: [UIDragDropDirective, BrowserAnimationsModule, OverlayModule, UIDropDirective]
        })
    ]
};
export default meta;

type CustomTemplate = {
    isAllowedToDrop: (dropTarget: string, draggedItems: string[]) => boolean;
    itemsDropped: (dropEvent: UIDropEvent<string>) => void;
};
type Story = StoryObj<UIDragDropDirective<string> | CustomTemplate>;

export const DragDrop: Story = {
    args: {},
    render: args => {
        function itemsDropped(dropEvent: UIDropEvent<string>): void {
            alert(`Dropped ${dropEvent.droppedItems} on droptarget ${dropEvent.dropTarget}`);
        }

        function isAllowedToDrop(dropTarget: string, draggedItems: string[]): boolean {
            // eslint-disable-next-line no-console
            console.log('draggedItems', draggedItems);
            // eslint-disable-next-line no-console
            console.log('dropTarget', dropTarget);
            return draggedItems.find(item => item === dropTarget) ? false : true;
        }

        return {
            props: {
                ...args,
                isAllowedToDrop,
                itemsDropped
            },
            template: `
            <div class="wrapper">
                <div class="container" uiDragDrop
                    [dragData]="['2']"
                    [dropData]="'1'"
                    (itemsDropped)="itemsDropped($event)"
                    [isAllowedToDrop]="isAllowedToDrop">drag and drop on me</div>
                <div uiDrop [isAllowedToDrop]="isAllowedToDrop" (itemsDropped)="itemsDropped($event)" [dropData]="'1'" class="container">drop on me</div>
                <div uiDrop [isAllowedToDrop]="isAllowedToDrop" (itemsDropped)="itemsDropped($event)" [dropData]="'2'" class="container">you cannot drop on me</div>
            </div>
            `,
            styles: [
                `
                .wrapper {
                    margin: 50px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;

                }
                .container {
                    text-align: center;
                    width: 100px;
                    height: 100px;
                    background-color: lightblue;
                }
                .ui-dropzone-allowed {
                    background-color: green;
                }
                .ui-dropzone-not-allowed {
                    background-color: red;
                }
                `
            ]
        };
    }
};
