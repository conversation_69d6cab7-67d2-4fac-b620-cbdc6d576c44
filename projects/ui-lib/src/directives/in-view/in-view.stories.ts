import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { UIButtonComponent } from '../../components';
import { UIInViewDirective } from './in-view.directive';

const meta: Meta<UIInViewDirective> = {
    title: 'Directives/InView',
    component: UIInViewDirective,
    decorators: [
        moduleMetadata({
            imports: [UIButtonComponent]
        })
    ]
};
export default meta;

type Story = StoryObj<UIInViewDirective>;

export const Default: Story = {
    render: args => ({
        ...args,
        template: `
<style>
.wrapper{
    background-color:red;
    width: 2000px;
}
</style>
<div #wrapper class="wrapper">
    <ui-button
        [text]="'Scoll me'"
        [ui-in-view]="0.5"
        (ui-in-view-enter)="wrapper.style.backgroundColor='green'"
        (ui-in-view-leave)="wrapper.style.backgroundColor='red'"
    />
</div>
<span> When button is 50% in view, background will become green. Background will be red, otherwise </span>
`
    })
};
