const charToHTMLEntity: { [index: string]: string } = {
    '&': '&amp;',
    '"': '&quot;',
    '\\': '&#39;',
    '<': '&lt;',
    '>': '&gt;'
};

export function encodeXml(text: string): string {
    for (const i in charToHTMLEntity) {
        if (i === '\\') {
            text = text.replace(new RegExp('\\\\', 'g'), charToHTMLEntity[i]);
        } else {
            text = text.replace(new RegExp(i, 'g'), charToHTMLEntity[i]);
        }
    }

    return text;
}
