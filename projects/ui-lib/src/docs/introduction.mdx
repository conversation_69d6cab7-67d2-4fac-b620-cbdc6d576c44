import { <PERSON><PERSON>, <PERSON> } from '@storybook/addon-docs/blocks';

<Meta title="Introduction" />

# Bannerflow UI

## Purpose of UI

To create a reliable and highly reusable UI library to use in every app we create. The library should be fast to modify and easy to use. The UX department should have responsibility over its look.

## When to add a component to UI?

### Add it if it meets these requirements:

1. Your component will be used in multiple apps **according to sketches or the UX team.**
2. The component is **unique** and doesn't exist as something similar. In cases where there's overlap, it may be better to add options to an existing component or create a component in the app where you need it.
3. The component is well thought through and designed to work in several different use cases.
4. The component works with our theming.
5. The component has passed code review and is performant.

### Other reasons to add a component

1. A component in UI really needs this new component to work.
2. Other less common reasons that may still qualify your component.

### What about directives, pipes, helper classes, and functions?

The same rules apply as with components!

## How to add a component to Bannerflow UI?

- Start by adding it to the app where it's most needed.
- Check out [<PERSON>' UI board](https://bannerflow.atlassian.net/secure/RapidBoard.jspa?rapidView=50&projectKey=UI) for related tickets.
- Reach out in the [#frontend-devs Slack channel](https://bannerflow.slack.com/messages/C6NN76GLS) for guidance.
- Explore the [GitHub repo](https://github.com/nordicfactory) for any similar components.
- Discuss your idea with a colleague, especially if they've built components in this library before.
- Submit a pull request! For more information, see the [Bannerflow UI wiki on pull requests](https://wikiflow.atlassian.net/wiki/spaces/DEV/pages/894599169/Bannerflow+UI).

## Setting up the project

See the [bannerflow-ui repository](https://github.com/nordicfactory/bannerflow-ui) for instructions on setting up and building the project.
