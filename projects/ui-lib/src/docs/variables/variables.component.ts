import { AfterViewInit, Component, computed, input } from '@angular/core';
import { UISVGIconComponent } from '../../components';
import { UIComponentSizeDirective } from '../../directives';

type DisplayType = 'all' | 'general' | 'colors';

interface Variable {
    name: string;
    value: string;
    resolvedValue?: string;
    resolutionPath?: string[];
    imageSrc?: string;
}

@Component({
    imports: [UISVGIconComponent, UIComponentSizeDirective],
    selector: 'variables',
    templateUrl: './variables.component.html',
    styleUrls: ['./variables.component.scss']
})
export class VariablesComponent implements AfterViewInit {
    displayType = input<DisplayType>('all');
    displayGeneral = computed(() => this.displayType() === 'general' || this.displayType() === 'all');
    displayColors = computed(() => this.displayType() === 'colors' || this.displayType() === 'all');

    generalVariables: Variable[] = [];
    colorVariables: Variable[] = [];
    staticColors: string[] = [];

    copiedVariable: string | null = null;

    ngAfterViewInit(): void {
        const styleSheets = Array.from(document.styleSheets) as CSSStyleSheet[];
        styleSheets.forEach(sheet => {
            if (this.isLocalSheet(sheet)) {
                this.extractVariablesFromSheet(sheet);
            }
        });
    }

    copyVariable(name: string): void {
        navigator.clipboard.writeText(name).then(() => {
            this.copiedVariable = name;

            setTimeout(() => {
                this.copiedVariable = null;
            }, 300);
        });
    }

    private isLocalSheet(sheet: CSSStyleSheet): boolean {
        return !sheet.href || sheet.href.includes(document.location.origin);
    }

    private extractVariablesFromSheet(sheet: CSSStyleSheet): void {
        try {
            Array.from(sheet.cssRules).forEach(rule => {
                if (this.isRootRule(rule)) {
                    this.extractVariablesFromRule(rule as CSSStyleRule);
                }
            });
        } catch (error) {
            console.warn('Could not access stylesheet:', error);
        }
    }

    private isRootRule(rule: CSSRule): boolean {
        return (rule as CSSStyleRule).selectorText?.includes(':root');
    }

    private extractVariablesFromRule(rule: CSSStyleRule): void {
        Array.from(rule.style).forEach(name => {
            const initialValue = rule.style.getPropertyValue(name).trim();
            const { resolvedValue, resolutionPath } = this.resolveVariable(name, initialValue);
            if (this.isCustomVariable(name) && !this.isExcludedVariable(name)) {
                this.categorizeVariable(name, initialValue, resolvedValue, resolutionPath);
            }
        });
    }

    private isCustomVariable(name: string): boolean {
        return name.startsWith('--');
    }

    private isExcludedVariable(name: string): boolean {
        return name.includes('--ui-static') || name.includes('--default');
    }

    private categorizeVariable(
        name: string,
        initialValue: string,
        resolvedValue: string,
        resolutionPath: string[]
    ): void {
        const variable: Variable = { name, value: initialValue, resolvedValue, resolutionPath };
        if (this.isBase64Image(resolvedValue)) {
            variable.imageSrc = this.getImageSrcFromBase64(resolvedValue);
        }
        if (this.isColor(resolvedValue)) {
            this.colorVariables.push(variable);
        } else {
            this.generalVariables.push(variable);
        }
    }

    private isBase64Image(value: string): boolean {
        return value.startsWith('url("data:image/') && value.includes('base64,');
    }

    private getImageSrcFromBase64(value: string): string | undefined {
        const match = value.match(
            /url\(\s*['"]?(data:image\/[a-zA-Z0-9+.-]+;base64,[a-zA-Z0-9+/=]+)['"]?\s*\)/
        );
        return match ? match[1] : undefined;
    }

    private isColor(value: string): boolean {
        const nonColorKeywords = ['inherit', 'initial', 'unset', 'none', 'auto'];
        if (value.includes('var(') || nonColorKeywords.includes(value)) {
            return false;
        }
        if (CSS.supports('color', value)) {
            const s = new Option().style;
            s.color = value;
            return s.color !== '' && !nonColorKeywords.includes(s.color);
        }
        return false;
    }

    private resolveVariable(
        _name: string,
        value: string,
        path: string[] = []
    ): { resolvedValue: string; resolutionPath: string[] } {
        const variableMatch = value.match(/^var\((--[a-zA-Z0-9-_]+)\)$/);
        if (!variableMatch) {
            return { resolvedValue: value, resolutionPath: [...path, value] };
        }

        const referencedVariable = variableMatch[1];
        const resolvedValue = window
            .getComputedStyle(document.documentElement)
            .getPropertyValue(referencedVariable)
            .trim();

        if (!resolvedValue) {
            return {
                resolvedValue: value,
                resolutionPath: [...path, `${referencedVariable}`]
            };
        }
        if (path.includes(referencedVariable)) {
            return {
                resolvedValue: value,
                resolutionPath: [...path, `Circular: ${referencedVariable}`]
            };
        }
        return this.resolveVariable(referencedVariable, resolvedValue, [...path, referencedVariable]);
    }
}
