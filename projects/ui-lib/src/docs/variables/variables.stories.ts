import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { VariablesComponent } from './variables.component';

const meta: Meta<VariablesComponent> = {
    title: 'Customization/Variables',
    component: VariablesComponent,
    tags: ['!autodocs'],
    decorators: [
        moduleMetadata({
            imports: [VariablesComponent]
        })
    ],
    parameters: {
        chromatic: { disableSnapshot: true } 
    }
};

export default meta;
type Story = StoryObj<VariablesComponent>;

export const General: Story = {
    args: {
        displayType: 'general'
    }
};

export const Colors: Story = {
    args: {
        displayType: 'colors'
    }
};