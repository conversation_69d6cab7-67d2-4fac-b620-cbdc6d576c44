import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { VariablesComponent } from './variables.component';

const meta: Meta<VariablesComponent> = {
    title: 'Customization/Variables',
    component: VariablesComponent,
    tags: ['!autodocs'],
    decorators: [
        moduleMetadata({
            imports: [VariablesComponent]
        })
    ],
    parameters: {
        // Disables Chromatic's snapshotting on a component level
        chromatic: { disableSnapshot: true },
    },
};

export default meta;

type Story = StoryObj<VariablesComponent>;

export const All: Story = {
    args: {
        displayType: 'all'
    }
};
