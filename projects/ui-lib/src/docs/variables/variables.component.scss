.variable-list {
    list-style-type: none;
    padding: 0;
    margin: 0 0 2rem 0;

    li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
}

.color-grid,
.variable-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--ui-padding-small);
    margin-bottom: 2rem;
}

.color-box,
.variable-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 6px;
    overflow: hidden;
    text-align: center;
    font-size: 0.95rem;
    padding: 1rem;
    background-color: var(--ui-color-surface);
    box-shadow: var(--ui-box-shadow);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    position: relative;

    &.copied {
        background-color: var(--ui-color-selected-background);
    }

    &:hover {
        transform: scale(1.05);
    }

    .copy-icon-container {
        position: absolute;
        top: 8px;
        right: 8px;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
    }

    &:hover .copy-icon-container {
        opacity: 1;
    }

    .copy-icon {
        transition: transform 0.2s ease-in-out;
    }

    &.copied .copy-icon {
        transform: scale(1.2);
    }

    .clickable {
        position: relative;
    }

    &:hover .color-sample .overlay {
        opacity: 1;
    }
}

.color-sample {
    position: relative;
    width: 100%;
    height: 60px;
    border: var(--ui-border);
    border-radius: 6px;
    overflow: hidden;
    transition: transform 0.3s ease;

    .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.4);
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
        pointer-events: none;

        .copy-icon {
            color: var(--ui-color-text-inverted);
            font-size: 2rem;
        }
    }
}

.color-info {
    padding: 0.5rem 0;
}

.color-info,
.variable-box {
    strong {
        display: block;
        font-weight: 600;
        margin-bottom: 0.2rem;
    }

    .resolution-path {
        font-size: 0.8rem;
        color: #555;
        text-align: center;
    }
}

.variable-image {
    max-width: 100%;
    max-height: 80px;
    border-radius: 6px;
    padding: 1rem;
    background-color: var(--ui-color-background);
    display: block;
    margin: auto;
    object-fit: contain;
}
