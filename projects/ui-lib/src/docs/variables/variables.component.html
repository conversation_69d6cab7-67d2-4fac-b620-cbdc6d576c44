@if (displayGeneral()) {
    <h3 [uiSize]="'sm'">General Variables</h3>
    <div class="variable-grid">
        @for (variable of generalVariables; track variable) {
            <div
                class="variable-box"
                [class.copied]="copiedVariable === variable.name"
                (click)="copyVariable(variable.name)">
                <div class="copy-icon-container">
                    <ui-svg-icon
                        icon="copy"
                        class="copy-icon" />
                </div>
                <strong class="clickable">{{ variable.name }}</strong>
                @if (variable.imageSrc) {
                    <img
                        [src]="variable.imageSrc"
                        alt="{{ variable.name }}"
                        class="variable-image" />
                } @else {
                    <div class="resolution-path">
                        @for (step of variable.resolutionPath; track step) {
                            {{ step }}
                            <br />
                        }
                    </div>
                }
            </div>
        }
    </div>
}

@if (displayColors()) {
    <h3 [uiSize]="'sm'">Color Variables</h3>
    <div class="color-grid">
        @for (variable of colorVariables; track variable) {
            <div
                class="color-box"
                [class.copied]="copiedVariable === variable.name"
                (click)="copyVariable(variable.name)">
                <div
                    class="color-sample"
                    [style.backgroundColor]="variable.resolvedValue">
                    <div class="overlay">
                        <ui-svg-icon
                            icon="copy"
                            class="copy-icon" />
                    </div>
                </div>
                <div class="color-info">
                    <strong>{{ variable.name }}</strong>
                    <div class="resolution-path">
                        @for (step of variable.resolutionPath; track step) {
                            {{ step }}
                            <br />
                        }
                    </div>
                    @if (variable.imageSrc) {
                        <img
                            [src]="variable.imageSrc"
                            alt="{{ variable.name }}"
                            class="variable-image" />
                    }
                </div>
            </div>
        }
    </div>
}
