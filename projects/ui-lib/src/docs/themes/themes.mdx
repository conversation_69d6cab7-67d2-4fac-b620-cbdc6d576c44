import { <PERSON>a, <PERSON> } from '@storybook/addon-docs/blocks';
import * as ThemeStories from './themes.stories';

<Meta of={ThemeStories} />

# UI Themes

The `ui-theme` attribute allows you to apply different themes across components. You can use it directly on elements or wrap a group of elements in a container with `ui-theme` to apply the theme consistently within that context.

**Available themes:**

- **default**
- **small**
- **minimal**
- **tiny**
- **dark** *- Not well supported yet*

---

### Button Theme Variants

Below are examples of the different theme options as applied to buttons. Notice how each theme affects the button's size and style.

<Story of={ThemeStories.ButtonExample} />

**Example usage:**

```html
<ui-button text="Default" type="primary" />
<ui-button text="Small" type="primary" ui-theme="small" />
<ui-button text="Minimal" type="primary" ui-theme="minimal" />
<ui-button text="Tiny" type="primary" ui-theme="tiny" />
```

---

### Parent Theme Example

For components that break the DOM hierarchy, such as `<ui-select>`, it's necessary to apply the `theme` input directly on the component to ensure it inherits the correct theme.

<Story of={ThemeStories.SelectExample} />

**Example usage:**

```html
<ui-select theme="minimal">
    <ui-option value="one">Option One</ui-option>
    <ui-option value="two">Option Two</ui-option>
    <ui-option value="three">Option Three</ui-option>
</ui-select>
```
