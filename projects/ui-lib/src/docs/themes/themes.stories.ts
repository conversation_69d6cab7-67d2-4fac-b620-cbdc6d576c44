import { CommonModule } from '@angular/common';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { UIButtonComponent, UISelectComponent, UIOptionComponent } from '../../components';

const meta: Meta = {
    title: 'Customization/Themes',
    decorators: [
        moduleMetadata({
            imports: [CommonModule, UIButtonComponent, UISelectComponent, UIOptionComponent]
        })
    ]
};

export default meta;

type Story = StoryObj;

export const ButtonExample: Story = {
    render: () => ({
        template: `
            <div style="display: flex; flex-direction: column; gap: 0.5rem; align-items: flex-start; max-width: fit-content;">
                <ui-button text="Default" type="primary" />
                <ui-button text="Small" type="primary" ui-theme="small" />
                <ui-button text="Minimal" type="primary" ui-theme="minimal" />
                <ui-button text="Tiny" type="primary" ui-theme="tiny" />
            </div>
        `
    })
};

export const SelectExample: Story = {
    render: () => ({
        template: `
            <ui-select theme="minimal">
                <ui-option value="one">Option One</ui-option>
                <ui-option value="two">Option Two</ui-option>
                <ui-option value="three">Option Three</ui-option>
            </ui-select>
        `
    })
};
