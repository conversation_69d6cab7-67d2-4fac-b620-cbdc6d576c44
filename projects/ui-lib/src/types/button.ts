export interface UISubmitResponse<T = undefined> {
    error?: any;
    state?: T;
}

export type OldButtonType = 'primary' | 'default' | 'discrete';
export type NewButtonType =
    | 'solid-primary'
    | 'solid-secondary'
    | 'solid-primary-destructive'
    | 'solid-secondary-destructive'
    | 'ghost-primary'
    | 'ghost-secondary'
    | 'ghost-primary-destructive'
    | 'plain-primary'
    | 'plain-secondary'
    | 'plain-primary-destructive';

export type UIButtonType = OldButtonType | NewButtonType;
