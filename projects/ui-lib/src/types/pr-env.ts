import { InjectionToken } from '@angular/core';

export interface PullRequestItem {
    name: string;
    formattedName: string;
    previewUrl?: string;
}

export type RepoAndPRs = Record<string, PullRequestItem[]>;
export type RepoAndPRsDto =
    | {
          success: true;
          data: RepoAndPRs;
      }
    | {
          success: false;
          error: string;
      };

export interface UIPrEnvBERepo {
    repoName: string; // Repo name
    path: string; // subpath for services .com/{path}/api
}

export interface UIPrEnvFERepo {
    repoName: string; // Repo name
    skipRedirection?: boolean; // false by default
}

export type PREnvTokenConfig = { beRepos: UIPrEnvBERepo[]; feRepos: UIPrEnvFERepo[] };
export const PREnvToken = new InjectionToken<PREnvTokenConfig>('ui-pr-env');
