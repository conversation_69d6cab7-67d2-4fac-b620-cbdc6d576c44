import { ConnectedPosition } from '@angular/cdk/overlay';
import { UITheme } from './theme';

export type xPosition = 'start' | 'center' | 'end';
export type yPosition = 'top' | 'center' | 'bottom';
export type arrowPosition = undefined | 'top' | 'left' | 'bottom' | 'right';
export interface IPositions extends ConnectedPosition {}

export interface IUIPopoverConfig {
    /**
     * * 'ui-dropdown' used for dropdown menus
     * * 'no-padding' (Old ui) removes padding
     * * 'ui-select' (NUI) used for select component
     * * 'ui-tooltip' (NUI) used for tooltips
     * * 'ui-card-popover' (NUI) looks like a card
     * * 'transparent' (NUI) remvoes background
     */
    panelClass?: string | string[];
    hasBackdrop?: boolean;
    backdropClass?: string;
    useTargetWidth?: boolean;
    width?: number | string;
    height?: number | string;
    maxWidth?: number | string;
    maxHeight?: number | string;
    minWidth?: number | string;
    backdropClickClose?: boolean;
    closeButton?: boolean;
    escKeyClose?: boolean;
    offset?: Offset;
    positions?: IPositions[];
    arrowPosition?: arrowPosition;
    position?: 'top' | 'right' | 'bottom' | 'left';
    theme?: UITheme;
    popoverType?: string;
    data?: any;
    backgroundColor?: string;
    openOnHover?: boolean;
    uiTooltipInteractive?: boolean;
    type?: 'primary' | 'secondary';
    size?: 'xs' | 'sm' | 'md';
    invalid?: boolean;
}

export interface Offset {
    x: number;
    y: number;
}
