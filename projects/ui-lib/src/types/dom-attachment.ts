import { ComponentPortal, DomPortalOutlet } from '@angular/cdk/portal';
import { ComponentRef, EmbeddedViewRef } from '@angular/core';

export interface IUIAttachedDOMItemBase {
    domPortalHost: DomPortalOutlet;
}

export interface IUIAttachedDOMComponent<T> extends IUIAttachedDOMItemBase {
    instance: T;
    componentPortal: ComponentPortal<T>;
    componentRef: ComponentRef<T>;
}

export interface IUIAttachedDOMTemplate extends IUIAttachedDOMItemBase {
    embeddedViewRef: EmbeddedViewRef<any>;
}

export type IUIAttachedDOMItem = IUIAttachedDOMComponent<any> | IUIAttachedDOMTemplate;
